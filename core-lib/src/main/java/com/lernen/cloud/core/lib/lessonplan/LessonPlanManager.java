package com.lernen.cloud.core.lib.lessonplan;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.timetable.StaffStandardEntityDetailsRow;
import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardRowDetails;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.lessonplan.ChapterTopicDetails;
import com.lernen.cloud.core.api.lessonplan.ChapterTopicPayload;
import com.lernen.cloud.core.api.lessonplan.LessonPlanChapterDetails;
import com.lernen.cloud.core.api.lessonplan.LessonPlanChapterPayload;
import com.lernen.cloud.core.api.lessonplan.LessonPlanClonePayload;
import com.lernen.cloud.core.api.lessonplan.LessonPlanMetaDataPayload;
import com.lernen.cloud.core.api.lessonplan.LessonPlanMetaDataResponse;
import com.lernen.cloud.core.api.lessonplan.LessonPlanMetaDataWithChapterAndTopicPayload;
import com.lernen.cloud.core.api.lessonplan.LessonPlanStatus;
import com.lernen.cloud.core.api.lessonplan.LessonPlanStatusWithCourseAndStaffDetails;
import com.lernen.cloud.core.api.lessonplan.StaffLessonPlanDetailsPayload;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.LibraryManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.dao.tier.lessonplan.LessonPlanDao;

public class LessonPlanManager {

    private static final Logger logger = LogManager.getLogger(LessonPlanManager.class);

    private final LessonPlanDao lessonPlanDao;
    private final UserPermissionManager userPermissionManager;
    private final InstituteManager instituteManager;
    private final TimetableManager timetableManager;
    private final TransactionTemplate transactionTemplate;

    public LessonPlanManager(LessonPlanDao lessonPlanDao, UserPermissionManager userPermissionManager, InstituteManager instituteManager, TimetableManager timetableManager, TransactionTemplate transactionTemplate) {
        this.lessonPlanDao = lessonPlanDao;
        this.userPermissionManager = userPermissionManager;
        this.instituteManager = instituteManager;
        this.timetableManager = timetableManager;
        this.transactionTemplate = transactionTemplate;
    }

    public boolean lessonPlanFunctionClassification(int instituteId, int academicSessionId, UUID userId, UUID courseId, List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(CollectionUtils.isEmpty(lessonPlanMetaDataChapterTopicPayloadList)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload"));
        }
        List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadAddList = new ArrayList<>();
        Map<UUID, List<LessonPlanChapterPayload>> lessonPlanChapterPayloadAddMap = new HashMap<>();
        Set<UUID> deleteChapterIdSet = new HashSet<>();
        Set<UUID> deleteTopicIdSet = new HashSet<>();
        List<LessonPlanChapterPayload> lessonPlanChapterUpdatePayload = new ArrayList<>();
        Map<UUID, List<ChapterTopicPayload>> chapterTopicPayloadAddMap = new HashMap<>();
        Map<UUID, ChapterTopicPayload> topicIdPayload = new HashMap<>();
        List<ChapterTopicPayload> chapterTopicUpdatePayload = new ArrayList<>();
        for(LessonPlanMetaDataWithChapterAndTopicPayload lessonPlanMetaDataWithChapterAndTopicPayload : lessonPlanMetaDataChapterTopicPayloadList){
            UUID lessonPlanId = lessonPlanMetaDataWithChapterAndTopicPayload.getLessonPlanMetaDataPayload().getLessonPlanId();
            LessonPlanMetaDataPayload lessonPlanMetaDataPayload = lessonPlanMetaDataWithChapterAndTopicPayload.getLessonPlanMetaDataPayload();
            if(lessonPlanId == null){
                lessonPlanMetaDataChapterTopicPayloadAddList.add(lessonPlanMetaDataWithChapterAndTopicPayload);
                continue;
            }
            Map<UUID, LessonPlanChapterPayload> chapterIdMap = getLessonPlanChapterPayloadMap(lessonPlanId, lessonPlanMetaDataWithChapterAndTopicPayload.getLessonPlanChapterPayloadList(), lessonPlanChapterPayloadAddMap, chapterTopicPayloadAddMap, topicIdPayload);
            List<LessonPlanChapterDetails> lessonPlanChapterDetailList = getChapterTopicDetails(lessonPlanId, lessonPlanMetaDataPayload.getLessonPlanStatus());
            for(LessonPlanChapterDetails lessonPlanChapterDetail : lessonPlanChapterDetailList){
                if(chapterIdMap.get(lessonPlanChapterDetail.getChapterId()) == null){
                    deleteChapterIdSet.add(lessonPlanChapterDetail.getChapterId());
                    continue;
                }
                lessonPlanChapterUpdatePayload.add(chapterIdMap.get(lessonPlanChapterDetail.getChapterId()));
                for(ChapterTopicDetails chapterTopicDetails : lessonPlanChapterDetail.getChapterTopicDetails()){
                    if(topicIdPayload.get(chapterTopicDetails.getTopicId()) == null){
                        deleteTopicIdSet.add(chapterTopicDetails.getTopicId());
                        continue;
                    }
                    chapterTopicUpdatePayload.add(topicIdPayload.get(chapterTopicDetails.getTopicId()));
                }
            }        
        }
        boolean addLessonPlan = true;
        if(!CollectionUtils.isEmpty(lessonPlanMetaDataChapterTopicPayloadAddList)){
            addLessonPlan = addLessonPlanWithChapterAndTopic(instituteId, academicSessionId, userId, courseId, lessonPlanMetaDataChapterTopicPayloadAddList);
        }
        if(!addLessonPlan){
            return false;
        }
        return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
                try{
                    if(!MapUtils.isEmpty(lessonPlanChapterPayloadAddMap)){
                        for(Map.Entry<UUID, List<LessonPlanChapterPayload>> entry : lessonPlanChapterPayloadAddMap.entrySet()){
                            UUID lessonPlanId = entry.getKey();
                            List<LessonPlanChapterPayload> lessonPlanChapterPayloadList = entry.getValue();
                            if(lessonPlanId == null){
                                throw new ApplicationException(
                                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Lesson Plan Id Not Found"));
                            }
                            Map<UUID,List<ChapterTopicPayload>> chapterTopicPayloadMap = addLessonPlanChapterMapping(instituteId, academicSessionId, userId, lessonPlanId, lessonPlanChapterPayloadList);
                            if(!MapUtils.isEmpty(chapterTopicPayloadMap)){
                                boolean result = addChapterTopicLessonMapping(instituteId, academicSessionId, userId, courseId, chapterTopicPayloadMap);
                                if(!result){
                                    throw new ApplicationException(
                                        new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                                }
                            }
                        }
                    }
                    if(!MapUtils.isEmpty(chapterTopicPayloadAddMap)){
                        boolean result = addChapterTopicLessonMapping(instituteId, academicSessionId, userId, courseId, chapterTopicPayloadAddMap);
                        if(!result){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                    }
                    if(!CollectionUtils.isEmpty(lessonPlanChapterUpdatePayload)){
                        boolean result = updateLessonPlanChapterMapping(instituteId, academicSessionId, userId, courseId, lessonPlanChapterUpdatePayload);
                        if(!result){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                    }
                    if(!CollectionUtils.isEmpty(chapterTopicUpdatePayload)){
                        boolean result = updateChapterTopicLessonMapping(instituteId, academicSessionId, userId, courseId, chapterTopicUpdatePayload);
                        if(!result){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                    }
                    if(!CollectionUtils.isEmpty(deleteChapterIdSet)){
                        boolean deleteStaffResponse = deleteStaffResponseFromLessonPlan(instituteId, academicSessionId, userId, courseId, null, deleteChapterIdSet, null);
                        if(!deleteStaffResponse){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                        boolean deleteTopicOfChapter = deleteTopicFromChapterOfLessonPlan(instituteId, academicSessionId, userId, courseId, deleteChapterIdSet, null);
                        if(!deleteTopicOfChapter){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                        boolean deleteChapterFromLessonPlan = deleteChapterFromLessonPlan(instituteId, academicSessionId, userId, courseId, null, deleteChapterIdSet);
                        if(!deleteChapterFromLessonPlan){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                    }
                    if(!CollectionUtils.isEmpty(deleteTopicIdSet)){
                        boolean deleteStaffResponse = deleteStaffResponseFromLessonPlan(instituteId, academicSessionId, userId, courseId, null, null, deleteTopicIdSet);
                        if(!deleteStaffResponse){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                        boolean deleteTopicOfChapter = deleteTopicFromChapterOfLessonPlan(instituteId, academicSessionId, userId, courseId, null, deleteTopicIdSet);
                        if(!deleteTopicOfChapter){
                            throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                        }
                    }

                }catch (Exception ex) {
                    throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete Data But If Any New LessonPlan is Added. It is Added successfully"));
                }
                return true;
            }
        });
    }

    public boolean lessonPlanCloneData(int instituteId, int academicSessionId, UUID userId, LessonPlanClonePayload lessonPlanClonePayload){
         if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(lessonPlanClonePayload == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to Clone Lesson Plan Details"));
        }
        validateLessonPlanClonePayload(instituteId, academicSessionId, lessonPlanClonePayload);
        Set<UUID> exsistingLessonPlanId = lessonPlanDao.getLessonPlanIdSet(instituteId, academicSessionId, lessonPlanClonePayload.getStandardId(), lessonPlanClonePayload.getSectionIds());
        return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
                List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadList = getLessonPlanPayloadOfClonedData(instituteId, academicSessionId, lessonPlanClonePayload);
                if(CollectionUtils.isEmpty(lessonPlanMetaDataChapterTopicPayloadList)){
                    throw new ApplicationException(
					            new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Lesson Plan Not Found For The Cloning."));
                }
                if(!CollectionUtils.isEmpty(exsistingLessonPlanId)){
                    for(UUID lessonPlanId : exsistingLessonPlanId){
                        boolean result = lessonPlanDao.deleteFullLessonPlanWithoutTransaction(instituteId, academicSessionId, lessonPlanId);
                        if(!result){
                            throw new ApplicationException(
					            new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able to Clone Lesson Plan Details"));
                        }
                    }
                }
                boolean addLessonPlan = addLessonPlanWithChapterAndTopic(instituteId, academicSessionId, userId, null, lessonPlanMetaDataChapterTopicPayloadList);
                if(!addLessonPlan){
                    throw new ApplicationException(
					            new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able to Clone Lesson Plan Details"));
                }
                return true;
            }
        });
    }

    private List<LessonPlanMetaDataWithChapterAndTopicPayload> getLessonPlanPayloadOfClonedData(int instituteId, int academicSessionId, LessonPlanClonePayload lessonPlanClonePayload){
        List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadList = new ArrayList<>();
        for(UUID lessonPlanId : lessonPlanClonePayload.getLessonPlanIds()){
            List<LessonPlanMetaDataResponse> lessonPlanMetaDataResponseList = lessonPlanDao.getLessonPlanMetaDataResponses(instituteId, academicSessionId, null, null, null, lessonPlanId);
            List<LessonPlanChapterDetails> lessonPlanChapterDetailList = getChapterTopicDetails(lessonPlanId, null);
            if(CollectionUtils.isEmpty(lessonPlanMetaDataResponseList) || CollectionUtils.isEmpty(lessonPlanChapterDetailList)){
                continue;
            }
            // it will contain only one lesson plan metadata
            LessonPlanMetaDataResponse lessonPlanMetaDataResponse = lessonPlanMetaDataResponseList.get(0);
            LessonPlanMetaDataPayload lessonPlanMetaDataPayload = new LessonPlanMetaDataPayload(null, lessonPlanMetaDataResponse.getTitle(), lessonPlanMetaDataResponse.getDescription(), lessonPlanMetaDataResponse.getStandardId(), lessonPlanClonePayload.getSectionIds(), lessonPlanMetaDataResponse.getCourseId(), lessonPlanMetaDataResponse.getLessonPlanStatus());
            List<LessonPlanChapterPayload> lessonPlanChapterPayloadList = new ArrayList<>();
            for(LessonPlanChapterDetails lessonPlanChapterDetails : lessonPlanChapterDetailList){
                List<ChapterTopicPayload> chapterTopicPayloadList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(lessonPlanChapterDetails.getChapterTopicDetails())){
                    for(ChapterTopicDetails chapterTopicDetails : lessonPlanChapterDetails.getChapterTopicDetails()){
                        chapterTopicPayloadList.add(new ChapterTopicPayload(chapterTopicDetails.getTopicId(), chapterTopicDetails.getTitle(), chapterTopicDetails.getDescription(), chapterTopicDetails.getDueDate()));
                    }   
                }
                lessonPlanChapterPayloadList.add(new LessonPlanChapterPayload(lessonPlanChapterDetails.getChapterId(), lessonPlanChapterDetails.getTitle(), lessonPlanChapterDetails.getDescription(), chapterTopicPayloadList, lessonPlanChapterDetails.getDueDate()));
            }
            lessonPlanMetaDataChapterTopicPayloadList.add(new LessonPlanMetaDataWithChapterAndTopicPayload(lessonPlanMetaDataPayload, lessonPlanChapterPayloadList));
        }
        return lessonPlanMetaDataChapterTopicPayloadList;
    }

    private void validateLessonPlanClonePayload(int instituteId, int academicSessionId, LessonPlanClonePayload lessonPlanClonePayload){
        if(CollectionUtils.isEmpty(lessonPlanClonePayload.getLessonPlanIds())){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Select At least one lesson Plan To Be Cloned"));
        }
        if(lessonPlanClonePayload.getStandardId() == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Standard Id Is Required Can't Be Null"));
        }
        if(CollectionUtils.isEmpty(lessonPlanClonePayload.getSectionIds())){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "At Least select One Section Id For Cloning"));
        }
        validateStandardAndSection(lessonPlanClonePayload.getStandardId(), lessonPlanClonePayload.getSectionIds(), instituteId, academicSessionId);
    }

    public boolean staffLessonPlanFunctionClassification(int instituteId, int academicSessionId, UUID userId, UUID courseId, List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsPayloads){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(CollectionUtils.isEmpty(staffLessonPlanDetailsPayloads)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STAFF_RESPONSE_ACCESS, false) && !userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add Response In Lesson Plan"));
        }
        List<UUID> lessonPlanIdList = vaildateStaffLessonPlanPayload(instituteId, academicSessionId, userId, staffLessonPlanDetailsPayloads);
        Set<UUID> topicIdStaffDataExsists = lessonPlanDao.getStaffLessonPlanMapping(lessonPlanIdList);
        List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsAddPayloads = new ArrayList<>();
        List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsUpdatePayloads = getStaffLessonPlanResonpseUpdateAndAddData(staffLessonPlanDetailsPayloads, staffLessonPlanDetailsAddPayloads, topicIdStaffDataExsists);
        return transactionTemplate.execute(new TransactionCallback<Boolean>() {
			public Boolean doInTransaction(TransactionStatus status) {
                if(!CollectionUtils.isEmpty(staffLessonPlanDetailsAddPayloads)){
                    boolean result = addStaffLessonPlanResponse(instituteId, academicSessionId, userId, staffLessonPlanDetailsAddPayloads);
                    if(!result){
                        throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete staff Response Data."));
                    }
                }
                if(!CollectionUtils.isEmpty(staffLessonPlanDetailsUpdatePayloads)){
                    boolean result = updateStaffLessonPlanResponse(instituteId, academicSessionId, userId, staffLessonPlanDetailsUpdatePayloads);
                    if(!result){
                        throw new ApplicationException(
                                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Not Able To Add, Update Or delete staff Response Data."));
                    }
                }
                return true;
            }
        });
    }

    private List<StaffLessonPlanDetailsPayload> getStaffLessonPlanResonpseUpdateAndAddData(List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsPayloads,  List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsAddPayloads, Set<UUID> topicIdStaffDataExsists){
        if(CollectionUtils.isEmpty(topicIdStaffDataExsists)){
            staffLessonPlanDetailsAddPayloads.addAll(staffLessonPlanDetailsPayloads);
            return null;
        }
        List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsUpdatePayloads = new ArrayList<>();
        for(StaffLessonPlanDetailsPayload staffLessonPlanDetailsPayload : staffLessonPlanDetailsPayloads){
            if(topicIdStaffDataExsists.contains(staffLessonPlanDetailsPayload.getTopicId())){
                staffLessonPlanDetailsUpdatePayloads.add(staffLessonPlanDetailsPayload);
                continue;
            }
            staffLessonPlanDetailsAddPayloads.add(staffLessonPlanDetailsPayload);
        }
        return staffLessonPlanDetailsUpdatePayloads;
    }

    private Map<UUID, LessonPlanChapterPayload> getLessonPlanChapterPayloadMap(UUID lessonPlanId, List<LessonPlanChapterPayload> lessonPlanChapterPayloadList,  Map<UUID, List<LessonPlanChapterPayload>> lessonPlanChapterPayloadAddMap,  Map<UUID, List<ChapterTopicPayload>> chapterTopicPayloadAddMap,  Map<UUID, ChapterTopicPayload> topicIdPayload){
        if(CollectionUtils.isEmpty(lessonPlanChapterPayloadList)){
            throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "No Chapter Found In The Data Atleast One Chapter data should be there"));
        }
        Map<UUID, LessonPlanChapterPayload> lessonPlanChapterMap= new HashMap<>();
        List<LessonPlanChapterPayload> lessonPlanChapterPayloadsAdd = new ArrayList<>();
        for(LessonPlanChapterPayload lessonPlanChapterPayload : lessonPlanChapterPayloadList){
            if(lessonPlanChapterPayload.getChapterId() == null){
                lessonPlanChapterPayloadsAdd.add(lessonPlanChapterPayload);
                continue;
            }
            lessonPlanChapterMap.putIfAbsent(lessonPlanChapterPayload.getChapterId(), lessonPlanChapterPayload);
            if(CollectionUtils.isEmpty(lessonPlanChapterPayload.getChapterTopicPayloadList())){
                continue;
            }
            List<ChapterTopicPayload> chapterTopicPayloadsAdd = new ArrayList<>();
            for(ChapterTopicPayload chapterTopicPayload : lessonPlanChapterPayload.getChapterTopicPayloadList()){
                if(chapterTopicPayload.getTopicId() == null){
                    chapterTopicPayloadsAdd.add(chapterTopicPayload);
                    continue;
                }
                topicIdPayload.putIfAbsent(chapterTopicPayload.getTopicId(), chapterTopicPayload);
            }
            if(!CollectionUtils.isEmpty(chapterTopicPayloadsAdd)){
                chapterTopicPayloadAddMap.putIfAbsent(lessonPlanChapterPayload.getChapterId(), chapterTopicPayloadsAdd);
            }
        }
        lessonPlanChapterPayloadAddMap.put(lessonPlanId, lessonPlanChapterPayloadsAdd);
        return lessonPlanChapterMap;
    }

    public boolean deleteLessonPlanDetails(int instituteId, int academicSessionId, UUID userId, UUID lessonPlanId){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (lessonPlanId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            UUID courseId = lessonPlanDao.getCourseIdFromLessonPlanDetails(instituteId, academicSessionId, lessonPlanId);
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        return lessonPlanDao.deleteFullLessonPlan(instituteId, academicSessionId, lessonPlanId);
    }

    public boolean deleteChapterFromLessonPlan(int instituteId, int academicSessionId, UUID userId, UUID courseId, UUID lessonPlanId, Set<UUID> chapterIdSet){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if(userId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
        }
        if(lessonPlanId == null && CollectionUtils.isEmpty(chapterIdSet)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid chapter id. or lesson plan id."));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        return lessonPlanDao.deleteChapterFromLessonPlan(lessonPlanId, chapterIdSet);
    }

    public boolean deleteTopicFromChapterOfLessonPlan(int instituteId, int academicSessionId, UUID userId, UUID courseId, Set<UUID> chapterIdSet, Set<UUID> topicIdSet){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if(userId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
        }
        if(CollectionUtils.isEmpty(topicIdSet) && CollectionUtils.isEmpty(chapterIdSet)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Topic id. or chapter id."));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        return lessonPlanDao.deleteTopicFromChapterOfLessonPlan(chapterIdSet, topicIdSet);
    }

    public boolean updateLessonPlanDetails(int instituteId, int academicSessionId, UUID userId, LessonPlanMetaDataPayload lessonPlanMetaDataPayload){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(lessonPlanMetaDataPayload == null){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
        }
        if(lessonPlanMetaDataPayload.getLessonPlanId() == null){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Lesson Plan Id can't Be null"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            UUID courseId = lessonPlanDao.getCourseIdFromLessonPlanDetails(instituteId, academicSessionId, lessonPlanMetaDataPayload.getLessonPlanId());
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        validateStandardAndSection(lessonPlanMetaDataPayload.getStandardId(), lessonPlanMetaDataPayload.getSectionIds(), instituteId, academicSessionId);
        return lessonPlanDao.updateLessonPlanDetails(userId, lessonPlanMetaDataPayload);
    }

    public boolean updateLessonPlanChapterMapping(int instituteId, int academicSessionId, UUID userId, UUID courseId, List<LessonPlanChapterPayload> lessonPlanChapterPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if (courseId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid course id."));
		}
        if(CollectionUtils.isEmpty(lessonPlanChapterPayloadList)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        validateLessonPlanChapterMapping(lessonPlanChapterPayloadList, true);
        return lessonPlanDao.updateLessonPlanChapterMapping(lessonPlanChapterPayloadList);
    }

    public Map<UUID,List<ChapterTopicPayload>> addLessonPlanChapterMapping(int instituteId, int academicSessionId, UUID userId, UUID lessonPlanId, List<LessonPlanChapterPayload> lessonPlanChapterPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if (lessonPlanId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            UUID courseId = lessonPlanDao.getCourseIdFromLessonPlanDetails(instituteId, academicSessionId, lessonPlanId);
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        validateLessonPlanChapterMapping(lessonPlanChapterPayloadList, false);
        List<UUID> lessonPlanIdList = new ArrayList<>();
        lessonPlanIdList.add(lessonPlanId);
        return lessonPlanDao.addLessonPlanChapterMapping(lessonPlanIdList, lessonPlanChapterPayloadList);
    }

    private void validateLessonPlanChapterMapping(List<LessonPlanChapterPayload> lessonPlanChapterPayloadList, boolean chapterIdCheck){
        for(LessonPlanChapterPayload lessonPlanChapterPayload : lessonPlanChapterPayloadList){
            if(chapterIdCheck && (lessonPlanChapterPayload.getChapterId() == null)){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Chapter Id/Chapter Name can't Be null"));
            }
            if(StringUtils.isBlank(lessonPlanChapterPayload.getTitle())){
                 throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Chapter Name Can't Be null"));
            }
        }
    }

    public boolean addChapterTopicLessonMapping(int instituteId, int academicSessionId, UUID userId, UUID courseId, Map<UUID,List<ChapterTopicPayload>> chapterTopicPayloadMap){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if (courseId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(MapUtils.isEmpty(chapterTopicPayloadMap)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        for(Map.Entry<UUID, List<ChapterTopicPayload>> entry : chapterTopicPayloadMap.entrySet()){
            if(entry.getKey() == null){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid chapter id."));
            }
            validateChapterTopicLessonMapping(entry.getValue(), false);
        }
        return lessonPlanDao.addChapterTopicLessonMapping(chapterTopicPayloadMap);
    }
    public boolean updateChapterTopicLessonMapping(int instituteId, int academicSessionId, UUID userId, UUID courseId, List<ChapterTopicPayload> chapterTopicPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if (courseId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid course id."));
		}
        if(CollectionUtils.isEmpty(chapterTopicPayloadList)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        validateChapterTopicLessonMapping(chapterTopicPayloadList, true);
        return lessonPlanDao.updateChapterTopicLessonMapping(chapterTopicPayloadList);
    }

    private void validateChapterTopicLessonMapping(List<ChapterTopicPayload> chapterTopicPayloadList, boolean topicIdCheck){
        for(ChapterTopicPayload chapterTopicPayload : chapterTopicPayloadList){
            if(topicIdCheck && chapterTopicPayload.getTopicId() == null){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid topic Id can't Be null"));
            }
            if(StringUtils.isBlank(chapterTopicPayload.getTitle())){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Topic Name Can't Be null"));
            }
        }
    }

    public boolean addLessonPlanWithChapterAndTopic(int instituteId, int academicSessionId, UUID userId, UUID courseId, List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
            validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        validateLessonPlanMetaDataWithChapterAndTopicPayload(instituteId, academicSessionId, lessonPlanMetaDataChapterTopicPayloadList);
       
        return lessonPlanDao.addLessonPlanWithChapterAndTopic(instituteId, academicSessionId, userId, lessonPlanMetaDataChapterTopicPayloadList);
    }
    private void validateLessonPlanMetaDataWithChapterAndTopicPayload(int instituteId, int academicSessionId, List<LessonPlanMetaDataWithChapterAndTopicPayload> lessonPlanMetaDataChapterTopicPayloadList){
        if(CollectionUtils.isEmpty(lessonPlanMetaDataChapterTopicPayloadList)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
        }
        for(LessonPlanMetaDataWithChapterAndTopicPayload lessonPlanMetaDataWithChapterAndTopicPayload : lessonPlanMetaDataChapterTopicPayloadList){
            LessonPlanMetaDataPayload lessonPlanMetaDataPayload = lessonPlanMetaDataWithChapterAndTopicPayload.getLessonPlanMetaDataPayload();
            if(lessonPlanMetaDataPayload.getCourseId() == null){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Course Id Can't Be Empty"));
            }
            if(lessonPlanMetaDataPayload.getLessonPlanStatus() != LessonPlanStatus.ACTIVE && lessonPlanMetaDataPayload.getLessonPlanStatus() != LessonPlanStatus.IN_ACTIVE){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Lesson Plan Status Doesn't exsist"));
            }
            validateStandardAndSection(lessonPlanMetaDataPayload.getStandardId(), lessonPlanMetaDataPayload.getSectionIds(), instituteId, academicSessionId);
            List<LessonPlanChapterPayload> lessonPlanChapterPayloadList = lessonPlanMetaDataWithChapterAndTopicPayload.getLessonPlanChapterPayloadList();
            if(CollectionUtils.isEmpty(lessonPlanChapterPayloadList)){
                throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Lesson Plan Does Not Containe Any Chapter"));
            }
            for(LessonPlanChapterPayload lessonPlanChapterPayload : lessonPlanChapterPayloadList){
                List<ChapterTopicPayload> chapterTopicPayloadList = lessonPlanChapterPayload.getChapterTopicPayloadList();
                if(CollectionUtils.isEmpty(chapterTopicPayloadList)){
                    continue;
                }
                if(lessonPlanChapterPayload.getDueDate()!= null){
                    for(ChapterTopicPayload chapterTopicPayload : chapterTopicPayloadList){
                        if(chapterTopicPayload.getDueDate() == null){
                            continue;
                        }
                        if(lessonPlanChapterPayload.getDueDate() < chapterTopicPayload.getDueDate()){
                            throw new ApplicationException(
					            new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Chapter Due Date Can't be less than Topic Due Date"));
                        }
                    }
                }
            }
        }

    }
    private void validateStandardAndSection(UUID standardId, List<Integer> sectionIds, int instituteId,
			int academicSessionId) {

         if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid standard."));
		}

		final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		boolean standardFound = false;
		List<StandardSections> standardSectionList = null;
		for (final Standard standard : standards) {
			if (standardId.equals(standard.getStandardId())) {
				standardFound = true;
				standardSectionList = standard.getStandardSectionList();
				break;
			}
		}

		if (!standardFound) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Given standard does not exists."));
		}

		validateSection(sectionIds, standardSectionList);
	}

	private void validateSection(List<Integer> sectionIds, List<StandardSections> standardSectionList) {
		if (CollectionUtils.isEmpty(standardSectionList)) {
            if (!CollectionUtils.isEmpty(sectionIds)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA,
                        "Given section(s) do not exist for standard."));
            }
            return;
        }

        if (CollectionUtils.isEmpty(sectionIds)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "No section(s) provided for Assessment."));
        }
        Set<Integer> availableSectionIds = new HashSet<Integer>();
        for (StandardSections standardSections : standardSectionList) {
            availableSectionIds.add(standardSections.getSectionId());
        }
        for (Integer sectionId : sectionIds) {
            if (!availableSectionIds.contains(sectionId)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA,
                        "Section ID " + sectionId + " does not exist for the given standard."));
            }
        }
	}

    public boolean addStaffLessonPlanResponse(int instituteId, int academicSessionId, UUID userId, List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
        if(userId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STAFF_RESPONSE_ACCESS, false) && !userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add Response In Lesson Plan"));
        }
        vaildateStaffLessonPlanPayload(instituteId, academicSessionId, userId, staffLessonPlanDetailsPayloadList);
        return lessonPlanDao.addStaffLessonPlanMapping(staffLessonPlanDetailsPayloadList);
    }
    public boolean updateStaffLessonPlanResponse(int instituteId, int academicSessionId, UUID userId, List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsPayloadList){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
        if(userId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STAFF_RESPONSE_ACCESS, false) && !userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add Response In Lesson Plan"));
        }
        vaildateStaffLessonPlanPayload(instituteId, academicSessionId, userId, staffLessonPlanDetailsPayloadList);
        return lessonPlanDao.updateStaffLessonPlanMapping(staffLessonPlanDetailsPayloadList);
    }
    public boolean deleteStaffResponseFromLessonPlan(int instituteId, int academicSessionId, UUID userId, UUID courseId, UUID lessonPlanId, Set<UUID> chapterIdSet, Set<UUID> topicIdSet){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user id."));
		}
        if(courseId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid course id."));
        }
        if(lessonPlanId == null && CollectionUtils.isEmpty(chapterIdSet) && CollectionUtils.isEmpty(topicIdSet)){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Should Provide Atleast one payload data"));
        }
        if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false)){
            if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_STAFF_ACCESS, false)){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED, "You do not have permission to add, update, or delete lesson plan"));
            }
           validateStaffBelongToThatCourse(instituteId, academicSessionId, userId, courseId);
        }
        return lessonPlanDao.deleteStaffResponseFromLessonPlan(lessonPlanId, chapterIdSet, topicIdSet);
    }

    private List<UUID> vaildateStaffLessonPlanPayload(int instituteId, int academicSessionId, UUID staffId, List<StaffLessonPlanDetailsPayload> staffLessonPlanDetailsPayloadList){
        List<UUID> lessonPlanIdList = new ArrayList<>();
        for(StaffLessonPlanDetailsPayload staffLessonPlanDetailsPayload : staffLessonPlanDetailsPayloadList){
            if(staffLessonPlanDetailsPayload == null){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Payload Can't Be Empty"));
            }
            if(staffLessonPlanDetailsPayload.getLessonPlanId() == null){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Lesson Plan Id"));
            }
            lessonPlanIdList.add(staffLessonPlanDetailsPayload.getLessonPlanId());
            if(staffId == null){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid staff Id"));
            }
            if(staffLessonPlanDetailsPayload.getChapterId() == null){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid chapter Id"));
            }
            if(staffLessonPlanDetailsPayload.getTopicId() == null){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid topic Id"));
            }
            UUID courseId = lessonPlanDao.getCourseIdFromLessonPlanDetails(instituteId, academicSessionId, staffLessonPlanDetailsPayload.getLessonPlanId());
            validateStaffBelongToThatCourse(instituteId, academicSessionId, staffId, courseId);
            if(staffLessonPlanDetailsPayload.getLessonPlanStatus() != LessonPlanStatus.NOT_STARTED && staffLessonPlanDetailsPayload.getLessonPlanStatus() != LessonPlanStatus.IN_PROGRESS && staffLessonPlanDetailsPayload.getLessonPlanStatus() != LessonPlanStatus.COMPLETED){
                throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invaild Status Input"));
            }
            staffLessonPlanDetailsPayload.setStaffId(staffId);
        }
        return lessonPlanIdList;
    }
    private void validateStaffBelongToThatCourse(int instituteId, int academicSessionId, UUID staffId, UUID courseId){
        if(courseId == null){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Lesson Plan Is Not Present"));
        }
        List<StaffStandardEntityDetailsRow> staffStandardEntityDetailsRows = timetableManager.getStaffStandardEntityDetailsRows(instituteId, academicSessionId, staffId, courseId);
        if(CollectionUtils.isEmpty(staffStandardEntityDetailsRows)){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Kindly note that you are unable to add the status for this topic because you are not the designated subject teacher for this course."));
        }
    
    }
    public List<LessonPlanStatusWithCourseAndStaffDetails> getCourseAndStaffDetails(int instituteId, int academicSessionId, UUID userId, UUID standardId, Integer sectionId){
        if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid institute."));
		}
        if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid academic Session Id."));
		}
        if(userId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid user Id."));
        }
        List<UUID> standardIdList = new ArrayList<>();
        List<Integer> sectionIdList = new ArrayList<>();
        UUID staffId = null;
        boolean isAdmin = userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.LESSON_PLAN_ADMIN_ACCESS, false);
        if(!isAdmin){
            List<StaffStandardEntityDetailsRow> staffStandardEntityDetailsRows = timetableManager.getStaffStandardEntityDetailsRows(instituteId, academicSessionId, userId, null);
           standardIdList = getStandardIdsTaughtByStaff(staffStandardEntityDetailsRows);
           if(CollectionUtils.isEmpty(standardIdList)){
                return null;
            }
            staffId = userId;
            return lessonPlanDao.getStaffWithCourseDetails(instituteId, academicSessionId, staffId, standardIdList);
        }
        else{
            if(standardId == null){
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Standard Id."));
            }
            standardIdList.add(standardId);
            if(sectionId != null){
                sectionIdList.add(sectionId);
            }
        }
        
        return lessonPlanDao.getCourseWithStaffDetails(instituteId, academicSessionId, standardIdList, sectionIdList);
    }

    private List<UUID> getStandardIdsTaughtByStaff(List<StaffStandardEntityDetailsRow> staffStandardEntityDetailsRows) {
        List<UUID> standardIdList = new ArrayList<>();
        if(CollectionUtils.isEmpty(staffStandardEntityDetailsRows)){
            return standardIdList;
        }
        for(StaffStandardEntityDetailsRow staffStandardEntityDetailsRow : staffStandardEntityDetailsRows){
            StandardRowDetails standardRowDetails = staffStandardEntityDetailsRow.getStandardRowDetails();
            if(standardRowDetails == null){
                return standardIdList;
            }
            if(!standardIdList.contains(standardRowDetails.getStandardId())){
                standardIdList.add(standardRowDetails.getStandardId());
            }
        }
        return standardIdList;
    }
    

    public List<LessonPlanChapterDetails> getChapterTopicDetails(UUID lessonPlanId, LessonPlanStatus lessonPlanStatus){
        if(lessonPlanId == null){
            throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Invalid Course Id."));
        }
        if(lessonPlanStatus != LessonPlanStatus.ACTIVE && lessonPlanStatus != LessonPlanStatus.IN_ACTIVE && lessonPlanStatus != null){
            throw new ApplicationException(
                new ErrorResponse(ApplicationErrorCode.INVALID_LESSON_DATA, "Status is Not Available"));
        }

        return lessonPlanDao.getChapterTopicDetails(lessonPlanId, lessonPlanStatus);
    }
}
