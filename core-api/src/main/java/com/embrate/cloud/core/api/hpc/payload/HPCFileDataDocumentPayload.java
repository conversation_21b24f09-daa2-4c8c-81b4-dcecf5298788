package com.embrate.cloud.core.api.hpc.payload;

import com.lernen.cloud.core.api.common.FileData;

public class HPCFileDataDocumentPayload {

    private final HPCDocumentPayload hpcDocumentPayload;

    private final FileData fileData;

    public HPCFileDataDocumentPayload(HPCDocumentPayload hpcDocumentPayload, FileData fileData) {
        this.hpcDocumentPayload = hpcDocumentPayload;
        this.fileData = fileData;
    }

    public HPCDocumentPayload getHpcDocumentPayload() {
        return hpcDocumentPayload;
    }

    public FileData getFileData() {
        return fileData;
    }
}
