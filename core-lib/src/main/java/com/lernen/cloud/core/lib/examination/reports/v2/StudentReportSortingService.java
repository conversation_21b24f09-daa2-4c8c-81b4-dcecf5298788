package com.lernen.cloud.core.lib.examination.reports.v2;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.utils.student.StudentSorter;

/**
 * Service class for sorting ExamReportData based on different criteria.
 * Follows SOLID principles by separating sorting concerns.
 */
public class StudentReportSortingService {

    /**
     * Sorts the list of ExamReportData based on the specified sorting parameter.
     *
     * @param examReportDataList List of ExamReportData to sort
     * @param sortingParameter The sorting criteria
     * @return Sorted list of ExamReportData
     */
    public List<ExamReportData> sortExamReportData(List<ExamReportData> examReportDataList,
                                                   StudentSortingParameters sortingParameter) {
        if (sortingParameter == null || examReportDataList == null || examReportDataList.isEmpty()) {
            return examReportDataList;
        }

        switch (sortingParameter) {
            case RANK:
                return sortByRank(examReportDataList);
            case ADMISSION_NUMBER:
            case STUDENT_NAME:
            case ROLL_NUMBER:
                return sortByStudentAttributes(examReportDataList, sortingParameter);
            default:
                return examReportDataList;
        }
    }

    /**
     * Sorts ExamReportData by rank using ExamMarksUtils.sortStudentOnRank
     */
    private List<ExamReportData> sortByRank(List<ExamReportData> examReportDataList) {
        // Create a map of student ID to rank
        Map<UUID, Integer> studentRankMap = createStudentRankMap(examReportDataList);

        // Extract StudentLite objects
        List<StudentLite> studentLiteList = examReportDataList.stream()
                .map(ExamReportData::getStudentLite)
                .collect(Collectors.toList());

        // Sort using ExamMarksUtils
        List<StudentLite> sortedStudentLiteList = ExamMarksUtils.sortStudentOnRank(studentLiteList, studentRankMap);

        // Rebuild ExamReportData list in sorted order
        return rebuildExamReportDataList(sortedStudentLiteList, examReportDataList);
    }

    /**
     * Sorts ExamReportData by student attributes using StudentSorter
     */
    private List<ExamReportData> sortByStudentAttributes(List<ExamReportData> examReportDataList,
                                                         StudentSortingParameters sortingParameter) {
        StudentSorter.sortStudents(examReportDataList, sortingParameter, new ExamReportDataKeyExtractor());
        return examReportDataList;
    }

    /**
     * Creates a map of student ID to rank from ExamReportData list
     */
    private Map<UUID, Integer> createStudentRankMap(List<ExamReportData> examReportDataList) {
        Map<UUID, Integer> studentRankMap = new HashMap<>();
        for (ExamReportData examReportData : examReportDataList) {
            UUID studentId = examReportData.getStudentLite().getStudentId();
            Integer rank = examReportData.getRank();
            if (studentId != null && rank != null) {
                studentRankMap.put(studentId, rank);
            }
        }
        return studentRankMap;
    }

    /**
     * Rebuilds ExamReportData list based on sorted StudentLite list
     */
    private List<ExamReportData> rebuildExamReportDataList(List<StudentLite> sortedStudentLiteList,
                                                           List<ExamReportData> originalExamReportDataList) {
        // Create a map for quick lookup
        Map<UUID, ExamReportData> examReportDataMap = originalExamReportDataList.stream()
                .collect(Collectors.toMap(
                    data -> data.getStudentLite().getStudentId(),
                    data -> data
                ));

        // Rebuild list in sorted order
        return sortedStudentLiteList.stream()
                .map(studentLite -> examReportDataMap.get(studentLite.getStudentId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * Key extractor for ExamReportData to work with StudentSorter
     */
    private static class ExamReportDataKeyExtractor implements StudentSorter.StudentKeyExtractor<ExamReportData> {

        @Override
        public String getAdmissionNumber(ExamReportData examReportData) {
            return examReportData.getStudentLite().getAdmissionNumber();
        }

        @Override
        public String getName(ExamReportData examReportData) {
            return examReportData.getStudentLite().getName();
        }

        @Override
        public String getRollNumber(ExamReportData examReportData) {
            return examReportData.getStudentLite().getStudentSessionData() != null ?
                   examReportData.getStudentLite().getStudentSessionData().getRollNumber() : null;
        }

        @Override
        public String getSectionName(ExamReportData examReportData) {
            return examReportData.getStudentLite().getStudentSessionData() != null &&
                   examReportData.getStudentLite().getStudentSessionData().getStandardSection() != null ?
                   examReportData.getStudentLite().getStudentSessionData().getStandardSection().getSectionName() : null;
        }

        @Override
        public Integer getAdmissionDate(ExamReportData examReportData) {
            return examReportData.getStudentLite().getAdmissionDate();
        }
    }
}
