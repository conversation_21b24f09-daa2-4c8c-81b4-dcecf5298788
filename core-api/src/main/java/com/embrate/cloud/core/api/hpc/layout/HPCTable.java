package com.embrate.cloud.core.api.hpc.layout;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCTable extends HPCElement {
	private String id;

	private List<HPCTableHeader> headerRow;
	private HPCElementAlignment childAlignment;
	private List<HPCCell> cells;

	private HPCPDFAttributes pdfAttributes;

	@Override
	String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public List<HPCTableHeader> getHeaderRow() {
		return headerRow;
	}

	public void setHeaderRow(List<HPCTableHeader> headerRow) {
		this.headerRow = headerRow;
	}

	public HPCElementAlignment getChildAlignment() {
		return childAlignment;
	}

	public void setChildAlignment(HPCElementAlignment childAlignment) {
		this.childAlignment = childAlignment;
	}

	public List<HPCCell> getCells() {
		return cells;
	}

	public void setCells(List<HPCCell> cells) {
		this.cells = cells;
	}

	public HPCPDFAttributes getPdfAttributes() {
		return pdfAttributes;
	}

	public void setPdfAttributes(HPCPDFAttributes pdfAttributes) {
		this.pdfAttributes = pdfAttributes;
	}

	@Override
	public String toString() {
		return "HPCTable{" +
				"id='" + id + '\'' +
				", headerRow=" + headerRow +
				", childAlignment=" + childAlignment +
				", cells=" + cells +
				'}';
	}
}
