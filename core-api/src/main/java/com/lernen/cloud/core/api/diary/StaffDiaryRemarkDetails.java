package com.lernen.cloud.core.api.diary;

import com.lernen.cloud.core.api.staff.StaffLite;
import com.lernen.cloud.core.api.user.Document;
import com.lernen.cloud.core.api.user.UserLite;

import java.util.List;
import java.util.UUID;

public class StaffDiaryRemarkDetails {

    private final int instituteId;
    private final int academicSessionId;
    private final Integer date;
    private final UUID remarkId;
    private final String title;
    private final String description;
    private final List<StaffLite> staffLiteList;
    private final RemarkUserType remarkUserType;
    private final UserLite createdByUser;
    private final Integer createdTimestamp;
    private final Integer updatedTimestamp;
    private final DiaryRemarkCategory diaryRemarkCategory;
    private final int attachmentCount;
    private final long attachmentSize;
    private final String allowedMimeTypes;
    private final List<Document<DiaryRemarkDocumentType>> diaryRemarkAttachments;

    public StaffDiaryRemarkDetails(int instituteId, int academicSessionId, Integer date, UUID remarkId, String title, String description, List<StaffLite> staffLiteList, RemarkUserType remarkUserType, UserLite createdByUser, Inte<PERSON> createdTimestamp, Integer updatedTimestamp, DiaryRemarkCategory diaryRemarkCategory, int attachmentCount, long attachmentSize, String allowedMimeTypes, List<Document<DiaryRemarkDocumentType>> diaryRemarkAttachments) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.date = date;
        this.remarkId = remarkId;
        this.title = title;
        this.description = description;
        this.staffLiteList = staffLiteList;
        this.remarkUserType = remarkUserType;
        this.createdByUser = createdByUser;
        this.createdTimestamp = createdTimestamp;
        this.updatedTimestamp = updatedTimestamp;
        this.diaryRemarkCategory = diaryRemarkCategory;
        this.attachmentCount = attachmentCount;
        this.attachmentSize = attachmentSize;
        this.allowedMimeTypes = allowedMimeTypes;
        this.diaryRemarkAttachments = diaryRemarkAttachments;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public Integer getDate() {
        return date;
    }

    public UUID getRemarkId() {
        return remarkId;
    }

    public String getTitle() {
        return title;
    }

    public String getDescription() {
        return description;
    }

    public List<StaffLite> getStaffLiteList() {
        return staffLiteList;
    }

    public RemarkUserType getRemarkUserType() {
        return remarkUserType;
    }

    public UserLite getCreatedByUser() {
        return createdByUser;
    }

    public DiaryRemarkCategory getDiaryRemarkCategory() {
        return diaryRemarkCategory;
    }

    public int getAttachmentCount() {
        return attachmentCount;
    }

    public long getAttachmentSize() {
        return attachmentSize;
    }

    public String getAllowedMimeTypes() {
        return allowedMimeTypes;
    }

    public List<Document<DiaryRemarkDocumentType>> getDiaryRemarkAttachments() {
        return diaryRemarkAttachments;
    }

    public Integer getCreatedTimestamp() {
        return createdTimestamp;
    }

    public Integer getUpdatedTimestamp() {
        return updatedTimestamp;
    }

    @Override
    public String toString() {
        return "StaffDiaryRemarkDetails{" +
                "instituteId=" + instituteId +
                ", academicSessionId=" + academicSessionId +
                ", date=" + date +
                ", remarkId=" + remarkId +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", staffLiteList=" + staffLiteList +
                ", remarkUserType=" + remarkUserType +
                ", createdByUser=" + createdByUser +
                ", createdTimestamp=" + createdTimestamp +
                ", updatedTimestamp=" + updatedTimestamp +
                ", diaryRemarkCategory=" + diaryRemarkCategory +
                ", attachmentCount=" + attachmentCount +
                ", attachmentSize=" + attachmentSize +
                ", allowedMimeTypes='" + allowedMimeTypes + '\'' +
                ", diaryRemarkAttachments=" + diaryRemarkAttachments +
                '}';
    }
}
