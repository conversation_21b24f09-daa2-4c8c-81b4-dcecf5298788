package com.embrate.cloud.core.api.dashboards.fees;

import com.lernen.cloud.core.api.common.TransactionMode;

/**
 * <AUTHOR>
 */
public class PaymentModeCollection {

	private final TransactionMode mode;

	private final double value;

	public PaymentModeCollection(TransactionMode mode, double value) {
		this.mode = mode;
		this.value = value;
	}

	public TransactionMode getMode() {
		return mode;
	}

	public String getModeDisplayName() {
		return mode.getDisplayName();
	}

	public double getValue() {
		return value;
	}

	@Override
	public String toString() {
		return "PaymentModeAmount{" +
				"mode=" + mode +
				", value=" + value +
				'}';
	}
}
