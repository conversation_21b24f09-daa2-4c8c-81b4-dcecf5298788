package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.TransferCertificateVariables;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class TransferCertificateGenerator10135 extends TransferCertificateGenerator {

	public TransferCertificateGenerator10135(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(TransferCertificateGenerator10135.class);

	public static final float DEFAULT_PAGE_SIDE_MARGIN = 50f;
	public static final float DEFAULT_TABLE_BORDER_WIDTH = 0.5f;
	public static final float LOGO_WIDTH = 75f;
	public static final float LOGO_HEIGHT = 75f;

	private static final String HEADING_1 = "OFFICE OF THE";
	private static final String HEADING_2 = "LALA CENTRAL SCHOOL, HAILAKANDI";
	private static final String HEADING_3 = "(CBSE Based English Medium School)";
	private static final String HEADING_4 = "A Wing of R.D. Foundation";
	private static final String HEADING_5 = "Dhanipur, Near NH 154 Byepass, Lala P.O & P.S. - Lala; Dist. - Hailakandi, Assam; Pin: 788163";
	private static final String HEADING_6_LEFT = "Reg. No: ";
	private static final String HEADING_6_RIGHT = "Date : ";
	private static final String HEADING_7 = "SCHOOL LEAVING CERTIFICATE";

	private static final String CONTENT_1 = "Pupil's Name : ";
	private static final String CONTENT_2 = "Student's Admission Number : ";
	private static final String CONTENT_3 = "Father's Name : ";
	private static final String CONTENT_4 = "Mother's Name : ";
	private static final String CONTENT_5 = "Date of Birth : ";
	private static final String CONTENT_6 = "Certified that ";
	private static final String CONTENT_7 = " attended this school. He/she has paid all sums due to school and was allowed on the above-mentioned date to withdraw his/her name. Reason of transfer being ";
	private static final String CONTENT_8 = " He/she was studying in class ";
	private static final String CONTENT_9 = " in this school. His/her code of conduct was ";
	private static final String CONTENT_10 = ". I wish him/her all the very best for the future. ";

	private static final String SIGNATURE_1 = "Principal";
	private static final String SIGNATURE_2 = "Lala Central School";

	//	Purple color - #54628e (84,98,142,255)
	//	Pink color  - #755a8d (117,90,141,255)


	@Override
	public DocumentOutput generateTransferCertificate(StudentManager studentManager, Institute institute, Student student,
													  String documentName) {
		try {
			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();
			int instituteId = institute.getInstituteId();

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);

			float contentFontSize = 12f;
			float defaultBorderWidth = DEFAULT_TABLE_BORDER_WIDTH;

			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
			DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup,
					null, null, contentFontSize, defaultBorderWidth,
					LOGO_WIDTH, LOGO_HEIGHT, LogoProvider.INSTANCE.getLogo(instituteId),
					null, null);

			float bgImageHeightWidth = 500f;
			generateBackgroundImage(document, documentLayoutSetup, ImageProvider.INSTANCE.getImage(
							ImageProvider._10135_COLOURED_BG_LOGO), bgImageHeightWidth, bgImageHeightWidth, bgImageHeightWidth / 5 - 50,
					bgImageHeightWidth / 2 - 80);
			generateDynamicImageProvider(documentLayoutData, 30f, documentLayoutSetup.getPageSize().getHeight() - LOGO_HEIGHT - 45f, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

			generateHeader(document, documentLayoutSetup, contentFontSize, cambriaBoldFont, cambriaFont, student);
			generateContent(document, documentLayoutSetup, contentFontSize, student, cambriaBoldFont, cambriaFont);
			generateSignatureBox(document, documentLayoutSetup, contentFontSize, cambriaBoldFont, cambriaFont);

			document.close();
			return documentOutput;

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										   float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	private void generateHeader(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								PdfFont cambriaBoldFont, PdfFont cambriaFont, Student student) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setTextAlignment(TextAlignment.CENTER).setFontSize(contentFontSize);

		List<Integer> purpleRGB = EColorUtils.hex2Rgb("#54628e");
		List<Integer> pinkRGB = EColorUtils.hex2Rgb("#755a8d");

		Table table = getPDFTable(documentLayoutSetup, 1)
				.setUnderline().setTextAlignment(TextAlignment.CENTER);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_1)),
				cellLayoutSetup.copy().setPdfFont(cambriaFont).setTextAlignment(TextAlignment.CENTER));
		document.add(table);

		table = getPDFTable(documentLayoutSetup, 1).setPaddingLeft(20f);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_2, purpleRGB)),
				cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setFontSize(contentFontSize + 10));
		document.add(table);

		table = getPDFTable(documentLayoutSetup, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_3)),
				cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.RIGHT)
						.setFontSize(contentFontSize + 2));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_4, pinkRGB)),
				cellLayoutSetup.copy().setPdfFont(cambriaBoldFont).setFontSize(contentFontSize + 6));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_5)),
				cellLayoutSetup.copy().setPdfFont(cambriaFont).setFontSize(contentFontSize));
		document.add(table);

		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), 1);
		canvas.moveTo(12, documentLayoutSetup.getPageSize().getHeight() - 160);
		canvas.lineTo(583, documentLayoutSetup.getPageSize().getHeight() - 160);
		canvas.setLineWidth(2f);
		canvas.closePathStroke();

		String relievedDate = student.getStudentBasicInfo().getRelieveDate() == null ?
				"" : DateUtils.getFormattedDate(student.getStudentBasicInfo().getRelieveDate());
		String registrationNumber = student.getStudentBasicInfo().getRegistrationNumber();
		table = getPDFTable(documentLayoutSetup, 2);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(HEADING_6_LEFT + registrationNumber, cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT)),
				new CellData(HEADING_6_RIGHT + relievedDate, cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT))));
		document.add(table);

		addBlankLine(document, true, 2);

		table = getPDFTable(documentLayoutSetup, 1);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(HEADING_7)),
				cellLayoutSetup.copy().setTextAlignment(TextAlignment.CENTER).setPdfFont(cambriaBoldFont));
		document.add(table);

		addBlankLine(document, true, 2);
	}

	private void generateContent(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
								 Student student, PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {

		int singleContentColumn = 1;
		int columnCount = getColumnCount(documentLayoutSetup.isOfficeCopy(), singleContentColumn);
		float contentWidth = getContentWidth(documentLayoutSetup);
		Table table = getPDFTable(documentLayoutSetup, columnCount);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setWidth(contentWidth / singleContentColumn).setPdfFont(cambriaFont)
				.setFontSize(contentFontSize).setTextAlignment(TextAlignment.LEFT);

		String studentName = student.getStudentBasicInfo().getName();
		String admissionNumber = student.getStudentBasicInfo().getAdmissionNumber();
		String fathersName = student.getStudentFamilyInfo() == null ? NOT_AVAILABLE :
				student.getStudentFamilyInfo().getFathersName();
		String mothersName = student.getStudentFamilyInfo() == null ? NOT_AVAILABLE :
				student.getStudentFamilyInfo().getMothersName();
		String dateOfBirth = student.getStudentBasicInfo().getDateOfBirth() == null
				|| student.getStudentBasicInfo().getDateOfBirth() <= 0 ? NOT_AVAILABLE
				: DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
				DATE_FORMAT, User.DFAULT_TIMEZONE);
		String reason = student.getStudentBasicInfo().getRelieveReason();
		String className = student.getStudentAcademicSessionInfoResponse() == null ? NOT_AVAILABLE :
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection();
		String codeOfConduct = CollectionUtils.isEmpty(student.getTcVariables()) ? NOT_AVAILABLE :
				student.getTcVariables().get(TransferCertificateVariables.CODE_OF_CONDUCT) == null ? NOT_AVAILABLE :
						student.getTcVariables().get(TransferCertificateVariables.CODE_OF_CONDUCT);

		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph(CONTENT_1,
				studentName, cambriaFont, cambriaBoldFont)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph(CONTENT_2,
				admissionNumber, cambriaFont, cambriaBoldFont)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph(CONTENT_3,
				fathersName, cambriaFont, cambriaBoldFont)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph(CONTENT_4,
				mothersName, cambriaFont, cambriaBoldFont)), cellLayoutSetup);
		addRow(table, documentLayoutSetup, Arrays.asList(getKeyValueParagraph(CONTENT_5,
				dateOfBirth, cambriaFont, cambriaBoldFont)), cellLayoutSetup);
		document.add(table);
		addBlankLine(document, false, 2);

//		table = getPDFTable(documentLayoutSetup, columnCount);
//		String content6 = CONTENT_6 + studentName;
//		String content7 = CONTENT_7 + reason;
//		String textContent1 = content6 + content7;
//		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(textContent1)),
//				cellLayoutSetup.copy().setPdfFont(cambriaFont).setTextAlignment(TextAlignment.JUSTIFIED));
//		document.add(table);


		table = getPDFTable(documentLayoutSetup, columnCount);
		Text t6 = new Text(String.format(CONTENT_6)).setFont(cambriaFont);
		Text t7 = new Text(String.format(studentName)).setFont(cambriaBoldFont);
		Text t8 = new Text(String.format(CONTENT_7)).setFont(cambriaFont);
		Text t9 = new Text(String.format(reason)).setFont(cambriaBoldFont);
		Paragraph p1 = new Paragraph();
		p1.add(t6).add(t7).add(t8).add(t9);
		addRow(table, documentLayoutSetup, Arrays.asList(p1),
				cellLayoutSetup.copy().setPdfFont(cambriaFont).setTextAlignment(TextAlignment.JUSTIFIED));

		document.add(table);

		addBlankLine(document, false, 2);

		table = getPDFTable(documentLayoutSetup, columnCount);
		Text t10 = new Text(String.format(CONTENT_8)).setFont(cambriaFont);
		Text t11 = new Text(String.format(className)).setFont(cambriaBoldFont);
		Text t12 = new Text(String.format(CONTENT_9)).setFont(cambriaFont);
		Text t13 = new Text(String.format(codeOfConduct)).setFont(cambriaBoldFont);
		Text t14 = new Text(String.format(CONTENT_10)).setFont(cambriaFont);

		Paragraph p2 = new Paragraph();
		p2.add(t10).add(t11).add(t12).add(t13).add(t14);
		addRow(table, documentLayoutSetup, Arrays.asList(p2),
				cellLayoutSetup.copy().setPdfFont(cambriaFont).setTextAlignment(TextAlignment.JUSTIFIED));
		document.add(table);

//		String content8 = CONTENT_8 + className;
//		String content9 = CONTENT_9 + codeOfConduct;
//		String content10 = CONTENT_10;
//		String textContent2 =  content8 + content9 + content10;



		addBlankLine(document, true, 4);
	}

	private void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
									  PdfFont cambriaBoldFont, PdfFont cambriaFont) throws IOException {
		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(cambriaFont).setFontSize(contentFontSize).setTextAlignment(TextAlignment.RIGHT);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(SIGNATURE_1, signatureCellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(SIGNATURE_2, signatureCellLayoutSetup)));
		document.add(table);
	}

	@Override
	public DocumentOutput generateBulkTransferCertificate(StudentManager studentManager, Institute institute,
			List<Student> students, String documentName) {
		return null;
	}
}
