package com.lernen.cloud.core.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

public class RomanNumberUtils {

	private static final Map<Character, Integer> ROMAN_MAP = new HashMap<>();
	static {
		ROMAN_MAP.put('I', 1);
		ROMAN_MAP.put('V', 5);
		ROMAN_MAP.put('X', 10);
		ROMAN_MAP.put('L', 50);
		ROMAN_MAP.put('C', 100);
		ROMAN_MAP.put('D', 500);
		ROMAN_MAP.put('M', 1000);
	}

	private static final String[] ARABIC_TO_ROMAN = {
        "", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"
    };

	private static final String[] WORDS = {"Zero", "First", "Second", "Third", "Fourth", "Fifth", "Sixth", "Seventh", "Eighth", "Ninth", "Tenth", "Eleventh", "Twelfth"};

	public static String romanToFigure(String romanNumeral) {
		if (romanNumeral == null || romanNumeral.isEmpty() || !romanNumeral.matches("^[IVXLCDM]+$")) {
			return "";
		}

		int result = 0;
		for (int i = 0; i < romanNumeral.length(); i++) {
			if (i > 0 && ROMAN_MAP.get(romanNumeral.charAt(i)) > ROMAN_MAP.get(romanNumeral.charAt(i - 1))) {
				result += ROMAN_MAP.get(romanNumeral.charAt(i)) - 2 * ROMAN_MAP.get(romanNumeral.charAt(i - 1));
			} else {
				result += ROMAN_MAP.get(romanNumeral.charAt(i));
			}
		}
		return String.valueOf(result);
	}

	public static String romanToWord(String romanNumeral) {
		int figure = Integer.parseInt(romanToFigure(romanNumeral));
		if (figure == -1 || figure >= WORDS.length) {
			return "";
		}
		return WORDS[figure];
	}

	public static String stringToRomanNumeral (String[] classNameParts) {
		String romanNumeralPart = "";
		for (String classNamePart : classNameParts) {
			String[] subParts = classNamePart.split("-");
			if (subParts.length <=2) {
				for (String subPart : subParts){
					if (subPart.matches("^[IVXLCDM]+$")) {
						romanNumeralPart = subPart;
						break;
					}
				}
			}
			else if (classNamePart.matches("^[IVXLCDM]+$")) {
				romanNumeralPart = classNamePart;
			}
		}
		return romanNumeralPart;
	}

	public static String figureToRoman(int figure) {                      
        return (figure >= 1 && figure < ARABIC_TO_ROMAN.length)
               ? ARABIC_TO_ROMAN[figure]
               : "";
    }

	public static String standardNameToRoman(String standardDisplayName) {
		if (StringUtils.isBlank(standardDisplayName)) {
			return standardDisplayName;
		}

		Pattern pattern = Pattern.compile("\\b(\\d{1,2})\\b");
		Matcher matcher = pattern.matcher(standardDisplayName);

		if (matcher.find()) {
			int numStartIndex = matcher.start();
			int num = Integer.parseInt(matcher.group(1));

			String beforeNumber = standardDisplayName.substring(0, numStartIndex).toLowerCase();
			if (!beforeNumber.contains("class")) {
				return standardDisplayName;
			}

			String roman = figureToRoman(num);
			if (!roman.isEmpty()) {
				String suffix = standardDisplayName.substring(matcher.end());
				return "Class "+ roman + suffix;
			}
		}

		return standardDisplayName;
	}



}
