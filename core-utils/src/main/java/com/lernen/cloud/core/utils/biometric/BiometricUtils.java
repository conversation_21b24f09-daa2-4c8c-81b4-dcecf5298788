package com.lernen.cloud.core.utils.biometric;

import com.embrate.cloud.core.api.attendance.DeviceUpdateUserIdentityData;
import com.lernen.cloud.core.api.user.biometric.FaceData;
import com.lernen.cloud.core.api.user.biometric.FingerprintData;
import com.lernen.cloud.core.api.user.biometric.RFIDCardData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 */

public class BiometricUtils {

    public static boolean cardExists(List<RFIDCardData> rfidCardDataList, DeviceUpdateUserIdentityData deviceUpdateUserIdentityData) {
        if (CollectionUtils.isEmpty(rfidCardDataList)) {
            return false;
        }

        for (RFIDCardData rfidCardData : rfidCardDataList) {
            return StringUtils.equalsIgnoreCase(rfidCardData.getCardNumber().trim(), deviceUpdateUserIdentityData.getData().trim());
        }

        return false;
    }

    public static boolean fingerprintExists(List<FingerprintData> fingerprintDataList, DeviceUpdateUserIdentityData deviceUpdateUserIdentityData) {
        if (CollectionUtils.isEmpty(fingerprintDataList)) {
            return false;
        }

        for (FingerprintData fingerprintData : fingerprintDataList) {
            return StringUtils.equalsIgnoreCase(fingerprintData.getData().trim(), deviceUpdateUserIdentityData.getData().trim()) &&
                    StringUtils.equalsIgnoreCase(fingerprintData.getIndex().trim(), deviceUpdateUserIdentityData.getIndex().trim())
                    && StringUtils.equalsIgnoreCase(fingerprintData.getSize().trim(), deviceUpdateUserIdentityData.getSize().trim());
        }

        return false;
    }

    public static boolean faceExists(List<FaceData> faceDataList, DeviceUpdateUserIdentityData deviceUpdateUserIdentityData) {
        if (CollectionUtils.isEmpty(faceDataList)) {
            return false;
        }

        for (FaceData faceData : faceDataList) {
            return StringUtils.equalsIgnoreCase(faceData.getData().trim(), deviceUpdateUserIdentityData.getData().trim()) &&
                    StringUtils.equalsIgnoreCase(faceData.getIndex().trim(), deviceUpdateUserIdentityData.getIndex().trim())
                    && StringUtils.equalsIgnoreCase(faceData.getSize().trim(), deviceUpdateUserIdentityData.getSize().trim());
        }

        return false;
    }

}
