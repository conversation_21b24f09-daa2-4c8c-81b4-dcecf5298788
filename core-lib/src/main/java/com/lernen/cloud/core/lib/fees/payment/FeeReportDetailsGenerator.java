package com.lernen.cloud.core.lib.fees.payment;

import com.embrate.cloud.core.api.attendance.notification.UserAttendanceNotificationData;
import com.embrate.cloud.core.api.wallet.UserWalletReportType;
import com.embrate.cloud.core.lib.wallet.UserWalletReportGenerator;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.common.AggMode;
import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fee.discount.DiscountStructureAmountMetadata;
import com.lernen.cloud.core.api.fee.discount.FeeDiscountMetadata;
import com.lernen.cloud.core.api.fee.discount.DiscountStructureType;
import com.lernen.cloud.core.api.fee.discount.StudentFeeDiscountAssignmentMetadata;
import com.lernen.cloud.core.api.fees.*;
import com.lernen.cloud.core.api.fees.payment.*;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.InstituteBankAccountDetails;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.*;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.api.user.UserType;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.fees.configuration.FeeConfigurationManager;
import com.lernen.cloud.core.lib.fees.configuration.FeeDiscountConfigurationManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.NumberUtils;
import com.lernen.cloud.core.utils.StringHelper;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;



public class FeeReportDetailsGenerator extends ReportGenerator {

	private static final Logger logger = LogManager.getLogger(FeeReportDetailsGenerator.class);

	private static final Map<String, List<ReportHeaderAttribute>> REPORT_HEADERS = new HashMap<>();

	private static final String[] STUDENT_FEES_LEVEL_AGGREGATED_REPORT = {"S.No", "Registration Number", "Admission Number", "Student Name", "Status", "Class", "Father Name", "Primary Contact", "Whatsapp Number", "Father Contact", "Mother Contact", "Fees Name",
			"Assigned Amount", "Amount Paid", "Given Discount", "Discount To Be Given", "Due Amount", "Paid Fine Amount", "Due Fine Amount"};

	private static final String[] CANCELLED_TRANSACTION_DETAIL = { "S.No", "Cancelled Date", "Transaction Date",
			"Invoice Number", "Transaction by", "Admission Number", "Student Name", "Status", "Father Name", "Class", "Bank Account" };

	private static final String[] USER_DAY_LEVEL_COLLECTION = {"S.No", "User Id", "User Name", "Transaction Date","Collected Amount", "Student Wallet Usage", "Instant Discount"};

	private static final String[] STUDENT_DISCOUNT_ASSIGNMENT = {"S.No", "Admission Number", "Name", "Status", "Father's Name",
			"Class", "Assigned Discounts", "Amount", "Instant", "Total Discount"};

	private static final String[] DISCOUNT_SUMMARY_REPORT = {"S.No", "Structure Name", "Total Amount"};

	static {
		REPORT_HEADERS.put(FeesReportType.STUDENT_AGGREGATED_PAYMENT.name(), Arrays.asList(
				ReportHeaderAttribute.FEES_SR_NO.markMandatory(),
				ReportHeaderAttribute.REGISTRATION_NUMBER,
				ReportHeaderAttribute.ADMISSION_NUMBER.markMandatory(),
				ReportHeaderAttribute.FEES_STUDENT_NAME.markMandatory(),
				ReportHeaderAttribute.STATUS, ReportHeaderAttribute.STUDENT_CLASS,
				ReportHeaderAttribute.FEES_FATHER_NAME,	ReportHeaderAttribute.PRIMARY_CONTACT, ReportHeaderAttribute.WHATSAPP_NUMBER,
				ReportHeaderAttribute.FEES_FATHER_CONTACT, ReportHeaderAttribute.FEES_MOTHER_CONTACT, ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.markMandatory(),
				ReportHeaderAttribute.FEES_AMOUNT_COLLECTED.markMandatory(), ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.markMandatory(),
				ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.markMandatory(), ReportHeaderAttribute.FEES_DUE_AMOUNT.markMandatory(),
				ReportHeaderAttribute.FEES_PAID_FINE_AMOUNT, ReportHeaderAttribute.FEES_DUE_FINE_AMOUNT));

		REPORT_HEADERS.put(FeesReportType.STUDENT_FEES_HEAD_PAYMENT.name(), Arrays.asList(
				ReportHeaderAttribute.FEES_SR_NO.markMandatory(),
				ReportHeaderAttribute.REGISTRATION_NUMBER,
				ReportHeaderAttribute.ADMISSION_NUMBER.markMandatory(),
				ReportHeaderAttribute.FEES_STUDENT_NAME.markMandatory(),
				ReportHeaderAttribute.STATUS, ReportHeaderAttribute.STUDENT_CLASS,
				ReportHeaderAttribute.FEES_FATHER_NAME, ReportHeaderAttribute.PRIMARY_CONTACT, ReportHeaderAttribute.WHATSAPP_NUMBER,
				ReportHeaderAttribute.FEES_FATHER_CONTACT, ReportHeaderAttribute.FEES_MOTHER_CONTACT, ReportHeaderAttribute.FEES_HEAD_NAME.markMandatory(), ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.markMandatory(),
				ReportHeaderAttribute.AMOUNT_PAID.markMandatory(), ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.markMandatory(),
				ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.markMandatory(), ReportHeaderAttribute.FEES_DUE_AMOUNT.markMandatory()));

		REPORT_HEADERS.put(FeesReportType.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL.name(), Arrays.asList(
				ReportHeaderAttribute.FEES_SR_NO,
				ReportHeaderAttribute.TRANSACTION_DATE,
				ReportHeaderAttribute.TRANSACTION_ADDED_AT,
				ReportHeaderAttribute.TRANSACTION_MODE,
				ReportHeaderAttribute.INVOICE_NUMBER.markMandatory(),
				ReportHeaderAttribute.TRANSACTION_BY,
				ReportHeaderAttribute.REGISTRATION_NUMBER,
				ReportHeaderAttribute.ADMISSION_NUMBER,
				ReportHeaderAttribute.FEES_STUDENT_NAME,
				ReportHeaderAttribute.STATUS, ReportHeaderAttribute.FEES_FATHER_NAME,
				ReportHeaderAttribute.STUDENT_CLASS,
				ReportHeaderAttribute.BANK_ACCOUNT, ReportHeaderAttribute.REMARKS));

		REPORT_HEADERS.put(FeesReportType.TRANSACTION_SUMMARY_DETAILED_REPORT.name(), Arrays.asList(
				ReportHeaderAttribute.FEES_SR_NO,
				ReportHeaderAttribute.TRANSACTION_DATE,
				ReportHeaderAttribute.TRANSACTION_ADDED_AT,
				ReportHeaderAttribute.TRANSACTION_MODE,
				ReportHeaderAttribute.INVOICE_NUMBER.markMandatory(),
				ReportHeaderAttribute.TRANSACTION_BY,
				ReportHeaderAttribute.REGISTRATION_NUMBER,
				ReportHeaderAttribute.ADMISSION_NUMBER,
				ReportHeaderAttribute.FEES_STUDENT_NAME,
				ReportHeaderAttribute.STATUS, ReportHeaderAttribute.FEES_FATHER_NAME,
				ReportHeaderAttribute.STUDENT_CLASS,
				ReportHeaderAttribute.BANK_ACCOUNT, ReportHeaderAttribute.REMARKS));
	}

	private final FeeConfigurationManager feeConfigurationManager;
	private final FeeDiscountConfigurationManager feeDiscountConfigurationManager;
	private final FeePaymentInsightManager feePaymentInsightManager;
	private final UserManager userManager;
	private final UserPermissionManager userPermissionManager;
	private final InstituteManager instituteManager;
	private final UserWalletReportGenerator userWalletReportGenerator;
	private final StudentManager studentManager;

	public FeeReportDetailsGenerator(FeePaymentInsightManager feePaymentInsightManager,
									 UserManager userManager, UserPermissionManager userPermissionManager, InstituteManager instituteManager,
									 FeeConfigurationManager feeConfigurationManager, FeeDiscountConfigurationManager feeDiscountConfigurationManager,
									 UserWalletReportGenerator userWalletReportGenerator, StudentManager studentManager) {
		this.feePaymentInsightManager = feePaymentInsightManager;
		this.userManager = userManager;
		this.userPermissionManager = userPermissionManager;
		this.instituteManager = instituteManager;
		this.feeConfigurationManager = feeConfigurationManager;
		this.feeDiscountConfigurationManager = feeDiscountConfigurationManager;
		this.userWalletReportGenerator = userWalletReportGenerator;
		this.studentManager = studentManager;
	}

	public List<ReportHeaderAttribute> getReportHeader(FeesReportType feesReportType) {
        return REPORT_HEADERS.containsKey(feesReportType.name()) ? REPORT_HEADERS.get(feesReportType.name())
                : new ArrayList<>();
    }

    public Map<String, List<ReportHeaderAttribute>> getReportHeader() {
        return REPORT_HEADERS;
    }

	public ReportDetails generateReport(int instituteId, Integer academicSessionId, FeesReportType feesReportType,
										Integer start, Integer end, Integer feeDueDate, String feeIdsStr, String requiredStandardsCSV,
										FeePaymentTransactionStatus feePaymentTransactionStatus,String requiredHeaders, UUID userId, DownloadFormat downloadFormat,
										String studentStatusCSV, String feeHeadIdsStr, String multipleAcademicSessionIdsStr, FeeReportDataType feeReportDataType, String transactionModeStr, boolean hideStudentWithZeroFeesAssignment, ReportRowColumnDataVisibility reportRowColumnDataVisibility, boolean showStudentDetailsWithZeroWalletBalance) {

		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid User Id"));
		}

		if(downloadFormat == DownloadFormat.EXCEL) {
			if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_REPORTS, false)) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
						"You don't have access to download fees reports in excel!"));
			}
		} else if (downloadFormat == DownloadFormat.PDF) {
			if(!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_PDF_REPORTS, false)) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
						"You don't have access to download fees reports in pdf!"));
			}
		}

		// academicSessionId = 0 means no filtering
		academicSessionId = academicSessionId == 0 ? null : academicSessionId;

		final Map<UUID, List<Integer>> requiredStandardSections = convertToRequiredStandardsWithSection(requiredStandardsCSV);
		final Set<UUID> requiredStandards = requiredStandardSections.keySet();
		final Set<UUID> feeIds = convertStrToUUIDSet(feeIdsStr);
        final Set<StudentStatus> studentStatuses = getStudentStatus(studentStatusCSV);
        final Set<Integer> multipleSessionIds = convertToRequiredMultipleSessions(multipleAcademicSessionIdsStr);
        final Set<Integer> feeHeadIds = convertStrToIntegerSet(feeHeadIdsStr);
		final Set<String> requiredHeaderAttributes = convertToRequiredHeaderAttributes(requiredHeaders);
		feeReportDataType = feeReportDataType == null ? FeeReportDataType.ALL : feeReportDataType;
		Set<TransactionMode> transactionModeSet = getTransactionMode(transactionModeStr);

		switch (feesReportType) {
			case STUDENT_AGGREGATED_PAYMENT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_AGGREGATED_PAYMENT_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Aggregated Fees Report!"));
				}
				return generateStudentLevelAggregatedReport(instituteId, academicSessionId, feeReportDataType,
					requiredStandardSections, feeDueDate, feeIds, feeHeadIds, studentStatuses, requiredHeaderAttributes, feesReportType, hideStudentWithZeroFeesAssignment);

			case STUDENT_FEES_PAYMENT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_FEES_PAYMENT_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Student Fees Level Report!"));
				}
				return generateStudentFeesLevelReport(instituteId, academicSessionId, feeReportDataType,
						feeIds, studentStatuses, requiredStandardSections, feeDueDate);

			case STUDENT_FEES_HEAD_PAYMENT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_FEES_HEAD_PAYMENT_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Student Fees Head Level Report!"));
				}
				switch (reportRowColumnDataVisibility) {
					case COLUMN_WISE:
						return generateStudentFeesHeadLevelReportColWise(instituteId, academicSessionId, feeReportDataType,
								feeHeadIds, studentStatuses, requiredStandardSections, feeDueDate, requiredHeaderAttributes, feesReportType);
					case ROW_WISE:
						return generateStudentFeesHeadLevelReportRowWise(instituteId, academicSessionId, feeReportDataType,
								feeHeadIds, studentStatuses, requiredStandardSections, feeDueDate, requiredHeaderAttributes, feesReportType);
				}
			case FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_HEAD_DAY_LEVEL_COLLECTED_AMOUNT_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Fees Collection Report (Head Wise)!"));
				}
				return generateFeeHeadLevelCollectedAmount(instituteId, academicSessionId, start, end, AggMode.DAY, studentStatuses);

			case TRANSACTION_MODE_AMOUNTS_REPORT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.TRANSACTION_MODE_AMOUNTS_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Fees Collection Report (Mode Wise)!"));
				}
				return generateTransactionAmountsByModeReport(instituteId, academicSessionId, start, end, AggMode.DAY);

			case USER_DAY_LEVEL_COLLECTION:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.USER_DAY_LEVEL_COLLECTION_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download User Day Level Collection Report!"));
				}
				return generateUserWiseDayLevelCollectionReport(instituteId, multipleSessionIds, start, end, userId);

			case TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Transaction Summary Report!"));
				}
				return generateTransactionReports(instituteId, academicSessionId,
						start, end, requiredStandardSections, feePaymentTransactionStatus == null ?
								FeePaymentTransactionStatus.ACTIVE : feePaymentTransactionStatus, transactionModeSet, requiredHeaderAttributes, feesReportType);

			case TRANSACTION_SUMMARY_DETAILED_REPORT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.TRANSACTION_SUMMARY_DETAILED_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Transaction Summary Detailed Report!"));
				}
				return generateTransactionReports(instituteId, academicSessionId,
						start, end, requiredStandardSections, feePaymentTransactionStatus == null ?
								FeePaymentTransactionStatus.ACTIVE : feePaymentTransactionStatus, transactionModeSet, requiredHeaderAttributes, feesReportType);

			case CANCELLED_TRANSACTION_DETAIL:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.CANCELLED_TRANSACTIONS_DETAIL_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Cancelled Transactions Summary Report!"));
				}
				return generateCancelledTransactionDetailReport(instituteId, academicSessionId, start, end, requiredStandardSections);

			case STUDENT_WALLET_TRANSACTIONS:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_WALLET_TRANSACTIONS_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Student Wallet Transactions Report!"));
				}
				return userWalletReportGenerator.generateReport(instituteId, null, null,
						UserWalletReportType.STUDENT_WALLET_TRANSACTIONS, start, end, null, studentStatuses, false);

			case STUDENT_WALLET_BALANCE:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_WALLET_BALANCE_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Student Wallet Balance Report!"));
				}
				return userWalletReportGenerator.generateReport(instituteId, academicSessionId, requiredStandardsCSV,
						UserWalletReportType.STUDENT_WALLET_BALANCE, start, end, null, studentStatuses, showStudentDetailsWithZeroWalletBalance);

			case STUDENT_DISCOUNT_ASSIGNMENT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_DISCOUNT_ASSIGNMENT_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Discount Assignment Report!"));
				}
				return generateStudentDiscountAssignmentReport(instituteId, academicSessionId,
						feesReportType, requiredStandardSections, studentStatuses);

			case STUDENT_LEDGER:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_LEDGER_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Enrolled Students' Ledger Report!"));
				}
				return getStudentLedgerDetails(instituteId, academicSessionId, requiredStandards, feeIds, studentStatuses);

			case STUDENT_AGGREGATED_MULTI_SESSION_DUE_PAYMENT:
				if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.FEE_MULTI_SESSION_DUE_REPORT, false)) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You don't have access to download Aggregated Multi Session Fees Due Report!"));
				}
				return generateStudentLevelAggregatedMultiSessionDueReport(instituteId, multipleSessionIds, requiredStandards,feeDueDate, feeIds, userId);
			case DISCOUNT_SUMMARY_REPORT:
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DISCOUNT_SUMMARY_REPORT);
				return generateDiscountSummaryReport(instituteId, academicSessionId, requiredStandardSections, studentStatuses);
			default:
				return null;
		}
	}

	private List<StudentAllFeePaymentTransactionDetails> getCancelledTransactionDetailsList(
			int instituteId, Integer academicSessionId, Integer startTime, Integer endTime, Set<UUID> requiredStandards) {
		final List<StudentAllFeePaymentTransactionDetails> studentAllFeePaymentTransactionDetailsList = new ArrayList<>();

		final List<StudentAllFeePaymentTransactionDetails> inputStudentAllFeePaymentTransactionDetailsList = feePaymentInsightManager
				.getCancelledTransactionDetailsList(instituteId, startTime, endTime);

		if (CollectionUtils.isEmpty(inputStudentAllFeePaymentTransactionDetailsList)) {
			return studentAllFeePaymentTransactionDetailsList;
		}

		for (final StudentAllFeePaymentTransactionDetails studentAllFeePaymentTransactionDetails : inputStudentAllFeePaymentTransactionDetailsList) {
			final Student student = studentAllFeePaymentTransactionDetails.getStudent();
			if(student == null) {
				continue;
			}
			if(CollectionUtils.isEmpty(studentAllFeePaymentTransactionDetails.getFeePaymentTransactionDetailsList())) {
				continue;
			}

			if(!CollectionUtils.isEmpty(requiredStandards) && !requiredStandards
					.contains(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
				continue;
			}
			List<FeePaymentTransactionDetails> filteredFeePaymentTransactionDetailsList = new ArrayList<>();
			for(FeePaymentTransactionDetails feePaymentTransactionDetails : studentAllFeePaymentTransactionDetails.getFeePaymentTransactionDetailsList()) {
				if(academicSessionId == null || academicSessionId == feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getAcademicSessionId()) {
					filteredFeePaymentTransactionDetailsList.add(feePaymentTransactionDetails);
				}
			}

			studentAllFeePaymentTransactionDetailsList.add(new StudentAllFeePaymentTransactionDetails(student, filteredFeePaymentTransactionDetailsList));
		}

		return studentAllFeePaymentTransactionDetailsList;
	}

	private List<StudentAllFeePaymentTransactionDetails> getStudentFeePaymentTransactionDetailsInDateRange(
			int instituteId, Integer academicSessionId, Integer startTime, Integer endTime, Set<UUID> requiredStandards,
			FeePaymentTransactionStatus feePaymentTransactionStatus) {
		final List<StudentAllFeePaymentTransactionDetails> studentAllFeePaymentTransactionDetailsList = new ArrayList<>();

		final List<StudentAllFeePaymentTransactionDetails> inputStudentAllFeePaymentTransactionDetailsList = feePaymentInsightManager
				.getStudentFeePaymentTransactionDetailsInDateRange(instituteId, startTime, endTime, feePaymentTransactionStatus);

		if (CollectionUtils.isEmpty(inputStudentAllFeePaymentTransactionDetailsList)) {
			return studentAllFeePaymentTransactionDetailsList;
		}

		for (final StudentAllFeePaymentTransactionDetails studentAllFeePaymentTransactionDetails : inputStudentAllFeePaymentTransactionDetailsList) {
			final Student student = studentAllFeePaymentTransactionDetails.getStudent();
			if(student == null) {
				continue;
			}
			if(CollectionUtils.isEmpty(studentAllFeePaymentTransactionDetails.getFeePaymentTransactionDetailsList())) {
				continue;
			}

			if(!CollectionUtils.isEmpty(requiredStandards) && !requiredStandards
					.contains(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId())) {
				continue;
			}
			List<FeePaymentTransactionDetails> filteredFeePaymentTransactionDetailsList = new ArrayList<>();
			for(FeePaymentTransactionDetails feePaymentTransactionDetails : studentAllFeePaymentTransactionDetails.getFeePaymentTransactionDetailsList()) {
				if(academicSessionId == null || academicSessionId == feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getAcademicSessionId()) {
					filteredFeePaymentTransactionDetailsList.add(feePaymentTransactionDetails);
				}
			}

			studentAllFeePaymentTransactionDetailsList.add(new StudentAllFeePaymentTransactionDetails(student, filteredFeePaymentTransactionDetailsList));
		}

		return studentAllFeePaymentTransactionDetailsList;
	}

	private ReportDetails generateCancelledTransactionDetailReport(int instituteId, Integer academicSessionId, Integer start, Integer end,
																   Map<UUID, List<Integer>> requiredStandardSections) {


		final String sheetName = "CancelledTransactionsDetailReport";
		final String reportName = "Cancelled Transactions Detail Report";

		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			final TimeZone timezone = User.DFAULT_TIMEZONE;
			int startTime;
			int endTime;
			startTime = DateUtils.getDayStart(start, timezone);
			endTime = DateUtils.getDayEnd(end, timezone);

			final Set<UUID> requiredStandards = requiredStandardSections.keySet();
			final List<StudentAllFeePaymentTransactionDetails> studentAllFeePaymentTransactionDetailsList = getCancelledTransactionDetailsList(
					instituteId, academicSessionId, startTime, endTime, requiredStandards);

			if (CollectionUtils.isEmpty(studentAllFeePaymentTransactionDetailsList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			final Map<Integer, FeeHeadConfiguration> feeheadMap = new HashMap<>();
			final Map<UUID, Map<UUID, Map<Integer, Double>>> studentTransactionFeeHeadAggregates = new HashMap<>();
			final List<StudentFeePaymentTransactionDetails> studentFeePaymentTransactionDetailsList = new ArrayList<>();
			final Set<UUID> transactionByUsers = new HashSet<>();
			for (final StudentAllFeePaymentTransactionDetails studentAllFeePaymentTransactionDetails : studentAllFeePaymentTransactionDetailsList) {
				final UUID studentId = studentAllFeePaymentTransactionDetails.getStudent().getStudentId();
				if (!studentTransactionFeeHeadAggregates.containsKey(studentId)) {
					studentTransactionFeeHeadAggregates.put(studentId, new HashMap<>());
				}
				for (final FeePaymentTransactionDetails feePaymentTransactionDetails : studentAllFeePaymentTransactionDetails
						.getFeePaymentTransactionDetailsList()) {
					final UUID transactionBy = feePaymentTransactionDetails.getFeePaymentTransactionMetaData()
							.getTransactionAddedBy();

					if (transactionBy != null) {
						transactionByUsers.add(transactionBy);
					}

					studentFeePaymentTransactionDetailsList.add(new StudentFeePaymentTransactionDetails(
							studentAllFeePaymentTransactionDetails.getStudent(), feePaymentTransactionDetails));
					final Map<Integer, Double> feeHeadLevelAggregatedAmounts = new HashMap<>();
					for (final FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentTransactionDetails
							.getFeeIdFeeHeadTransactionDetails()) {
						for (final FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeIdFeeHeadTransactionDetails
								.getFeeHeadTransactionAmountsDetails()) {
							double paidAmount = feeHeadTransactionAmountsDetails.getPaidAmount();
							if(paidAmount == 0) {
								continue;
							}

							final Integer feeHeadId = feeHeadTransactionAmountsDetails.getFeeHeadId();
							feeheadMap.put(feeHeadId, feeHeadTransactionAmountsDetails.getFeeHeadConfiguration());

							if (!feeHeadLevelAggregatedAmounts.containsKey(feeHeadId)) {
								feeHeadLevelAggregatedAmounts.put(feeHeadId, paidAmount);
							} else {
								feeHeadLevelAggregatedAmounts.put(feeHeadId, NumberUtils.addValues(feeHeadLevelAggregatedAmounts.get(feeHeadId), paidAmount));
							}
						}
					}
					studentTransactionFeeHeadAggregates.get(studentId).put(
							feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getTransactionId(),
							feeHeadLevelAggregatedAmounts);
				}
			}
			Collections.sort(studentFeePaymentTransactionDetailsList, new Comparator<StudentFeePaymentTransactionDetails>() {
				@Override
				public int compare(StudentFeePaymentTransactionDetails t1,
								   StudentFeePaymentTransactionDetails t2) {
					if (t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
							.getUpdatedAt() != t2.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getUpdatedAt()) {
						return t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getUpdatedAt() < t2.getFeePaymentTransactionDetails()
								.getFeePaymentTransactionMetaData().getUpdatedAt() ? -1 : 1;
					}
					if (t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
							.getTransactionDate() != t2.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionDate()) {
						return t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getTransactionDate() < t2.getFeePaymentTransactionDetails()
								.getFeePaymentTransactionMetaData().getTransactionDate() ? -1 : 1;
					}
					return -1;
				}
			});

			final List<User> users = userManager.getUsers(instituteId, transactionByUsers);
			final Map<UUID, User> userDetailsMap = new LinkedHashMap<>();
			for (final User userObj : users) {
				userDetailsMap.put(userObj.getUuid(), userObj);
			}

			AcademicSession academicSession = null;
			if(academicSessionId != null) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			return getCancelledReportDetails(institute, reportName, sheetName, academicSession, feeheadMap, userDetailsMap, studentFeePaymentTransactionDetailsList,
					studentTransactionFeeHeadAggregates, requiredStandardSections);
		}
		catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}
		return null;
	}

	private ReportDetails generateTransactionReports(int instituteId, Integer academicSessionId, Integer start, Integer end,
													 Map<UUID, List<Integer>> requiredStandardSections, FeePaymentTransactionStatus feePaymentTransactionStatus, Set<TransactionMode> transactionModeSet, Set<String> requiredHeaderAttributes,
													 FeesReportType feesReportType) {

		String feeReportTypeString = feesReportType == FeesReportType.TRANSACTION_SUMMARY_DETAILED_REPORT ? "Detailed" : EMPTY_TEXT;
		final String sheetName = feePaymentTransactionStatus == null || feePaymentTransactionStatus == FeePaymentTransactionStatus.ACTIVE
				? "Active Transaction " + feeReportTypeString + " Report By Transaction Date" : "Cancelled Transaction " + feeReportTypeString + " Report By Transaction Date";
		final String reportName = feePaymentTransactionStatus == null || feePaymentTransactionStatus == FeePaymentTransactionStatus.ACTIVE ?
				"Active Transaction " + feeReportTypeString + " Report" : "Cancelled Transaction " + feeReportTypeString + " Report";

		try {
			final TimeZone timezone = User.DFAULT_TIMEZONE;
			int startTime;
			int endTime;
			startTime = DateUtils.getDayStart(start, timezone);
			endTime = DateUtils.getDayEnd(end, timezone);

			final Set<UUID> requiredStandards = requiredStandardSections.keySet();

			final List<StudentAllFeePaymentTransactionDetails> studentAllFeePaymentTransactionDetailsList = getStudentFeePaymentTransactionDetailsInDateRange(
					instituteId, academicSessionId, startTime, endTime, requiredStandards, feePaymentTransactionStatus);

			if (CollectionUtils.isEmpty(studentAllFeePaymentTransactionDetailsList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			final Map<Integer, FeeHeadConfiguration> feeheadMap = new HashMap<>();
			final Map<UUID, FeeConfigurationBasicInfo> feeConfigurationBasicInfoMap = new HashMap<>();
			final Map<UUID, Map<UUID, Map<Integer, Double>>> studentTransactionFeeHeadAggregates = new HashMap<>();
			final Map<UUID, Map<UUID, Map<UUID, Map<Integer, Double>>>> studentTransactionFeeIdFeeHeadAggregates = new HashMap<>();
			final Map<UUID, Set<Integer>> feeIdFeeHeadIdMap = new HashMap<>();
			final List<StudentFeePaymentTransactionDetails> studentFeePaymentTransactionDetailsList = new ArrayList<>();
			final Set<UUID> transactionByUsers = new HashSet<>();
			for (final StudentAllFeePaymentTransactionDetails studentAllFeePaymentTransactionDetails : studentAllFeePaymentTransactionDetailsList) {
				final UUID studentId = studentAllFeePaymentTransactionDetails.getStudent().getStudentId();
				if (!studentTransactionFeeHeadAggregates.containsKey(studentId)) {
					studentTransactionFeeHeadAggregates.put(studentId, new HashMap<>());
				}

				if (!studentTransactionFeeIdFeeHeadAggregates.containsKey(studentId)) {
					studentTransactionFeeIdFeeHeadAggregates.put(studentId, new HashMap<>());
				}

				for (final FeePaymentTransactionDetails feePaymentTransactionDetails : studentAllFeePaymentTransactionDetails
						.getFeePaymentTransactionDetailsList()) {
					final UUID transactionBy = feePaymentTransactionDetails.getFeePaymentTransactionMetaData()
							.getTransactionAddedBy();

					if (transactionBy != null) {
						transactionByUsers.add(transactionBy);
					}

					studentFeePaymentTransactionDetailsList.add(new StudentFeePaymentTransactionDetails(
							studentAllFeePaymentTransactionDetails.getStudent(), feePaymentTransactionDetails));

					final Map<Integer, Double> feeHeadLevelAggregatedAmounts = new HashMap<>();
					final Map<UUID, Map<Integer, Double>> feeIdFeeHeadLevelAggregatedAmounts = new HashMap<>();
					for (final FeeIdFeeHeadTransactionDetails feeIdFeeHeadTransactionDetails : feePaymentTransactionDetails
							.getFeeIdFeeHeadTransactionDetails()) {
						for (final FeeHeadTransactionAmountsDetails feeHeadTransactionAmountsDetails : feeIdFeeHeadTransactionDetails
								.getFeeHeadTransactionAmountsDetails()) {
							final Integer feeHeadId = feeHeadTransactionAmountsDetails.getFeeHeadId();
							double paidAmount = feeHeadTransactionAmountsDetails.getPaidAmount();
							if(paidAmount == 0) {
								continue;
							}

							if(!feeIdFeeHeadIdMap.containsKey(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId())) {
								feeIdFeeHeadIdMap.put(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId(), new HashSet<>());
							}

							if(!feeIdFeeHeadLevelAggregatedAmounts.containsKey(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId())) {
								feeIdFeeHeadLevelAggregatedAmounts.put(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId(), new HashMap<>());
							}

							feeConfigurationBasicInfoMap.put(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId(), feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo());
							feeheadMap.put(feeHeadId, feeHeadTransactionAmountsDetails.getFeeHeadConfiguration());

							feeIdFeeHeadIdMap.get(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId()).add(feeHeadId);

							if (!feeHeadLevelAggregatedAmounts.containsKey(feeHeadId)) {
								feeHeadLevelAggregatedAmounts.put(feeHeadId, paidAmount);
							} else {
								feeHeadLevelAggregatedAmounts.put(feeHeadId, NumberUtils.addValues(feeHeadLevelAggregatedAmounts.get(feeHeadId), paidAmount));
							}

							if (!feeIdFeeHeadLevelAggregatedAmounts.get(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId()).containsKey(feeHeadId)) {
								feeIdFeeHeadLevelAggregatedAmounts.get(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId()).put(feeHeadId, paidAmount);
							} else {
								feeIdFeeHeadLevelAggregatedAmounts.get(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId()).put(feeHeadId,
										NumberUtils.addValues(feeIdFeeHeadLevelAggregatedAmounts.get(feeIdFeeHeadTransactionDetails.getFeeConfigurationBasicInfo().getFeeId()).get(feeHeadId), paidAmount));
							}

						}

						studentTransactionFeeIdFeeHeadAggregates.get(studentId).put(
								feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getTransactionId(), feeIdFeeHeadLevelAggregatedAmounts);
					}

					studentTransactionFeeHeadAggregates.get(studentId).put(
							feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getTransactionId(),
							feeHeadLevelAggregatedAmounts);
				}
			}

			Map<UUID, InstituteBankAccountDetails> bankAccountDetailsMap = getInstituteBankAccountDetailsMap(instituteId, studentFeePaymentTransactionDetailsList);

			TreeMap<Integer, List<StudentFeePaymentTransactionDetails>> studentFeePaymentTransactionDetailsMap = new TreeMap<>();
			for(StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails : studentFeePaymentTransactionDetailsList) {
				int transactionDate = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionDate();
				if(transactionDate <= 0) {
					continue;
				}
				transactionDate = DateUtils.getDayStart(transactionDate, DateUtils.DEFAULT_TIMEZONE);
				UUID bankAccountId = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountId();
				if(bankAccountId != null){
					InstituteBankAccountDetails bankAccountDetails = bankAccountDetailsMap.get(bankAccountId);
					if(bankAccountDetails != null) {
						studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().setBankAccountDetails(bankAccountDetails);
					}
				}
				if(!studentFeePaymentTransactionDetailsMap.containsKey(transactionDate)) {
					studentFeePaymentTransactionDetailsMap.put(transactionDate, new ArrayList<>());
				}
				studentFeePaymentTransactionDetailsMap.get(transactionDate).add(studentFeePaymentTransactionDetails);
			}

			if(CollectionUtils.isEmpty(studentFeePaymentTransactionDetailsMap)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			for(List<StudentFeePaymentTransactionDetails> studentFeePaymentTransactionDetailsEntryList : new ArrayList<>(studentFeePaymentTransactionDetailsMap.values())) {
				Collections.sort(studentFeePaymentTransactionDetailsEntryList, new Comparator<StudentFeePaymentTransactionDetails>() {
					@Override
					public int compare(StudentFeePaymentTransactionDetails t1,
									   StudentFeePaymentTransactionDetails t2) {
						int compare = t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getTransactionDate() - t2.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData()
								.getTransactionDate();
						if(compare != 0) {
							return compare;
						}
						return t1.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionAddedAt()
								- t2.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionAddedAt();
					}
				});
			}

			final List<User> users = userManager.getUsersByUserIds(transactionByUsers);
			final Map<UUID, User> userDetailsMap = new LinkedHashMap<>();
			for (final User userObj : users) {
				userDetailsMap.put(userObj.getUuid(), userObj);
			}

			AcademicSession academicSession = null;
			if(academicSessionId != null) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			Institute institute = instituteManager.getInstitute(instituteId);
			switch (feesReportType) {

				case TRANSACTION_WISE_DAY_LEVEL_AMOUNT_DETAIL:
					return generateTransactionSummaryReport(institute, reportName, sheetName, academicSession, feeConfigurationBasicInfoMap, feeheadMap, userDetailsMap, studentFeePaymentTransactionDetailsMap,
							studentTransactionFeeHeadAggregates, requiredStandardSections, requiredHeaderAttributes, transactionModeSet, feesReportType);
				case TRANSACTION_SUMMARY_DETAILED_REPORT:
					return generateTransactionSummaryDetailedReport(institute, reportName, sheetName, academicSession, feeConfigurationBasicInfoMap, feeheadMap, userDetailsMap, studentFeePaymentTransactionDetailsMap,
							studentTransactionFeeIdFeeHeadAggregates, feeIdFeeHeadIdMap, requiredStandardSections, requiredHeaderAttributes, transactionModeSet, feesReportType);
			}
		}
		catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}
		return null;
	}

	private Map<UUID, InstituteBankAccountDetails> getInstituteBankAccountDetailsMap(int instituteId, List<StudentFeePaymentTransactionDetails> studentFeePaymentTransactionDetailsList){
		Set<UUID> bankAccountIds = new HashSet<>();
		for(StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails : studentFeePaymentTransactionDetailsList) {
			if (studentFeePaymentTransactionDetails == null) continue;

			UUID bankAccountId = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails() == null ||
					studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData() == null ? null : studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountId();

			if (bankAccountId != null) {
				bankAccountIds.add(bankAccountId);
			}
		}
		Map<UUID, InstituteBankAccountDetails> bankAccountDetailsMap = new HashMap<>();
		if (!bankAccountIds.isEmpty()) {
			bankAccountDetailsMap = instituteManager.getInstituteBankAccounts(instituteId, bankAccountIds);
		}

		return bankAccountDetailsMap;
	}

	private ReportDetails getCancelledReportDetails(Institute institute, String reportName, String sheetName, AcademicSession academicSession,
										   Map<Integer, FeeHeadConfiguration> feeheadMap, Map<UUID, User> userDetailsMap,
										   List<StudentFeePaymentTransactionDetails> studentFeePaymentTransactionDetailsList,
										   Map<UUID, Map<UUID, Map<Integer, Double>>> studentTransactionFeeHeadAggregates,
										   Map<UUID, List<Integer>> requiredStandardSections) {

		int totalColumns = 0;
		final List<FeeHeadConfiguration> feeHeadConfigurations = new ArrayList<>(feeheadMap.values());
		List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();

		/**
		 * In reportDetails object the first line of 2d list contain the heading this is mandatory
		 */
		List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
		String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
		reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
		headerReportCellDetails.add(reportHeaderRow);

		List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
		int i = 0;
		for (i = 0; i < CANCELLED_TRANSACTION_DETAIL.length; i++) {
			reportCellDetailsHeaderRow.add(new ReportCellDetails(CANCELLED_TRANSACTION_DETAIL[i], STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		}
		totalColumns += CANCELLED_TRANSACTION_DETAIL.length;
		final Map<Integer, Double> totalFeeHeadAmount = new HashMap<>();
		for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
			reportCellDetailsHeaderRow.add(new ReportCellDetails(feeHeadConfiguration.getFeeHead(), STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			totalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(), 0d);
			totalColumns++;
		}

		reportCellDetailsHeaderRow.add(new ReportCellDetails("Fine Amount", STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;
		reportCellDetailsHeaderRow.add(new ReportCellDetails("Grand Total", STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;
		reportCellDetailsHeaderRow.add(new ReportCellDetails("Total Instant Discount", STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;

		headerReportCellDetails.add(reportCellDetailsHeaderRow);

		List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
		CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
		headerMergeCellIndexesList.add(cellIndexes);

		List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

		int rowNum = 1;
		int colSpan = 0;
		int count = 1;
		double totalFineAmount = 0d;
		double totalInstantDiscount = 0d;
		double grandTotal = 0d;

		Map<UUID, InstituteBankAccountDetails> bankAccountDetailsMap = getInstituteBankAccountDetailsMap(institute.getInstituteId(), studentFeePaymentTransactionDetailsList);

		for (final StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails : studentFeePaymentTransactionDetailsList) {

			/**
			 * * Removing non required standards and sections
			 */

			final UUID standardId = studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
			final Integer sectionId = CollectionUtils.isEmpty(studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null : studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();

			if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
				continue;
			}
			if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
					!requiredStandardSections.get(standardId).contains(sectionId)) {
				continue;
			}

			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

			reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));



			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getFeePaymentTransactionMetaData().getUpdatedAt() <= 0 ? EMPTY_TEXT :
					DateUtils.getFormattedDate(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getUpdatedAt(),
					DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));


			reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(
					studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionDate(),
					DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getFeePaymentTransactionMetaData().getInvoiceId(), STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

			final UUID transactionAddedBy = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getFeePaymentTransactionMetaData().getTransactionAddedBy();
			if (transactionAddedBy == null || !userDetailsMap.containsKey(transactionAddedBy)) {
				reportCellDetailsRow.add(new ReportCellDetails(NA, STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
			} else {
				final User user = userDetailsMap.get(transactionAddedBy);
				reportCellDetailsRow.add(new ReportCellDetails(user.getFullName(), STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			}

			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
					.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR,
					WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
					.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR,
					WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

			reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
					studentFeePaymentTransactionDetails.getStudent().getStudentStatus()),
					STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
					.getStudentFamilyInfo().getFathersName(), STRING, CONTENT_SIZE, BLACK_COLOR,
					WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

			reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
					.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

			String bankAccount = "";
			if(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountId() != null){
				InstituteBankAccountDetails bankAccountDetails = bankAccountDetailsMap.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountId());
				bankAccount = StringUtils.isEmpty(bankAccountDetails.getAccountNumber()) ? bankAccountDetails.getBankName() : bankAccountDetails.getAccountNumber() +" ("+ bankAccountDetails.getBankName() +")";
			}
			reportCellDetailsRow.add(new ReportCellDetails(bankAccount,
					STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));


			Double totalTransactionAmount = 0.0d;
			for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
				Double feeHeadAmount = studentTransactionFeeHeadAggregates
						.get(studentFeePaymentTransactionDetails.getStudent().getStudentId())
						.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
								.getFeePaymentTransactionMetaData().getTransactionId())
						.get(feeHeadConfiguration.getFeeHeadId());
				feeHeadAmount = feeHeadAmount == null ? 0d : feeHeadAmount;

				reportCellDetailsRow.add(new ReportCellDetails(feeHeadAmount == null ? EMPTY_TEXT :
						NumberUtils.formatToIndianCurrency(feeHeadAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
						WHITE_COLOR, false));
				totalTransactionAmount += feeHeadAmount;
				totalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(),
						NumberUtils.addValues(totalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId()), feeHeadAmount));
			}

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
					studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getTotalFineAmount()),
					STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, false));

			totalFineAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getTotalFineAmount(), 0d);

			totalTransactionAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getTotalFineAmount(), 0d);

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTransactionAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
					WHITE_COLOR, false));
			grandTotal += totalTransactionAmount;

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
					studentFeePaymentTransactionDetails
							.getFeePaymentTransactionDetails().getTotalInstantDiscountAmount()), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, false));

			totalInstantDiscount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
					.getTotalInstantDiscountAmount(), 0d);

			count++;
			reportCellDetails.add(reportCellDetailsRow);
			rowNum++;
		}

		List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
		reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;
		reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		colSpan++;

		for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
			final Double feeHeadAmount = totalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId());
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
					feeHeadAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
		}

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalFineAmount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(grandTotal), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInstantDiscount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		rowNum++;
		reportCellDetails.add(reportCellDetailsTotalRow);

		List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
		cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
		mergeCellIndexesList.add(cellIndexes);

		List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

		ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
				totalColumns, headerMergeCellIndexesList,
				mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
				true, institute, totalColumns);

		reportSheetDetailsList.add(reportSheetDetails);
		return new ReportDetails(reportName, reportSheetDetailsList);
	}
	public Set<TransactionMode> getTransactionMode(String transactionModeStr){
		Set<TransactionMode> transactionMode = new HashSet<TransactionMode>();
		if(org.apache.commons.lang.StringUtils.isEmpty(transactionModeStr)){
			TransactionMode[] status = TransactionMode.values();
			for(TransactionMode transactionmode: status){
				transactionMode.add(transactionmode);
			}
		}
		else
		{
			String[] status = transactionModeStr.split(",");
			for(String stats : status){
				if(StringUtils.isBlank(stats)){
					continue;
				}
				transactionMode.add(TransactionMode.getTransactionMode(stats.trim()));
			}
		}
		return transactionMode;
	}

	private ReportDetails generateTransactionSummaryReport(Institute institute, String reportName, String sheetName, AcademicSession academicSession,
														   Map<UUID, FeeConfigurationBasicInfo> feeConfigurationBasicInfoMap,
														   Map<Integer, FeeHeadConfiguration> feeheadMap, Map<UUID, User> userDetailsMap,
														   TreeMap<Integer, List<StudentFeePaymentTransactionDetails>> studentFeePaymentTransactionDetailsMap,
														   Map<UUID, Map<UUID, Map<Integer, Double>>> studentTransactionFeeHeadAggregates,
														   Map<UUID, List<Integer>> requiredStandardSections, Set<String> requiredHeaderAttributes, Set<TransactionMode> transactionModeSet,
														   FeesReportType feesReportType) {

		final List<FeeHeadConfiguration> feeHeadConfigurations = new ArrayList<>(feeheadMap.values());
		List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
		/**
		 * In reportDetails object the first line of 2d list contain the heading this is mandatory
		 */
		List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
		String heading = academicSession == null ? reportName + "(All Session)" : reportName + " (" + academicSession.getDisplayName() + ") ";
		reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
		headerReportCellDetails.add(reportHeaderRow);

		List<ReportHeaderAttribute> reportHeaderAttributeList = REPORT_HEADERS.get(feesReportType.name());
		if(CollectionUtils.isEmpty(reportHeaderAttributeList)) {
			logger.info("Empty report {}", reportName);
			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null, null);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);
		}

		int dayWiseLength = 0;
		int totalColumns = 0;
		int index = 0;
		List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
		for(ReportHeaderAttribute reportHeaderAttribute : reportHeaderAttributeList) {
			/**
			 * adding this as we want remark to be the very end of report
			 */
			if(index == reportHeaderAttributeList.size() - 1) {
				break;
			}
			if (requiredHeaderAttributes.contains(reportHeaderAttribute.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				dayWiseLength++;
			}
			index++;
		}

		final Map<Integer, Double> totalFeeHeadAmount = new HashMap<>();
		final Map<Integer, Double> dayWiseTotalFeeHeadAmount = new HashMap<>();
		for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
			reportCellDetailsHeaderRow.add(new ReportCellDetails(feeHeadConfiguration.getFeeHead(), STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			totalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(), 0d);
			dayWiseTotalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(), 0d);
			totalColumns++;
		}

		reportCellDetailsHeaderRow.add(new ReportCellDetails("Fine Amount", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;
		reportCellDetailsHeaderRow.add(new ReportCellDetails("Grand Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;
		reportCellDetailsHeaderRow.add(new ReportCellDetails("Total Instant Discount", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		totalColumns++;
		if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
			reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.REMARKS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			totalColumns++;
		}

		headerReportCellDetails.add(reportCellDetailsHeaderRow);

		List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
		CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
		headerMergeCellIndexesList.add(cellIndexes);

		List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

		List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
		int rowNum = 1;
		int colSpan = 0;
		int count = 1;
		double totalFineAmount = 0d;
		double totalInstantDiscount = 0d;
		double grandTotal = 0d;
		for (final Entry<Integer, List<StudentFeePaymentTransactionDetails>> studentFeePaymentTransactionDetailsEntry : studentFeePaymentTransactionDetailsMap.entrySet()) {
			Integer transactionDate = studentFeePaymentTransactionDetailsEntry.getKey();
			Map<Integer, Double> dayLevelTotalFeeHeadAmount = new HashMap<>(dayWiseTotalFeeHeadAmount);
			double dayLevelTotalFineAmount = 0d;
			double dayLevelTotalInstantDiscount = 0d;
			double dayLevelGrandTotal = 0d;
			colSpan = 0;
			for (StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails : studentFeePaymentTransactionDetailsEntry.getValue()) {

				/**
				 * * Removing non required standards and sections
				 */

				final UUID standardId = studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
				final Integer sectionId = CollectionUtils.isEmpty(studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null : studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();

				if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
					continue;
				}
				if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
						!requiredStandardSections.get(standardId).contains(sectionId)) {
					continue;
				}
				if (!CollectionUtils.isEmpty(transactionModeSet) && !transactionModeSet.contains(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionMode())) {
					continue;
				}
				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_DATE.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(transactionDate,
							DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_ADDED_AT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(studentFeePaymentTransactionDetails
									.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionAddedAt(),
							DateUtils.DEFAULT_DATE_TIME_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_MODE.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionModeDisplayName(),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getFeePaymentTransactionMetaData().getInvoiceId(), STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_BY.getKey())) {
					final UUID transactionAddedBy = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionAddedBy();
					if (transactionAddedBy == null || !userDetailsMap.containsKey(transactionAddedBy)) {
						reportCellDetailsRow.add(new ReportCellDetails(NA, STRING,
								CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					} else {
						final User user = userDetailsMap.get(transactionAddedBy);
						reportCellDetailsRow.add(new ReportCellDetails(user.getFullName(), STRING,
								CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getRegistrationNumber(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							studentFeePaymentTransactionDetails.getStudent().getStudentStatus()),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentFamilyInfo().getFathersName(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.BANK_ACCOUNT.getKey())) {
					String bankAccount = "";
					if(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountDetails() != null){
						InstituteBankAccountDetails bankAccountDetails = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountDetails();
						bankAccount = StringUtils.isEmpty(bankAccountDetails.getAccountNumber()) ? bankAccountDetails.getBankName() : bankAccountDetails.getAccountNumber() +" ("+ bankAccountDetails.getBankName() +")";
					}
					reportCellDetailsRow.add(new ReportCellDetails(bankAccount,
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				Double totalTransactionAmount = 0.0d;
				for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
					Double feeHeadAmount = studentTransactionFeeHeadAggregates
							.get(studentFeePaymentTransactionDetails.getStudent().getStudentId())
							.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
									.getFeePaymentTransactionMetaData().getTransactionId())
							.get(feeHeadConfiguration.getFeeHeadId());
					feeHeadAmount = feeHeadAmount == null ? 0d : feeHeadAmount;

					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(feeHeadAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false));
					totalTransactionAmount += feeHeadAmount;
					totalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(),
							NumberUtils.addValues(totalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId()), feeHeadAmount));

					dayLevelTotalFeeHeadAmount.put(feeHeadConfiguration.getFeeHeadId(),
							NumberUtils.addValues(dayLevelTotalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId()), feeHeadAmount));
				}

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getTotalFineAmount()),
						STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				totalFineAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				dayLevelTotalFineAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				totalTransactionAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTransactionAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
						WHITE_COLOR, false));
				grandTotal += totalTransactionAmount;
				dayLevelGrandTotal += totalTransactionAmount;

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						studentFeePaymentTransactionDetails
								.getFeePaymentTransactionDetails().getTotalInstantDiscountAmount()), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, false));

				totalInstantDiscount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalInstantDiscountAmount(), 0d);
				dayLevelTotalInstantDiscount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalInstantDiscountAmount(), 0d);


				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails
							.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getRemark(),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				count++;
				reportCellDetails.add(reportCellDetailsRow);
				rowNum++;
			}

			if(dayLevelGrandTotal > 0) {
				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				for (int i = 1; i < dayWiseLength; i++) {
					dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}


				for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
					final Double feeHeadAmount = dayLevelTotalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId());
					dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							feeHeadAmount), STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
				}

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTotalFineAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelGrandTotal), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTotalInstantDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
					dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
				}
				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);
				rowNum++;

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
			}
		}

		colSpan = 0;
		List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
		reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		for(int i = 1; i < dayWiseLength; i++) {
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
		}


		for (final FeeHeadConfiguration feeHeadConfiguration : feeHeadConfigurations) {
			final Double feeHeadAmount = totalFeeHeadAmount.get(feeHeadConfiguration.getFeeHeadId());
			reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
					feeHeadAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
		}

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalFineAmount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(grandTotal), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInstantDiscount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));
		if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
		}
		rowNum++;
		reportCellDetails.add(reportCellDetailsTotalRow);

		cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
		mergeCellIndexesList.add(cellIndexes);

		List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

		ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
				totalColumns, headerMergeCellIndexesList,
				mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
				true, institute, totalColumns);

		reportSheetDetailsList.add(reportSheetDetails);
		return new ReportDetails(reportName, reportSheetDetailsList);
	}

	private ReportDetails generateTransactionSummaryDetailedReport(Institute institute, String reportName, String sheetName, AcademicSession academicSession,
			   Map<UUID, FeeConfigurationBasicInfo> feeConfigurationBasicInfoMap, Map<Integer, FeeHeadConfiguration> feeheadMap, Map<UUID, User> userDetailsMap,
			   TreeMap<Integer, List<StudentFeePaymentTransactionDetails>> studentFeePaymentTransactionDetailsMap,
			   Map<UUID, Map<UUID, Map<UUID, Map<Integer, Double>>>> studentTransactionFeeIdFeeHeadAggregates,
			   Map<UUID, Set<Integer>> feeIdFeeHeadIdMap, Map<UUID, List<Integer>> requiredStandardSections, Set<String> requiredHeaderAttributes,
			   Set<TransactionMode> transactionModeSet, FeesReportType feesReportType) {

		int totalColumns = 0;
		List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();

		/**
		 * In reportDetails object the first line of 2d list contain the heading this is mandatory
		 */
		List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
		String heading = academicSession == null ? reportName + "(All Session)" : reportName + " (" + academicSession.getDisplayName() + ") ";
		reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
		headerReportCellDetails.add(reportHeaderRow);

		List<ReportHeaderAttribute> reportHeaderAttributeList = REPORT_HEADERS.get(feesReportType.name());
		if(CollectionUtils.isEmpty(reportHeaderAttributeList)) {
			logger.info("Empty report {}", reportName);
			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null, null);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);
		}

		int dayWiseLength = 0;
		List<ReportCellDetails> reportCellDetailsHeaderRow1 = new ArrayList<ReportCellDetails>();
		List<ReportCellDetails> reportCellDetailsHeaderRow2 = new ArrayList<ReportCellDetails>();

		List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();

		int index = 0;
		for(ReportHeaderAttribute reportHeaderAttribute : reportHeaderAttributeList) {
			/**
			 * adding this as we want remark to be the very end of report
			 */
			if(index == reportHeaderAttributeList.size() - 1) {
				break;
			}
			if (requiredHeaderAttributes.contains(reportHeaderAttribute.getKey())) {

				reportCellDetailsHeaderRow1.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

				CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
				headerMergeCellIndexesList.add(cellIndexes);

				totalColumns += 1;
				dayWiseLength++;

			}
			index++;
		}

		final Map<UUID, Map<Integer, Double>> totalFeeIdFeeHeadAmount = new HashMap<>();
		final Map<UUID, Map<Integer, Double>> dayWiseTotalFeeIdFeeHeadAmount = new HashMap<>();
		for (final Entry<UUID, Set<Integer>> feeIdFeeHeadIdEntry : feeIdFeeHeadIdMap.entrySet()) {
			UUID feeId = feeIdFeeHeadIdEntry.getKey();
			String feeName = feeConfigurationBasicInfoMap.get(feeId).getFeeName();
			Set<Integer> feeHeadIdSet = feeIdFeeHeadIdEntry.getValue();
			for (final Integer feeHeadId : feeHeadIdSet) {
				String feeHeadName = feeheadMap.get(feeHeadId).getFeeHead();
				reportCellDetailsHeaderRow1.add(new ReportCellDetails(feeName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(feeHeadName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalFeeIdFeeHeadAmount.put(feeId, new HashMap<>());
				totalFeeIdFeeHeadAmount.get(feeId).put(feeHeadId, 0d);
				dayWiseTotalFeeIdFeeHeadAmount.put(feeId, new HashMap<>());
				dayWiseTotalFeeIdFeeHeadAmount.get(feeId).put(feeHeadId, 0d);
				totalColumns++;
			}

			if(feeHeadIdSet.size() > 1) {
				CellIndexes cellIndexes = new CellIndexes(1, 1, totalColumns - feeHeadIdSet.size(), totalColumns - 1);
				headerMergeCellIndexesList.add(cellIndexes);
			}

		}

		reportCellDetailsHeaderRow1.add(new ReportCellDetails("Fine Amount", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		CellIndexes cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
		headerMergeCellIndexesList.add(cellIndexes);
		totalColumns++;
		reportCellDetailsHeaderRow1.add(new ReportCellDetails("Grand Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
		headerMergeCellIndexesList.add(cellIndexes);
		totalColumns++;
		reportCellDetailsHeaderRow1.add(new ReportCellDetails("Total Instant Discount", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
		cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
		headerMergeCellIndexesList.add(cellIndexes);
		totalColumns++;
		if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
			reportCellDetailsHeaderRow1.add(new ReportCellDetails(ReportHeaderAttribute.REMARKS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsHeaderRow2.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			cellIndexes = new CellIndexes(1, 2, totalColumns, totalColumns);
			headerMergeCellIndexesList.add(cellIndexes);
			totalColumns++;
		}

		headerReportCellDetails.add(reportCellDetailsHeaderRow1);
		headerReportCellDetails.add(reportCellDetailsHeaderRow2);

		cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
		headerMergeCellIndexesList.add(cellIndexes);

		List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

		List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
		int rowNum = 2;
		int colSpan = 0;
		int count = 1;
		double totalFineAmount = 0d;
		double totalInstantDiscount = 0d;
		double grandTotal = 0d;
		for (final Entry<Integer, List<StudentFeePaymentTransactionDetails>> studentFeePaymentTransactionDetailsEntry : studentFeePaymentTransactionDetailsMap.entrySet()) {
			Integer transactionDate = studentFeePaymentTransactionDetailsEntry.getKey();

			Map<UUID, Map<Integer, Double>> dayLevelTotalFeeHeadAmount = new HashMap<>();
			for (final Entry<UUID, Set<Integer>> feeIdFeeHeadIdEntry : feeIdFeeHeadIdMap.entrySet()) {
				UUID feeId = feeIdFeeHeadIdEntry.getKey();
				Set<Integer> feeHeadIdSet = feeIdFeeHeadIdEntry.getValue();
				for (final Integer feeHeadId : feeHeadIdSet) {
					dayLevelTotalFeeHeadAmount.put(feeId, new HashMap<>());
					dayLevelTotalFeeHeadAmount.get(feeId).put(feeHeadId, 0d);
				}
			}

			double dayLevelTotalFineAmount = 0d;
			double dayLevelTotalInstantDiscount = 0d;
			double dayLevelGrandTotal = 0d;
			colSpan = 0;
			for (StudentFeePaymentTransactionDetails studentFeePaymentTransactionDetails : studentFeePaymentTransactionDetailsEntry.getValue()) {

				/**
				 * * Removing non required standards and sections
				 */

				final UUID standardId = studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
				final Integer sectionId = CollectionUtils.isEmpty(studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null : studentFeePaymentTransactionDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();

				if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
					continue;
				}
				if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
						!requiredStandardSections.get(standardId).contains(sectionId)) {
					continue;
				}
				if (!CollectionUtils.isEmpty(transactionModeSet) && !transactionModeSet.contains(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionMode())) {
					continue;
				}
				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_DATE.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(transactionDate,
							DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_ADDED_AT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(studentFeePaymentTransactionDetails
									.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getTransactionAddedAt(),
							DateUtils.DEFAULT_DATE_TIME_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_MODE.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionModeDisplayName(),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.INVOICE_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getInvoiceId(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.TRANSACTION_BY.getKey())) {
					final UUID transactionAddedBy = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
							.getFeePaymentTransactionMetaData().getTransactionAddedBy();
					if (transactionAddedBy == null || !userDetailsMap.containsKey(transactionAddedBy)) {
						reportCellDetailsRow.add(new ReportCellDetails(NA, STRING,
								CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					} else {
						final User user = userDetailsMap.get(transactionAddedBy);
						reportCellDetailsRow.add(new ReportCellDetails(user.getFullName(), STRING,
								CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getRegistrationNumber(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentBasicInfo().getName(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							studentFeePaymentTransactionDetails.getStudent().getStudentStatus()),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentFamilyInfo().getFathersName(), STRING, CONTENT_SIZE, BLACK_COLOR,
							WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails.getStudent()
							.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.BANK_ACCOUNT.getKey())) {
					String bankAccount = "";
					if(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountDetails() != null){
						InstituteBankAccountDetails bankAccountDetails = studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getBankAccountDetails();
						bankAccount = StringUtils.isEmpty(bankAccountDetails.getAccountNumber()) ? bankAccountDetails.getBankName() : bankAccountDetails.getAccountNumber() +" ("+ bankAccountDetails.getBankName() +")";
					}
					reportCellDetailsRow.add(new ReportCellDetails(bankAccount,
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}

				Double totalTransactionAmount = 0.0d;
				for (final Entry<UUID, Set<Integer>> feeIdFeeHeadIdEntry : feeIdFeeHeadIdMap.entrySet()) {
					UUID feeId = feeIdFeeHeadIdEntry.getKey();
					String feeName = feeConfigurationBasicInfoMap.get(feeId).getFeeName();
					Set<Integer> feeHeadIdSet = feeIdFeeHeadIdEntry.getValue();
					for (final Integer feeHeadId : feeHeadIdSet) {
						String feeHeadName = feeheadMap.get(feeHeadId).getFeeHead();
						Double feeHeadAmount = CollectionUtils.isEmpty(studentTransactionFeeIdFeeHeadAggregates
								.get(studentFeePaymentTransactionDetails.getStudent().getStudentId())
								.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
										.getFeePaymentTransactionMetaData().getTransactionId())) ? null :
								CollectionUtils.isEmpty(studentTransactionFeeIdFeeHeadAggregates
										.get(studentFeePaymentTransactionDetails.getStudent().getStudentId())
										.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
												.getFeePaymentTransactionMetaData().getTransactionId()).get(feeId)) ? null :
								studentTransactionFeeIdFeeHeadAggregates
								.get(studentFeePaymentTransactionDetails.getStudent().getStudentId())
								.get(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
										.getFeePaymentTransactionMetaData().getTransactionId())
								.get(feeId).get(feeHeadId);

						feeHeadAmount = feeHeadAmount == null ? 0d : feeHeadAmount;

						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(feeHeadAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
								WHITE_COLOR, false));
						totalTransactionAmount += feeHeadAmount;

						totalFeeIdFeeHeadAmount.get(feeId).put(feeHeadId, NumberUtils.addValues(totalFeeIdFeeHeadAmount.get(feeId).get(feeHeadId), feeHeadAmount));

						dayLevelTotalFeeHeadAmount.get(feeId).put(feeHeadId, NumberUtils.addValues(dayLevelTotalFeeHeadAmount.get(feeId).get(feeHeadId), feeHeadAmount));
					}
				}

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails().getTotalFineAmount()),
						STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				totalFineAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				dayLevelTotalFineAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				totalTransactionAmount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalFineAmount(), 0d);

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalTransactionAmount), STRING, CONTENT_SIZE, BLACK_COLOR,
						WHITE_COLOR, false));
				grandTotal += totalTransactionAmount;
				dayLevelGrandTotal += totalTransactionAmount;

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						studentFeePaymentTransactionDetails
								.getFeePaymentTransactionDetails().getTotalInstantDiscountAmount()), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, false));

				totalInstantDiscount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalInstantDiscountAmount(), 0d);
				dayLevelTotalInstantDiscount += Math.max(studentFeePaymentTransactionDetails.getFeePaymentTransactionDetails()
						.getTotalInstantDiscountAmount(), 0d);

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentTransactionDetails
							.getFeePaymentTransactionDetails().getFeePaymentTransactionMetaData().getRemark(),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}


				count++;
				reportCellDetails.add(reportCellDetailsRow);
				rowNum++;
			}

			if(dayLevelGrandTotal > 0) {
				List<ReportCellDetails> dayWiseTotalReportCellDetailsRow = new ArrayList<ReportCellDetails>();
				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails("Daywise " + TOTAL + " for: " + DateUtils.getFormattedDate(transactionDate,
						DATE_FORMAT, User.DFAULT_TIMEZONE), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				for (int i = 1; i < dayWiseLength; i++) {
					dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}


				for (final Entry<UUID, Set<Integer>> feeIdFeeHeadIdEntry : feeIdFeeHeadIdMap.entrySet()) {
					UUID feeId = feeIdFeeHeadIdEntry.getKey();
					Set<Integer> feeHeadIdSet = feeIdFeeHeadIdEntry.getValue();
					for (final Integer feeHeadId : feeHeadIdSet) {
						final Double feeHeadAmount = dayLevelTotalFeeHeadAmount.get(feeId).get(feeHeadId);
						dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								feeHeadAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
					}
				}

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTotalFineAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelGrandTotal), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dayLevelTotalInstantDiscount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
					dayWiseTotalReportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
							BLACK_COLOR, WHITE_COLOR, true));
				}
				reportCellDetails.add(dayWiseTotalReportCellDetailsRow);
				rowNum++;

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);
			}
		}

		colSpan = 0;
		List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();
		reportCellDetailsTotalRow.add(new ReportCellDetails(TOTAL, STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		for( int i = 1 ; i < dayWiseLength ; i++ ){
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
		}

		for (final Entry<UUID, Set<Integer>> feeIdFeeHeadIdEntry : feeIdFeeHeadIdMap.entrySet()) {
			UUID feeId = feeIdFeeHeadIdEntry.getKey();
			String feeName = feeConfigurationBasicInfoMap.get(feeId).getFeeName();
			Set<Integer> feeHeadIdSet = feeIdFeeHeadIdEntry.getValue();
			for (final Integer feeHeadId : feeHeadIdSet) {
				String feeHeadName = feeheadMap.get(feeHeadId).getFeeHead();
				final Double feeHeadAmount = totalFeeIdFeeHeadAmount.get(feeId).get(feeHeadId);
				reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						feeHeadAmount), STRING, CONTENT_SIZE,
						BLACK_COLOR, WHITE_COLOR, true));
			}
		}

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalFineAmount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(grandTotal), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInstantDiscount), STRING, CONTENT_SIZE,
				BLACK_COLOR, WHITE_COLOR, true));

		if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REMARKS.getKey())) {
			reportCellDetailsTotalRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
		}
		rowNum++;
		reportCellDetails.add(reportCellDetailsTotalRow);

		cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
		mergeCellIndexesList.add(cellIndexes);

		List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

		ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, false,
				totalColumns, headerMergeCellIndexesList,
				mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
				true, institute, totalColumns);

		reportSheetDetailsList.add(reportSheetDetails);
		return new ReportDetails(reportName, reportSheetDetailsList);
	}

	private ReportDetails generateStudentLevelAggregatedReport(int instituteId,  Integer academicSessionId, FeeReportDataType feeReportDataType, Map<UUID, List<Integer>> requiredStandardSections, Integer feeDueDate,
		Set<UUID> feeIds, Set<Integer> feeHeadIds, Set<StudentStatus> studentStatuses, Set<String> requiredHeaderAttributes, FeesReportType feesReportType, boolean hideStudentWithZeroFeesAssignment) {

		String sheetName = "AggregatedFeesReport";
		String reportName = "Aggregated Fees Report";

		switch(feeReportDataType) {
			case ALL:
				sheetName = "AggregatedFeesReport";
				reportName = "Aggregated Fees Report";
				break;
			case ONLY_DUE_STUDENTS:
				sheetName = "AggregatedFeesDueReport";
				reportName = "Aggregated Fees Due Report";
				break;
			case ONLY_NON_DUE_STUDENTS:
				sheetName = "AggregatedFeesNonDueReport";
				reportName = "Aggregated Fees Non Due Report";
				break;
		}

		List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<>();
		AcademicSession academicSession = null;
		if(academicSessionId != null) {
			academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
		}
		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			Set<UUID> standards = requiredStandardSections.keySet();

			List<Student> studentList = studentManager.getStudentsByStandardIds(instituteId, academicSessionId, standards, (CollectionUtils.isEmpty(studentStatuses)) ?  new HashSet<>(Arrays.asList(StudentStatus.getActiveStatuesArr())) : studentStatuses);

			if(CollectionUtils.isEmpty(studentList)) {
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
					ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,null,
						null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
			}

			final List<StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDatas = feePaymentInsightManager
					.getStudentPaymentStats(instituteId, academicSessionId, feeDueDate, feeIds, feeHeadIds, standards,
							studentStatuses);

			Map<UUID, StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDataMap = new HashMap<>();
			if(!CollectionUtils.isEmpty(studentFeePaymentAggregatedDatas)) {
				studentFeePaymentAggregatedDataMap = EMapUtils.getMap(studentFeePaymentAggregatedDatas, new EMapUtils.MapFunction<StudentFeePaymentAggregatedData, UUID, StudentFeePaymentAggregatedData>() {
					@Override
					public UUID getKey(StudentFeePaymentAggregatedData entry) {
						return entry.getStudentId();
					}

					@Override
					public StudentFeePaymentAggregatedData getValue(StudentFeePaymentAggregatedData entry) {
						return entry;
					}
				});
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

			int totalColumns = 0;

			List<ReportHeaderAttribute> reportHeaderAttributeList = REPORT_HEADERS.get(feesReportType.name());
			if(CollectionUtils.isEmpty(reportHeaderAttributeList)) {
				logger.info("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null, null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			for(ReportHeaderAttribute reportHeaderAttribute : reportHeaderAttributeList) {
				if (requiredHeaderAttributes.contains(reportHeaderAttribute.getKey())) {
					reportCellDetailsHeaderRow.add(new ReportCellDetails(reportHeaderAttribute.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
					totalColumns += 1;
				}
			}

			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			double totalAssignedAmount = 0d;
			double totalAmountCollected = 0d;
			double totalGivenDiscount = 0d;
			double totalDiscountToBeGiven = 0d;
			double totalDueAmount = 0d;
			double totalPaidFineAmount = 0d;
			double totalDueFineAmount = 0d;
			int rowNum = 1;
			int colSpan = 0;
			int count = 1;
			for (final Student student : studentList) {
				StudentFeePaymentAggregatedData studentFeePaymentAggregatedData = studentFeePaymentAggregatedDataMap.get(student.getStudentId());
				if (!CollectionUtils.isEmpty(studentStatuses) &&
						!studentStatuses.contains(student.getStudentStatus())) {
					continue;
				}
				/**
				 * * Removing non required standards and sections
				 */

				final UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
				final Integer sectionId = CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList()) ? null :
						student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();

				if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
					continue;
				}
				if (!CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
						!requiredStandardSections.get(standardId).contains(sectionId)) {
					continue;
				}

				double assignedAmount = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getAssignedAmount();
				double amountCollected = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getAmountCollected();
				double givenDiscount = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getGivenDiscount();
				double remainingDiscountToBeGiven = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getRemainingDiscountToBeGiven();
				double dueAmount = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getDueAmount();
				double paidFineAmount = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getPaidFineAmount();
				double dueFineAmount = studentFeePaymentAggregatedData == null ? 0 : studentFeePaymentAggregatedData.getDueFineAmount();

				if(student.getStudentStatus() == StudentStatus.NSO
						|| student.getStudentStatus() == StudentStatus.RELIEVED
						|| student.getStudentStatus() == StudentStatus.DELETED) {
					assignedAmount = NumberUtils.addValues(amountCollected, givenDiscount);
					remainingDiscountToBeGiven = 0d;
					dueAmount = 0d;
					dueFineAmount = 0d;
				}
				if (hideStudentWithZeroFeesAssignment && assignedAmount <= 0) {
					continue;
				}
				if (feeReportDataType == FeeReportDataType.ONLY_DUE_STUDENTS && dueAmount <= 0) {
					continue;
				}

				if (feeReportDataType == FeeReportDataType.ONLY_NON_DUE_STUDENTS && dueAmount > 0) {
					continue;
				}

				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getRegistrationNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getAdmissionNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getName(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							student.getStudentStatus()), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey()))  {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse()
							.getStandard().getDisplayNameWithSection(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentFamilyInfo().getFathersName(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getPrimaryContactNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentBasicInfo().getWhatsappNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentFamilyInfo().getFathersContactNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(student.getStudentFamilyInfo().getMothersContactNumber(), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							assignedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalAssignedAmount += assignedAmount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_AMOUNT_COLLECTED.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							amountCollected), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalAmountCollected += amountCollected;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							givenDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalGivenDiscount += givenDiscount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							remainingDiscountToBeGiven), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalDiscountToBeGiven += remainingDiscountToBeGiven;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dueAmount), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalDueAmount += dueAmount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_PAID_FINE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(paidFineAmount), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalPaidFineAmount += paidFineAmount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_FINE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dueFineAmount), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalDueFineAmount += dueFineAmount;
				}
				count++;
				reportCellDetails.add(reportCellDetailsRow);
				rowNum++;
			}



			if(count > 1) {
				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
				reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
						true));
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE + 1,
							BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalAssignedAmount), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_AMOUNT_COLLECTED.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalAmountCollected), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalGivenDiscount), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalDiscountToBeGiven), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalDueAmount), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_PAID_FINE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalPaidFineAmount), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}

				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_FINE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalDueFineAmount), STRING, CONTENT_SIZE + 1, BLACK_COLOR, WHITE_COLOR,
							true));
				}
				rowNum++;
				reportCellDetails.add(reportCellDetailsRow);
			}

			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

			cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
					totalColumns);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}
		return null;
	}

	private String getStandardDisplay(StudentFeePaymentAggregatedData studentFeePaymentAggregatedData) {
		return getStandardDisplay(studentFeePaymentAggregatedData.getStandardName(),
				studentFeePaymentAggregatedData.getStream(), studentFeePaymentAggregatedData.getSectionName());
	}

	private ReportDetails generateStudentFeesLevelReport(int instituteId, Integer academicSessionId, FeeReportDataType feeReportDataType,
														 final Set<UUID> feeIds, Set<StudentStatus> studentStatuses, Map<UUID, List<Integer>> requiredStandardSections,
														 Integer feeDueDate) {

		String sheetName = "StudentFeesLevelReport";
		String reportName = "Student Fees Level";

		switch(feeReportDataType) {
			case ALL:
				sheetName = "StudentFeesLevelReport";
				reportName = "Student Fees Level";
				break;
			case ONLY_DUE_STUDENTS:
				sheetName = "StudentFeesLevelDueReport";
				reportName = "Student Fees Due Level";
				break;
			case ONLY_NON_DUE_STUDENTS:
				sheetName = "StudentFeesLevelNonDueReport";
				reportName = "Student Fees Non Due Level";
				break;
		}

		List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
		AcademicSession academicSession = null;
		if(academicSessionId != null) {
			academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
		}

		try {
			Set<UUID> requiredStandards = requiredStandardSections.keySet();
            final List<StudentFeePaymentData> studentFeePaymentDataList = feePaymentInsightManager.getStudentFeePaymentData(instituteId, academicSessionId, feeDueDate, feeIds,requiredStandards, null);

			if(CollectionUtils.isEmpty(studentFeePaymentDataList)) {
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
			}

			Institute institute = instituteManager.getInstitute(instituteId);
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, STUDENT_FEES_LEVEL_AGGREGATED_REPORT.length - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			for (int i = 0; i < STUDENT_FEES_LEVEL_AGGREGATED_REPORT.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(STUDENT_FEES_LEVEL_AGGREGATED_REPORT[i], STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			 // Create Other rows and cells with inventory data
			 int rowNum = 1;
			 int count = 1;
			 double totalAssignedAmount = 0;
			 double totalAmountCollected = 0;
			 double totalGivenDiscount = 0;
			 double totalDiscountToBeGiven = 0;
			 double totalDueAmount = 0;
			 double totalPaidFineAmount = 0;
			 double totalDueFineAmount = 0;

			 final Map<String, List<StudentFeePaymentData>> studentFeePaymentDataMap = getStudentLevelFeesListMap(studentFeePaymentDataList);
			 for (final Entry<String, List<StudentFeePaymentData>> studentFeePaymentDataEntry : studentFeePaymentDataMap.entrySet()) {
				 int numOfFees = 0;
				 double studentAssignedAmount = 0;
				 double studentAmountCollected = 0;
				 double studentGivenDiscount = 0;
				 double studentDiscountToBeGiven = 0;
				 double studentDueAmount = 0;
				 double studentPaidFineAmount = 0;
				 double studentDueFineAmount = 0;
				 String admissionNumber = "";
				 int colSpan = 0;
				 for (final StudentFeePaymentData studentFeePaymentData : studentFeePaymentDataEntry.getValue()) {
					 if (!CollectionUtils.isEmpty(studentStatuses) &&
							 !studentStatuses.contains(studentFeePaymentData.getStatus())) {
						 continue;
					 }

					 if (!CollectionUtils.isEmpty(feeIds)
							 && !feeIds.contains(studentFeePaymentData.getFeeId())) {
						 continue;
					 }

					 /**
					 * * Removing non required standards and sections
					 */

					final UUID standardId = studentFeePaymentData.getStandardId();
					final Integer sectionId = studentFeePaymentData.getSectionId();

					if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
						continue;
					}
					if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
							!requiredStandardSections.get(standardId).contains(sectionId)) {
						continue;
					}

					 admissionNumber = studentFeePaymentData.getAdmissionNumber();
					 double assignedAmount = studentFeePaymentData.getAssignedAmount();
					 double amountCollected = studentFeePaymentData.getAmountCollected();
					 double givenDiscount = studentFeePaymentData.getGivenDiscount();
					 double remainingDiscountToBeGiven = studentFeePaymentData.getRemainingDiscountToBeGiven();
					 double dueAmount = studentFeePaymentData.getDueAmount();
					 double paidFineAmount = studentFeePaymentData.getTotalPaidFineAmount();
					 double dueFineAmount = studentFeePaymentData.getTotalDueFineAmount();

					 if(studentFeePaymentData.getStatus() == StudentStatus.NSO
							 || studentFeePaymentData.getStatus() == StudentStatus.RELIEVED
							 || studentFeePaymentData.getStatus() == StudentStatus.DELETED) {
						 assignedAmount = NumberUtils.addValues(amountCollected, givenDiscount);
						 remainingDiscountToBeGiven = 0d;
						 dueAmount = 0d;
						 dueFineAmount = 0d;
					 }

					 if (feeReportDataType == FeeReportDataType.ONLY_DUE_STUDENTS && dueAmount <= 0) {
						 continue;
					 }

					 if (feeReportDataType == FeeReportDataType.ONLY_NON_DUE_STUDENTS && dueAmount > 0) {
						 continue;
					 }


					 List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

					 reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getRegistrationNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getAdmissionNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getStudentFullName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					 reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							 studentFeePaymentData.getStatus()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(getStandardDisplay(studentFeePaymentData), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getFatherName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getPrimaryContactNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getWhatsappNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getFatherContactNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getMotherContactNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentData.getFeeName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 assignedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalAssignedAmount += assignedAmount;
					 studentAssignedAmount += assignedAmount;


					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 amountCollected), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalAmountCollected += amountCollected;
					 studentAmountCollected += amountCollected;

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 givenDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalGivenDiscount += givenDiscount;
					 studentGivenDiscount += givenDiscount;

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 remainingDiscountToBeGiven), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalDiscountToBeGiven += remainingDiscountToBeGiven;
					 studentDiscountToBeGiven += remainingDiscountToBeGiven;

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 dueAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalDueAmount += dueAmount;
					 studentDueAmount += dueAmount;

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 paidFineAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalPaidFineAmount += paidFineAmount;
					 studentPaidFineAmount += paidFineAmount;

					 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							 dueFineAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					 totalDueFineAmount += dueFineAmount;
					 studentDueFineAmount += dueFineAmount;

					 numOfFees++;
					 rowNum++;
					 reportCellDetails.add(reportCellDetailsRow);
				}
				if (numOfFees > 0) {
					count++;
				}

				if (numOfFees <= 1) {
					continue;
				}

				for (int k = 0; k < 11; k++) {
					cellIndexes = new CellIndexes(rowNum - numOfFees + 1, rowNum , k, k);
					mergeCellIndexesList.add(cellIndexes);
				}

				 List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				 reportCellDetailsRow.add(new ReportCellDetails("Total (" + admissionNumber + ")", STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 colSpan++;
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentAssignedAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentAmountCollected), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentGivenDiscount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentDiscountToBeGiven), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentDueAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentPaidFineAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentDueFineAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
				 reportCellDetails.add(reportCellDetailsRow);
				 rowNum++;

				 cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				 mergeCellIndexesList.add(cellIndexes);

			}

			int colSpan = 0;
			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAssignedAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAmountCollected), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalGivenDiscount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountToBeGiven), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDueAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPaidFineAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDueFineAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetails.add(reportCellDetailsRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum,rowNum, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					STUDENT_FEES_LEVEL_AGGREGATED_REPORT.length, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
					STUDENT_FEES_LEVEL_AGGREGATED_REPORT.length);

			reportSheetDetailsList.add(reportSheetDetails);

			return new ReportDetails(reportName, reportSheetDetailsList);
		}
		catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}

		return null;
	}

	private Map<String, List<StudentFeePaymentData>> getStudentLevelFeesListMap(final List<StudentFeePaymentData> studentFeePaymentDataList) {

		final Map<String, List<StudentFeePaymentData>> studentFeePaymentDataMap = new LinkedHashMap<>();
		for (final StudentFeePaymentData studentFeePaymentData : studentFeePaymentDataList) {
			final String admissionNumber = studentFeePaymentData.getAdmissionNumber();
			if (!studentFeePaymentDataMap.containsKey(admissionNumber)) {
				studentFeePaymentDataMap.put(admissionNumber, new ArrayList<>());
			}
			studentFeePaymentDataMap.get(admissionNumber).add(studentFeePaymentData);
		}
		return studentFeePaymentDataMap;
	}

	private ReportDetails generateFeeHeadLevelCollectedAmount(int instituteId, Integer academicSessionId, int start, int end, AggMode aggMode,
															  Set<StudentStatus> studentStatuses) {

		final String sheetName = "FeesCollectionReport (Head Wise)";
		final String reportName = "Fees Collection Report (Head Wise)";
		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			final Map<Integer, Map<Integer, FeeHeadTransactionAmountsDetails>> aggregatedFeeHeadAmounts = feePaymentInsightManager.getAggregatedFeeHeadPaymentAmount(instituteId, academicSessionId, start, end, aggMode, studentStatuses);
			if (CollectionUtils.isEmpty(aggregatedFeeHeadAmounts)) {
					logger.error("Empty report {}", reportName);
					List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
					ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
					reportSheetDetailsList.add(reportSheetDetails);
					ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
					return reportDetails;
			}

			final List<FeeHeadConfigurationResponse> feeHeadConfigurationResponses = feeConfigurationManager.getFeeHeadConfiguration(instituteId);
			final Map<Integer, FeeHeadConfigurationResponse> feeHeads = new TreeMap<>();
			for (final FeeHeadConfigurationResponse feeHeadConfigurationResponse : feeHeadConfigurationResponses) {
				final int feeHeadId = feeHeadConfigurationResponse.getFeeHeadConfiguration().getFeeHeadId();
				if (feeHeads.containsKey(feeHeadId)) {
					continue;
				}
				feeHeads.put(feeHeadId, feeHeadConfigurationResponse);
			}
			AcademicSession academicSession = null;
			if(academicSessionId != null) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();


			int headerCount = 0;
			reportCellDetailsHeaderRow.add(new ReportCellDetails("S.No", STRING,
			CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
			true));
			headerCount++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Date", STRING,
			CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
			true));
			headerCount++;


            for (final Entry<Integer, FeeHeadConfigurationResponse> feeHeadEntry : feeHeads.entrySet()) {
				headerCount++;
				final String feeHeadNameHeader = feeHeadEntry.getValue().getFeeHeadConfiguration().getFeeHead();
				reportCellDetailsHeaderRow.add(new ReportCellDetails(feeHeadNameHeader, STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, headerCount - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
            int count = 1;
            final Map<Integer, Double> feeHeadSum = new HashMap<>();
            for (final Entry<Integer, Map<Integer, FeeHeadTransactionAmountsDetails>> entry : aggregatedFeeHeadAmounts
                    .entrySet()) {
						List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
						reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
						false));

						reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(entry.getKey(), DATE_FORMAT, User.DFAULT_TIMEZONE), STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
						false));

						for (final Entry<Integer, FeeHeadConfigurationResponse> feeHeadEntry : feeHeads.entrySet()) {
							final int feeHeadId = feeHeadEntry.getKey();
							if (!entry.getValue().containsKey(feeHeadId)) {
								reportCellDetailsRow.add(new ReportCellDetails(0, INTEGER,
								CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
								false));
								continue;
							}
							final double paidAmount = entry.getValue().get(feeHeadId).getPaidAmount();
							reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(paidAmount), STRING,
							CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
							false));

							if (!feeHeadSum.containsKey(feeHeadId)) {
								feeHeadSum.put(feeHeadId, paidAmount);
							}
							else {
								feeHeadSum.put(feeHeadId, NumberUtils.addValues(feeHeadSum.get(feeHeadId), paidAmount));
							}
						}
					count++;
					reportCellDetails.add(reportCellDetailsRow);
				}

			List<ReportCellDetails> reportCellDetailsTotalRow = new ArrayList<ReportCellDetails>();


			reportCellDetailsTotalRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE,
			BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsTotalRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE,
			BLACK_COLOR, WHITE_COLOR, true));

			for (final Entry<Integer, FeeHeadConfigurationResponse> feeHeadEntry : feeHeads.entrySet()) {
				final int feeHeadId = feeHeadEntry.getKey();
				if (!feeHeadSum.containsKey(feeHeadId)) {
					final Double amountValue = 0d;
					reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(amountValue), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
					continue;
				}
				final double totalPaidAmount = feeHeadSum.get(feeHeadId);
				reportCellDetailsTotalRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPaidAmount), STRING, CONTENT_SIZE,
					BLACK_COLOR, WHITE_COLOR, true));
					continue;
			}
			reportCellDetails.add(reportCellDetailsTotalRow);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					headerCount, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, headerCount);

				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
			e.printStackTrace();
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
	}

	private ReportDetails generateUserWiseDayLevelCollectionReport(int instituteId, Set<Integer> multipleSessionIds, Integer start,Integer end, UUID userId) {

		final User user = userManager.getUser(userId);
        boolean allUsers = false;
        if (user.getUserType() == UserType.ADMIN || user.getUserType() == UserType.INTERNAL) {
            allUsers = true;
        }
        final String reportName = "User Day Level Collection";
		final String sheetName = "UserDayLevelCollectionReport";

		try {
			Institute institute = instituteManager.getInstitute(instituteId);
            List<FeePaymentTransactionDetails> transactionDetailsList = new ArrayList<>();
			for(Integer academicSessionId : multipleSessionIds){
				List<FeePaymentTransactionDetails> transactionDetails = null;
				if (allUsers) {
					transactionDetails = feePaymentInsightManager.getTransactionDetails(instituteId, academicSessionId,
							start, end);
					if(transactionDetails != null){
						transactionDetailsList.addAll(transactionDetails);
					}
				} else {
					transactionDetails = feePaymentInsightManager.getTransactionDetails(instituteId, academicSessionId,
							start, end, userId);
					if(transactionDetails != null){
						transactionDetailsList.addAll(transactionDetails);
					}
				}
			}

			final TreeMap<Integer, Map<UUID, Map<String, UserDayCollection>>> userDayCollectionModeMap = new TreeMap<>();
			final Set<String> transactionModeList = new HashSet<>();
			final UUID unknownUser = UUID.randomUUID();
			final Map<String, Double> totalDifferentValueOfTransactionMode = new HashMap<>();
			for (final FeePaymentTransactionDetails feePaymentTransactionDetails : transactionDetailsList) {
				if (feePaymentTransactionDetails.getFeePaymentTransactionMetaData()
						.getFeePaymentTransactionStatus() == FeePaymentTransactionStatus.CANCELLED) {
					continue;
				}
				String transactionMode = feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getTransactionModeDisplayName();
				transactionModeList.add(transactionMode);
				UUID transactionBy = feePaymentTransactionDetails.getFeePaymentTransactionMetaData()
						.getTransactionAddedBy();
				if (transactionBy == null) {
					transactionBy = unknownUser;
				}
				final int startDay = DateUtils.getDayStart(
						feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getTransactionDate(),
						DateUtils.DEFAULT_TIMEZONE);
				if (!userDayCollectionModeMap.containsKey(startDay)) {
					userDayCollectionModeMap.put(startDay, new TreeMap<>());
				}
				if(!userDayCollectionModeMap.get(startDay).containsKey(transactionBy))
				{
					userDayCollectionModeMap.get(startDay).put(transactionBy, new TreeMap<>());
				}

				if (!userDayCollectionModeMap.get(startDay).get(transactionBy).containsKey(transactionMode)) {
					totalDifferentValueOfTransactionMode.put(transactionMode,0d);
					userDayCollectionModeMap.get(startDay).get(transactionBy).put(transactionMode, new UserDayCollection(startDay, transactionMode,
							0d, 0d, 0d));
				}
				final UserDayCollection userDayCollection = userDayCollectionModeMap.get(startDay).get(transactionBy).get(transactionMode);
				final double collectedAmount = userDayCollection.getCollectedAmount()
						+ feePaymentTransactionDetails.getTotalPaidAmount();
				final double instantDiscountAmount = userDayCollection.getInstantDiscountAmount()
						+ feePaymentTransactionDetails.getTotalInstantDiscountAmount();
				final double walletAmount = userDayCollection.getWalletAmount()
						+ feePaymentTransactionDetails.getFeePaymentTransactionMetaData().getDebitWalletAmount();
				userDayCollection.setCollectedAmount(collectedAmount);
				userDayCollection.setInstantDiscountAmount(instantDiscountAmount);
				userDayCollection.setWalletAmount(walletAmount);
			}

            final Map<UUID, User> userDetailsMap = new LinkedHashMap<>();
            if (allUsers) {
				Set<UUID> userIdList = new HashSet<>();
				
				for (Map<UUID, Map<String, UserDayCollection>> entry : userDayCollectionModeMap.values()) {
						userIdList.addAll(entry.keySet()); 
				}
                final List<User> users = userManager.getUsers(instituteId, userIdList);
                Collections.sort(users, new Comparator<User>() {
                    @Override
                    public int compare(User u1, User u2) {
                        return StringUtils.compare(u1.getFirstName(), u2.getFirstName());
                    }
                });

                for (final User userObj : users) {
                    userDetailsMap.put(userObj.getUuid(), userObj);
                }
            } else {
                userDetailsMap.put(userId, user);
            }

			if(CollectionUtils.isEmpty(userDetailsMap)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
            }

            int count = 1;
			int rowNum = 1;
			int colSpan = 0;
            double totalCollectedAmount = 0d;
			double totalWalletAmount = 0d;
            double totalInstantDiscount = 0d;
			AcademicSession academicSession = null;
			if(multipleSessionIds.size() == 1 ) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(multipleSessionIds.iterator().next());
			}
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = multipleSessionIds.size() > 0 && multipleSessionIds.size() != 1 ? reportName + " ( Multiple Session ) ":academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);
			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

			for (int i = 0; i <  USER_DAY_LEVEL_COLLECTION.length; i++) {
				if(i == 4){
					for(String transactionmode : transactionModeList)
					{
						reportCellDetailsHeaderRow.add(new ReportCellDetails(transactionmode, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
					}
				}
				reportCellDetailsHeaderRow.add(new ReportCellDetails(USER_DAY_LEVEL_COLLECTION[i], STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, transactionModeList.size() + USER_DAY_LEVEL_COLLECTION.length - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			for (final Entry<Integer, Map<UUID, Map<String, UserDayCollection>>> dayUserLevelCollectionEntry : userDayCollectionModeMap.entrySet()) {
				
				Map<UUID, Map<String, UserDayCollection>> dayUserModeLevelCollectionEntry = dayUserLevelCollectionEntry.getValue();

				for (final Entry<UUID, User> userEntry : userDetailsMap.entrySet()) {
					if (!dayUserModeLevelCollectionEntry.containsKey(userEntry.getKey())) {
						continue;
					}
					double totalUserCollectedAmount = 0d;
					double totalUserWalletAmount = 0d;
					double totalUserInstantDiscount = 0d;
					final Map<String, UserDayCollection> dayLevelCollectionMap = dayUserModeLevelCollectionEntry.get(userEntry.getKey());

					List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

					reportCellDetailsRow.add(new ReportCellDetails(count++, INTEGER,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

					reportCellDetailsRow.add(new ReportCellDetails(userEntry.getValue().getUserInstituteId(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

					reportCellDetailsRow.add(new ReportCellDetails( (StringUtils.isBlank(userEntry.getValue().getFirstName()) ? "" : userEntry.getValue().getFirstName())
							+ " " + (StringUtils.isBlank(userEntry.getValue().getLastName()) ? "" : userEntry.getValue().getLastName()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

					reportCellDetailsRow.add(new ReportCellDetails(DateUtils.getFormattedDate(dayUserLevelCollectionEntry.getKey()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
					Set<String> processedTransactionModes = new HashSet<>();
					for (Map.Entry<String, UserDayCollection> entry : dayLevelCollectionMap.entrySet()) {
						String key = entry.getKey();                 
    					UserDayCollection dayLevelCollection = entry.getValue();
						Double sum = totalDifferentValueOfTransactionMode.get(key);
						sum += dayLevelCollection.getCollectedAmount() == null ? 0 : dayLevelCollection.getCollectedAmount();
						totalDifferentValueOfTransactionMode.put(key, sum);
						for(String transactionmode : transactionModeList){
							if(!dayLevelCollectionMap.containsKey(transactionmode)){
								if (!processedTransactionModes.contains(transactionmode)) {
									reportCellDetailsRow.add(new ReportCellDetails(
										NumberUtils.formatToIndianCurrency(0d), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false
									));
									processedTransactionModes.add(transactionmode);
								}
							}
							if(transactionmode.equals(key)){
								reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								dayLevelCollection.getCollectedAmount()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

								totalUserCollectedAmount += dayLevelCollection.getCollectedAmount() == null
									? 0 : dayLevelCollection.getCollectedAmount();
								
								totalUserWalletAmount += dayLevelCollection.getWalletAmount() == null
									? 0 : dayLevelCollection.getWalletAmount();
								
								totalUserInstantDiscount += dayLevelCollection.getInstantDiscountAmount() == null
									? 0 : dayLevelCollection.getInstantDiscountAmount();
							}
						}
					}
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							totalUserCollectedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

					totalCollectedAmount += totalUserCollectedAmount == 0d
							? 0 : totalUserCollectedAmount;

					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						totalUserWalletAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

					totalWalletAmount += totalUserWalletAmount == 0d
							? 0 : totalUserWalletAmount;

					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
						totalUserInstantDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

					totalInstantDiscount += totalUserInstantDiscount == 0d
							? 0 : totalUserInstantDiscount;

					reportCellDetails.add(reportCellDetailsRow);
					rowNum++;
				}

				if (dayUserModeLevelCollectionEntry.containsKey(unknownUser)) {
					List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
					double totalUserCollectedAmount = 0d;
					double totalUserWalletAmount = 0d;
					double totalUserInstantDiscount = 0d;
					final Map<String, UserDayCollection> dayLevelCollectionMap = dayUserModeLevelCollectionEntry.get(unknownUser);
						reportCellDetailsRow.add(new ReportCellDetails(count++, INTEGER,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

						reportCellDetailsRow.add(new ReportCellDetails("NA", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

						reportCellDetailsRow.add(new ReportCellDetails("UNKNOWN", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

						reportCellDetailsRow.add(new ReportCellDetails( DateUtils.getFormattedDate(
								dayUserLevelCollectionEntry.getKey()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

								for (Map.Entry<String, UserDayCollection> entry : dayLevelCollectionMap.entrySet()) {
									String key = entry.getKey();                 
									UserDayCollection dayLevelCollection = entry.getValue();
									for(String transactionmode : transactionModeList){
										if(!dayLevelCollectionMap.keySet().contains(transactionmode)){
											reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
											0d), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
										}
										if(transactionmode.equals(key)){
											Double sum = totalDifferentValueOfTransactionMode.get(key);
											sum += dayLevelCollection.getCollectedAmount();
											totalDifferentValueOfTransactionMode.put(key, sum);
											reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
											dayLevelCollection.getCollectedAmount()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));

											totalUserCollectedAmount += dayLevelCollection.getCollectedAmount() == null
												? 0 : dayLevelCollection.getCollectedAmount();
											
											totalUserWalletAmount += dayLevelCollection.getWalletAmount() == null
												? 0 : dayLevelCollection.getWalletAmount();
											
											totalUserInstantDiscount += dayLevelCollection.getInstantDiscountAmount() == null
												? 0 : dayLevelCollection.getInstantDiscountAmount();
										}
									}
								}
								reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
										totalUserCollectedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
			
								totalCollectedAmount += totalUserCollectedAmount == 0d
										? 0 : totalUserCollectedAmount;
			
								reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
									totalUserWalletAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
			
								totalWalletAmount += totalUserWalletAmount == 0d
										? 0 : totalUserWalletAmount;
			
								reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
									totalUserInstantDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
			
								totalInstantDiscount += totalUserInstantDiscount == 0d
										? 0 : totalUserInstantDiscount;

						reportCellDetails.add(reportCellDetailsRow);
				}
			}

			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
			true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
						true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
				true));
			colSpan++;
			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
				true));
			colSpan++;
			for(Double value : totalDifferentValueOfTransactionMode.values()){
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(value), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
				true));
			}
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalCollectedAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
				true));
			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalWalletAmount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
					true));

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalInstantDiscount), STRING,CONTENT_SIZE+1, BLACK_COLOR, WHITE_COLOR,
			true));

			reportCellDetails.add(reportCellDetailsRow);
			rowNum++;

			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					USER_DAY_LEVEL_COLLECTION.length, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, USER_DAY_LEVEL_COLLECTION.length);

			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
			e.printStackTrace();
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
	}

	private class UserDayCollection {

		private int dayStartTimestamp;

		private String transactionMode;

		private Double collectedAmount;

		private Double walletAmount;

		private Double instantDiscountAmount;

        public UserDayCollection(int dayStartTimestamp, String transactionMode, Double collectedAmount, Double walletAmount, Double instantDiscountAmount) {
            this.dayStartTimestamp = dayStartTimestamp;
            this.transactionMode = transactionMode;
			this.collectedAmount = collectedAmount;
			this.walletAmount = walletAmount;
            this.instantDiscountAmount = instantDiscountAmount;
        }

        public int getDayStartTimestamp()
		{
            return dayStartTimestamp;
        }

        public void setDayStartTimestamp(int dayStartTimestamp)
		{
            this.dayStartTimestamp = dayStartTimestamp;
        }

        public Double getCollectedAmount() {
            return collectedAmount;
        }

        public void setCollectedAmount(Double collectedAmount) {
            this.collectedAmount = collectedAmount;
        }

        public Double getInstantDiscountAmount() {
            return instantDiscountAmount;
        }

        public void setInstantDiscountAmount(Double instantDiscountAmount) {
            this.instantDiscountAmount = instantDiscountAmount;
        }

		public Double getWalletAmount() {
			return walletAmount;
		}

		public void setWalletAmount(Double walletAmount) {
			this.walletAmount = walletAmount;
		}

		public String getTransactionMode() {
			return transactionMode;
		}

		public void setTransactionMode(String transactionMode) {
			this.transactionMode = transactionMode;
		}
	}

	private ReportDetails getStudentLedgerDetails(int instituteId, Integer academicSessionId, Set<UUID> requiredStandards, Set<UUID> feeIds,
												  Set<StudentStatus> studentStatusSet) {

		if (CollectionUtils.isEmpty(requiredStandards) || requiredStandards.size() > 1) {
            logger.error("Please select a class then generate report!");
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                    "Please select a single class then generate report!"));
        }

		final String reportName = "Student Ledger";
		final String sheetName = "StudentLedgerReport";

		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<StudentLedgerDetails> studentFeesInvoiceTransactionDetailsList = feePaymentInsightManager.getStudentLedgerDetails(instituteId, academicSessionId,
					//getting first element of standard
					requiredStandards.iterator().next(), feeIds, studentStatusSet);
			if (CollectionUtils.isEmpty(studentFeesInvoiceTransactionDetailsList)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
			}

			int rowNum = 1;
			int count = 1;
			AcademicSession academicSession = null;
			if(academicSessionId != null) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName + "(All Session)" : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			int totalColumns = 10;
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);


			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			for (StudentLedgerDetails studentFeesInvoiceTransactionDetails : studentFeesInvoiceTransactionDetailsList) {

				Student student = studentFeesInvoiceTransactionDetails.getStudent();

				List<ReportCellDetails> reportCellDetailsHeaderRow1 = new ArrayList<ReportCellDetails>();
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("S.No", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("Admission Number", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("Student Name", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("Class", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("Status", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow1.add(new ReportCellDetails("Fees", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				rowNum++;

				List<ReportCellDetails> reportCellDetailsHeaderRow2 = new ArrayList<ReportCellDetails>();
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(StringUtils.isEmpty(student.getStudentBasicInfo().getAdmissionNumber()) ?"-" : student.getStudentBasicInfo().getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(StringUtils.isEmpty(student.getStudentBasicInfo().getName()) ?"-" : student.getStudentBasicInfo().getName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse() == null ||StringUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection()) ?"-" : student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails(StudentStatus.getDisplayName(student.getStudentStatus()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow2.add(new ReportCellDetails("Date", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				rowNum++;

				List<ReportCellDetails> reportCellDetailsHeaderRow3 = new ArrayList<ReportCellDetails>();
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Father Name", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Primary Contact", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Whatsapp Number", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Father Contact", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Mother Contact", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				reportCellDetailsHeaderRow3.add(new ReportCellDetails("Invoice Number", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				rowNum++;

				List<ReportCellDetails> reportCellDetailsHeaderRow4 = new ArrayList<ReportCellDetails>();
				reportCellDetailsHeaderRow4.add(new ReportCellDetails(student.getStudentFamilyInfo() == null ||student.getStudentFamilyInfo().getFathersName() == null ? "-" :student.getStudentFamilyInfo().getFathersName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				reportCellDetailsHeaderRow4.add(new ReportCellDetails(StringUtils.isEmpty(student.getStudentBasicInfo().getPrimaryContactNumber()) ?"-" : student.getStudentBasicInfo().getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow4.add(new ReportCellDetails(StringUtils.isEmpty(student.getStudentBasicInfo().getWhatsappNumber()) ?"-" : student.getStudentBasicInfo().getWhatsappNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow4.add(new ReportCellDetails(student.getStudentFamilyInfo() == null || StringUtils.isEmpty(student.getStudentFamilyInfo().getFathersContactNumber()) ?"-" : student.getStudentFamilyInfo().getFathersContactNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow4.add(new ReportCellDetails(student.getStudentFamilyInfo() == null || StringUtils.isEmpty(student.getStudentFamilyInfo().getMothersContactNumber()) ?"-" : student.getStudentFamilyInfo().getMothersContactNumber(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
				reportCellDetailsHeaderRow4.add(new ReportCellDetails("Amount Paid", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				rowNum++;

				List<ReportCellDetails> reportCellDetailsHeaderRow5 = new ArrayList<ReportCellDetails>();
				reportCellDetailsHeaderRow5.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsHeaderRow5.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsHeaderRow5.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsHeaderRow5.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsHeaderRow5.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsHeaderRow5.add(new ReportCellDetails("Due Amount", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				rowNum++;

				StudentStatus status = student.getStudentStatus();

				int colNum = 5;
				for (Entry<UUID, FeesInvoiceTransactionDetails> feesInvoiceTransactionDetailsEntry : studentFeesInvoiceTransactionDetails.getFeesInvoiceTransactionDetailsMap().entrySet()) {
					List<InvoiceTransactionDetails> invoiceTransactionDetailsList = feesInvoiceTransactionDetailsEntry.getValue().getInvoiceTransactionDetailsList();
					int len = invoiceTransactionDetailsList.size();
					for (InvoiceTransactionDetails invoiceTransactionDetails : invoiceTransactionDetailsList) {
                        FeeConfigurationBasicInfo feeConfigurationBasicInfo = feesInvoiceTransactionDetailsEntry.getValue().getFeeConfigurationBasicInfo();
                        reportCellDetailsHeaderRow1.add(new ReportCellDetails(feeConfigurationBasicInfo.getFeeName() == null ? "-" :feeConfigurationBasicInfo.getFeeName(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
						reportCellDetailsHeaderRow2.add(new ReportCellDetails(invoiceTransactionDetails.getDate() == null ?"-" : DateUtils.getFormattedDate(invoiceTransactionDetails.getDate()), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
						reportCellDetailsHeaderRow3.add(new ReportCellDetails(invoiceTransactionDetails.getInvoiceId() == null ? "-" :invoiceTransactionDetails.getInvoiceId(), STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
						reportCellDetailsHeaderRow4.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(invoiceTransactionDetails.getPaidAmount()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
						double dueAmount = feesInvoiceTransactionDetailsEntry.getValue().getDueAmount();
						if(status == StudentStatus.NSO || status == StudentStatus.RELIEVED || status == StudentStatus.DELETED) {
							dueAmount = 0d;
						}
						reportCellDetailsHeaderRow5.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dueAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,false));
						colNum++;
					}

					if (len > 1) {
                        cellIndexes = new CellIndexes(rowNum - 6, rowNum - 6, colNum - len, colNum - 1);
                        mergeCellIndexesList.add(cellIndexes);
                        cellIndexes = new CellIndexes(rowNum - 2, rowNum - 2, colNum - len, colNum - 1);
						mergeCellIndexesList.add(cellIndexes);
                    }

				}

				cellIndexes = new CellIndexes(rowNum - 2, rowNum - 2, 0, 3);
				mergeCellIndexesList.add(cellIndexes);

				reportCellDetails.add(reportCellDetailsHeaderRow1);
				reportCellDetails.add(reportCellDetailsHeaderRow2);
				reportCellDetails.add(reportCellDetailsHeaderRow3);
				reportCellDetails.add(reportCellDetailsHeaderRow4);
				reportCellDetails.add(reportCellDetailsHeaderRow5);
				count++;
			}

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, totalColumns);

			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
	}

	private ReportDetails generateTransactionAmountsByModeReport(int instituteId, Integer academicSessionId, int start, int end, AggMode aggMode) {
		final String reportName = "Fees Collection Report (Mode Wise)";
		final String sheetName = "FeesCollectionReport(Mode Wise)";
		try {
			Institute institute = instituteManager.getInstitute(instituteId);
            final Map<Integer, Map<String, Double>> aggregatedTransactionModeAmounts = feePaymentInsightManager
                    .getTransactionAmountsByMode(instituteId, academicSessionId, start, end, aggMode);
            if (CollectionUtils.isEmpty(aggregatedTransactionModeAmounts)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
            }

			Set<String> distinctTransactionModes = new HashSet<>();

			for (Map.Entry<Integer, Map<String, Double>> entry : aggregatedTransactionModeAmounts.entrySet()) {
				for (Map.Entry<String, Double> innerEntry : entry.getValue().entrySet()) {
					if(!distinctTransactionModes.contains(innerEntry.getKey()) && innerEntry.getValue() > 0) {
							distinctTransactionModes.add(innerEntry.getKey());
						}
				}
			}

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			AcademicSession academicSession = null;
			if(academicSessionId > 0) {
					academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
						true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			final Map<String, String> transactionModeMap = new TreeMap<>();
			for (String transactionMode : distinctTransactionModes) {
				transactionModeMap.put(TransactionMode.getTransactionMode(transactionMode).name(),TransactionMode.getTransactionMode(transactionMode).getDisplayName());
			}

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

			int headerCount = 0;
			reportCellDetailsHeaderRow.add(new ReportCellDetails("S.No", STRING,
			CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
			true));
			headerCount++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Date", STRING,
			CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
			true));
			headerCount++;

			for (final Entry<String, String> transactionModeStringEntry : transactionModeMap.entrySet()) {
				headerCount++;
				reportCellDetailsHeaderRow.add(new ReportCellDetails(transactionModeStringEntry.getValue(), STRING,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				true));
			}
			reportCellDetailsHeaderRow.add(new ReportCellDetails("Total", STRING,
			CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
			true));
			headerCount++;

			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, headerCount - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			int count = 1;
            final Map<String, Double> transactionModeSum = new HashMap<>();
            double finalTotalAmount = 0d;
			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<>();
            for (final Entry<Integer, Map<String, Double>> entry : aggregatedTransactionModeAmounts.entrySet()) {
				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
				double totalForDay = 0d;
				reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER,
				CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				false));

				reportCellDetailsRow.add(new ReportCellDetails( DateUtils.getFormattedDate(entry.getKey(), DATE_FORMAT, User.DFAULT_TIMEZONE),STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
				false));

				for (final Entry<String, String> transactionModeStringEntry : transactionModeMap.entrySet()) {
					final String transactionMode = transactionModeStringEntry.getKey();
					if (!entry.getValue().containsKey(transactionMode)) {
						reportCellDetailsRow.add(new ReportCellDetails(0, INTEGER,
						CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
						false));
						continue;
					}

					final double paidAmount = entry.getValue().get(transactionMode);
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(paidAmount), STRING,
					CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,
					false));

					if (!transactionModeSum.containsKey(transactionMode)) {
						transactionModeSum.put(transactionMode, paidAmount);
					} else {
						transactionModeSum.put(transactionMode, NumberUtils.addValues(transactionModeSum.get(transactionMode), paidAmount));
					}

					totalForDay += paidAmount;
				}
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalForDay), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				finalTotalAmount += totalForDay;
				count++;
				reportCellDetails.add(reportCellDetailsRow);
			}

			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsRow.add(new ReportCellDetails(count, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			for (final Entry<String, String> transactionModeStringEntry : transactionModeMap.entrySet()) {
				final String transactionMode = transactionModeStringEntry.getKey();
                if (!transactionModeSum.containsKey(transactionMode)) {
					final Double amountValue = 0d;
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(amountValue), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
					continue;
				}
				final double totalPaidAmount = transactionModeSum.get(transactionMode);
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalPaidAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}

			final String finalTotalHeader = NumberUtils.formatToIndianCurrency(finalTotalAmount);
			reportCellDetailsRow.add(new ReportCellDetails( finalTotalHeader, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsRow);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					headerCount, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, headerCount);
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
	}

	private ReportDetails generateStudentFeesHeadLevelReportRowWise(int instituteId, Integer academicSessionId, FeeReportDataType feeReportDataType,
															 final Set<Integer> feeHeadIds, Set<StudentStatus> studentStatuses,
															Map<UUID, List<Integer>> requiredStandardSections, Integer feeDueDate, Set<String> requiredHeaderAttributes, FeesReportType feesReportType) {

		String sheetName = "StudentFeesHeadLevelReport";
		String reportName = "Student Fees Head Level Report";

		switch(feeReportDataType) {
			case ALL:
				sheetName = "StudentFeesHeadLevelReport";
				reportName = "Student Fees Head Level Report";
				break;
			case ONLY_DUE_STUDENTS:
				sheetName = "StudentFeesHeadLevelDueReport";
				reportName = "Student Fees Head Level Due Report";
				break;
			case ONLY_NON_DUE_STUDENTS:
				sheetName = "StudentFeesHeadLevelNonDueReport";
				reportName = "Student Fees Head Level Non Due Report";
				break;
		}

		try {
			int contentSize = 9;
			Institute institute = instituteManager.getInstitute(instituteId);
			Set<UUID> requiredStandards = requiredStandardSections.keySet();
            final List<StudentFeeHeadPaymentData> studentFeeHeadPaymentDataList = feePaymentInsightManager
                    .getStudentFeeHeadPaymentData(instituteId, academicSessionId, feeDueDate, feeHeadIds,
                            requiredStandards, null);
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			AcademicSession academicSession = null;
			if(academicSessionId > 0) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING, contentSize, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			if(CollectionUtils.isEmpty(studentFeeHeadPaymentDataList)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
			}

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

			int totalColumns = 0;
			int mergeColumns = 0;
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_SR_NO.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.REGISTRATION_NUMBER.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.ADMISSION_NUMBER.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_STUDENT_NAME.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.STATUS.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.STUDENT_CLASS.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_FATHER_NAME.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.PRIMARY_CONTACT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.WHATSAPP_NUMBER.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_FATHER_CONTACT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
				mergeColumns++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_HEAD_NAME.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_HEAD_NAME.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.AMOUNT_PAID.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_DUE_AMOUNT.getDisplayName(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			int count = 1;
			int rowNum = 1;
			double totalAssignedAmount = 0d;
			double totalAmountCollected = 0d;
			double totalGivenDiscount = 0d;
			double totalDiscountToBeGiven = 0d;
			double totalDueAmount = 0d;

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
			final Map<String, List<StudentFeeHeadPaymentData>> studentFeePaymentDataMap = getStudentLevelFeesHeadListMap(
					studentFeeHeadPaymentDataList);
			for (final Entry<String, List<StudentFeeHeadPaymentData>> studentFeeHeadPaymentDataEntry : studentFeePaymentDataMap
					.entrySet()) {
				int numOfFees = 0;
				double studentAssignedAmount = 0d;
				double studentAmountCollected = 0d;
				double studentGivenDiscount = 0d;
				double studentDiscountToBeGiven = 0d;
				double studentDueAmount = 0d;
				String admissionNumber = "";
				int colSpan = 0;
				for (final StudentFeeHeadPaymentData studentFeeHeadPaymentData : studentFeeHeadPaymentDataEntry.getValue()) {
					if (!CollectionUtils.isEmpty(studentStatuses) &&
							!studentStatuses.contains(studentFeeHeadPaymentData.getStatus())) {
						continue;
					}

					if (!CollectionUtils.isEmpty(feeHeadIds)
							&& !feeHeadIds.contains(studentFeeHeadPaymentData.getFeeHeadId())) {
						continue;
					}

					/**
					 * * Removing non required standards and sections
					 */

					final UUID standardId = studentFeeHeadPaymentData.getStandardId();
					final Integer sectionId = studentFeeHeadPaymentData.getSectionId();

					if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
						continue;
					}
					if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
							!requiredStandardSections.get(standardId).contains(sectionId)) {
						continue;
					}

					admissionNumber = studentFeeHeadPaymentData.getAdmissionNumber();
					double assignedAmount = studentFeeHeadPaymentData.getAssignedAmount();
					double amountCollected = studentFeeHeadPaymentData.getAmountCollected();
					double givenDiscount = studentFeeHeadPaymentData.getGivenDiscount();
					double remainingDiscountToBeGiven = studentFeeHeadPaymentData.getRemainingDiscountToBeGiven();
					double dueAmount = studentFeeHeadPaymentData.getDueAmount();

					if(studentFeeHeadPaymentData.getStatus() == StudentStatus.NSO
							|| studentFeeHeadPaymentData.getStatus() == StudentStatus.RELIEVED
							|| studentFeeHeadPaymentData.getStatus() == StudentStatus.DELETED) {
						assignedAmount = NumberUtils.addValues(amountCollected, givenDiscount);
						remainingDiscountToBeGiven = 0d;
						dueAmount = 0d;
					}

					if (feeReportDataType == FeeReportDataType.ONLY_DUE_STUDENTS && dueAmount <= 0) {
						continue;
					}

					if (feeReportDataType == FeeReportDataType.ONLY_NON_DUE_STUDENTS && dueAmount > 0) {
						continue;
					}

					numOfFees++;

					List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails( studentFeeHeadPaymentData.getRegistrationNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails( studentFeeHeadPaymentData.getAdmissionNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getStudentFullName(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							studentFeeHeadPaymentData.getStatus()),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(getStandardDisplay(studentFeeHeadPaymentData), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getFatherName(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getPrimaryContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getWhatsappNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getFatherContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getMotherContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_HEAD_NAME.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getFeeHeadName(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								assignedAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						totalAssignedAmount += assignedAmount;
						studentAssignedAmount += assignedAmount;
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								amountCollected), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						totalAmountCollected += amountCollected;
						studentAmountCollected += amountCollected;
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								givenDiscount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						totalGivenDiscount += givenDiscount;
						studentGivenDiscount += givenDiscount;
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								remainingDiscountToBeGiven), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						totalDiscountToBeGiven += remainingDiscountToBeGiven;
						studentDiscountToBeGiven += remainingDiscountToBeGiven;
					}
					if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
								dueAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						totalDueAmount += dueAmount;
						studentDueAmount += dueAmount;
					}
					reportCellDetails.add(reportCellDetailsRow);
					rowNum++;
				}
				if (numOfFees > 0) {
					count++;
				}

				if (numOfFees <= 1) {
					continue;
				}

				for (int k = 0; k < mergeColumns; k++) {
					cellIndexes = new CellIndexes(rowNum - numOfFees + 1, rowNum, k, k);
					mergeCellIndexesList.add(cellIndexes);
				}


				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				reportCellDetailsRow.add(new ReportCellDetails("Total (" + admissionNumber + ")", STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_HEAD_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
					colSpan++;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentAssignedAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentAmountCollected), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentGivenDiscount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentDiscountToBeGiven), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(studentDueAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				}
				reportCellDetails.add(reportCellDetailsRow);
				rowNum++;

				cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
				mergeCellIndexesList.add(cellIndexes);

			}

			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
			int colSpan = 0;

			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_HEAD_NAME.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAssignedAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAmountCollected), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalGivenDiscount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountToBeGiven), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDueAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, true));
			}
			reportCellDetails.add(reportCellDetailsRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);


			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
					totalColumns);


			reportSheetDetailsList.add(reportSheetDetails);

			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
			e.printStackTrace();
            logger.error("Error while generating {} report", reportName, e);
        }
        return null;
	}

	private ReportDetails generateStudentFeesHeadLevelReportColWise(int instituteId, Integer academicSessionId, FeeReportDataType feeReportDataType,
																	final Set<Integer> feeHeadIds, Set<StudentStatus> studentStatuses,
																	Map<UUID, List<Integer>> requiredStandardSections, Integer feeDueDate, Set<String> requiredHeaderAttributes, FeesReportType feesReportType) {

		String sheetName = "StudentFeesHeadLevelReport";
		String reportName = "Student Fees Head Level Report";

		switch (feeReportDataType) {
			case ALL:
				sheetName = "StudentFeesHeadLevelReport";
				reportName = "Student Fees Head Level Report";
				break;
			case ONLY_DUE_STUDENTS:
				sheetName = "StudentFeesHeadLevelDueReport";
				reportName = "Student Fees Head Level Due Report";
				break;
			case ONLY_NON_DUE_STUDENTS:
				sheetName = "StudentFeesHeadLevelNonDueReport";
				reportName = "Student Fees Head Level Non Due Report";
				break;
		}

		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			Set<UUID> requiredStandards = requiredStandardSections.keySet();
			final List<StudentFeeHeadPaymentData> studentFeeHeadPaymentDataList = feePaymentInsightManager
					.getStudentFeeHeadPaymentData(instituteId, academicSessionId, feeDueDate, feeHeadIds,
							requiredStandards, null);

			Map<Integer, String> feeHeadTypeMap = new HashMap<>();
			final LinkedHashMap<UUID, Map<Integer, StudentFeeHeadPaymentData>> studentFeeHeadPaymentDataMap = new LinkedHashMap<>();
			getStudentFeeHeadPaymentWithFeeHeaders(studentFeeHeadPaymentDataList, feeHeadTypeMap, studentFeeHeadPaymentDataMap);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			AcademicSession academicSession = null;
			if (academicSessionId > 0) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);

			if (CollectionUtils.isEmpty(studentFeeHeadPaymentDataList)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null, null);
				reportSheetDetailsList.add(reportSheetDetails);
				ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
				return reportDetails;
			}

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

			int totalColumns = 0;
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_SR_NO.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.REGISTRATION_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.ADMISSION_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_STUDENT_NAME.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.STATUS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.STUDENT_CLASS.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_FATHER_NAME.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.PRIMARY_CONTACT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.WHATSAPP_NUMBER.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_FATHER_CONTACT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			for (String value : feeHeadTypeMap.values()) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(value, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.AMOUNT_PAID.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(ReportHeaderAttribute.FEES_DUE_AMOUNT.getDisplayName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				totalColumns += 1;
			}
			headerReportCellDetails.add(reportCellDetailsHeaderRow);
			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<CellIndexes>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			int count = 1;
			int rowNum = 1;
			double totalAssignedAmount = 0d;
			double totalAmountCollected = 0d;
			double totalGivenDiscount = 0d;
			double totalDiscountToBeGiven = 0d;
			double totalDueAmount = 0d;

			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();

            final Map<Integer, Double> totalFeeHeadAssignedAmount = new HashMap<>();
			for (Map.Entry<UUID, Map<Integer, StudentFeeHeadPaymentData>> entry : studentFeeHeadPaymentDataMap.entrySet()) {
				Map<Integer, StudentFeeHeadPaymentData> feeHeadPaymentDataMap = entry.getValue();
				StudentFeeHeadPaymentData studentFeeHeadPaymentData = null;
				double studentAssignedAmount = 0d;
				double studentAmountCollected = 0d;
				double studentGivenDiscount = 0d;
				double studentRemainingDiscountToBeGiven = 0d;
				double studentDueAmount = 0d;
				for (Map.Entry<Integer, String> feeHeadEntry : feeHeadTypeMap.entrySet()) {
					Integer feeHeadId = feeHeadEntry.getKey();
					if (feeHeadPaymentDataMap.containsKey(feeHeadId)) {
						studentFeeHeadPaymentData = feeHeadPaymentDataMap.get(feeHeadId);
						studentAssignedAmount += studentFeeHeadPaymentData.getAssignedAmount();
						studentAmountCollected += studentFeeHeadPaymentData.getAmountCollected();
						studentGivenDiscount += studentFeeHeadPaymentData.getGivenDiscount();
						studentRemainingDiscountToBeGiven += studentFeeHeadPaymentData.getRemainingDiscountToBeGiven();
						studentDueAmount += studentFeeHeadPaymentData.getDueAmount();
					}

				}
				if (!CollectionUtils.isEmpty(studentStatuses) &&
						!studentStatuses.contains(studentFeeHeadPaymentData.getStatus())) {
					continue;
				}

				if (!CollectionUtils.isEmpty(feeHeadIds)
						&& !feeHeadIds.contains(studentFeeHeadPaymentData.getFeeHeadId())) {
					continue;
				}

				/**
				 * * Removing non required standards and sections
				 */
				final UUID standardId = studentFeeHeadPaymentData.getStandardId();
				final Integer sectionId = studentFeeHeadPaymentData.getSectionId();

				if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
					continue;
				}
				if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
						!requiredStandardSections.get(standardId).contains(sectionId)) {
					continue;
				}

				if (studentFeeHeadPaymentData.getStatus() == StudentStatus.NSO
						|| studentFeeHeadPaymentData.getStatus() == StudentStatus.RELIEVED
						|| studentFeeHeadPaymentData.getStatus() == StudentStatus.DELETED) {
					studentAssignedAmount = NumberUtils.addValues(studentAmountCollected, studentGivenDiscount);
					studentRemainingDiscountToBeGiven = 0d;
					studentDueAmount = 0d;
				}

				if (feeReportDataType == FeeReportDataType.ONLY_DUE_STUDENTS && studentDueAmount <= 0) {
					continue;
				}

				if (feeReportDataType == FeeReportDataType.ONLY_NON_DUE_STUDENTS && studentDueAmount > 0) {
					continue;
				}

				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_SR_NO.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(count, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getRegistrationNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getStudentFullName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
							studentFeeHeadPaymentData.getStatus()), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(getStandardDisplay(studentFeeHeadPaymentData), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getFatherName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getPrimaryContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getWhatsappNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getFatherContactNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(studentFeeHeadPaymentData.getMotherContactNumber(),STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				}
				for (Map.Entry<Integer, String> feeHeadEntry : feeHeadTypeMap.entrySet()) {
					Integer feeHeadId = feeHeadEntry.getKey();
					double assignedAmount = 0d;
					if (feeHeadPaymentDataMap.containsKey(feeHeadId)) {
						StudentFeeHeadPaymentData studentFeeData = feeHeadPaymentDataMap.get(feeHeadId);
						assignedAmount = studentFeeData.getAssignedAmount();
					}
					reportCellDetailsRow.add(new ReportCellDetails(
							NumberUtils.formatToIndianCurrency(assignedAmount),
							STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					if (totalFeeHeadAssignedAmount.containsKey(feeHeadId)) {
						double currentTotal = totalFeeHeadAssignedAmount.get(feeHeadId);
						totalFeeHeadAssignedAmount.put(feeHeadId, currentTotal + assignedAmount);
					} else {
						totalFeeHeadAssignedAmount.put(feeHeadId, assignedAmount);
					}

				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							studentAssignedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalAssignedAmount += studentAssignedAmount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							studentAmountCollected), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalAmountCollected += studentAmountCollected;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							studentGivenDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalGivenDiscount += studentGivenDiscount;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							studentRemainingDiscountToBeGiven), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalDiscountToBeGiven += studentRemainingDiscountToBeGiven;
				}
				if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
					reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(
							studentDueAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
					totalDueAmount += studentDueAmount;

				}
				reportCellDetails.add(reportCellDetailsRow);
				rowNum++;
				count++;
			}

			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
			int colSpan = 0;

			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.REGISTRATION_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.ADMISSION_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_STUDENT_NAME.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STATUS.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.STUDENT_CLASS.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_NAME.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.PRIMARY_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.WHATSAPP_NUMBER.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_FATHER_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_MOTHER_CONTACT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
				colSpan++;
			}
			for (Map.Entry<Integer, String> feeHeadEntry : feeHeadTypeMap.entrySet()) {
				Integer feeHeadId = feeHeadEntry.getKey();
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalFeeHeadAssignedAmount.get(feeHeadId)),
						STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_ASSIGNED_AMOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAssignedAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.AMOUNT_PAID.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalAmountCollected), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_GIVEN_DISCOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalGivenDiscount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DISCOUNT_TO_BE_GIVEN.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountToBeGiven), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			if (requiredHeaderAttributes.contains(ReportHeaderAttribute.FEES_DUE_AMOUNT.getKey())) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDueAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));
			}
			reportCellDetails.add(reportCellDetailsRow);
			rowNum++;

			cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
			mergeCellIndexesList.add(cellIndexes);


			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
					totalColumns);


			reportSheetDetailsList.add(reportSheetDetails);

			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error while generating {} report", reportName, e);
		}
		return null;
	}

	private void getStudentFeeHeadPaymentWithFeeHeaders(List<StudentFeeHeadPaymentData> studentFeePaymentDataList, Map<Integer, String> feeHeadTypeMap, LinkedHashMap<UUID, Map<Integer, StudentFeeHeadPaymentData>> studentFeeHeadPaymentDataMap) {
		for (final StudentFeeHeadPaymentData studentFeeHeadPaymentData : studentFeePaymentDataList) {
			int feeHeadId = studentFeeHeadPaymentData.getFeeHeadId();
			if (!feeHeadTypeMap.containsKey(feeHeadId)) {
				feeHeadTypeMap.put(feeHeadId, studentFeeHeadPaymentData.getFeeHeadName());
			}

			final UUID studentId = studentFeeHeadPaymentData.getStudentId();
			if (!studentFeeHeadPaymentDataMap.containsKey(studentId)) {
				studentFeeHeadPaymentDataMap.put(studentId, new HashMap<>());
			}
			Map<Integer, StudentFeeHeadPaymentData> studentFeeHeadMap = studentFeeHeadPaymentDataMap.get(studentId);
			studentFeeHeadMap.put(feeHeadId, studentFeeHeadPaymentData);
		}
	}

	private Map<String, List<StudentFeeHeadPaymentData>> getStudentLevelFeesHeadListMap(
		final List<StudentFeeHeadPaymentData> studentFeePaymentDataList) {
		final Map<String, List<StudentFeeHeadPaymentData>> studentFeeHeadPaymentDataMap = new LinkedHashMap<>();

		for (final StudentFeeHeadPaymentData studentFeeHeadPaymentData : studentFeePaymentDataList) {
			final String admissionNumber = studentFeeHeadPaymentData.getAdmissionNumber();
			if (!studentFeeHeadPaymentDataMap.containsKey(admissionNumber)) {
				studentFeeHeadPaymentDataMap.put(admissionNumber, new ArrayList<>());
			}
			studentFeeHeadPaymentDataMap.get(admissionNumber).add(studentFeeHeadPaymentData);
		}
		return studentFeeHeadPaymentDataMap;
	}

	private String getStandardDisplay(StudentFeeHeadPaymentData studentFeeHeadPaymentData) {
		return getStandardDisplay(studentFeeHeadPaymentData.getStandardName(),
				studentFeeHeadPaymentData.getStream(), studentFeeHeadPaymentData.getSectionName());
	}

	public ReportDetails generateStudentDiscountAssignmentReport(int instituteId, int academicSessionId, FeesReportType feesReportType,
																 Map<UUID, List<Integer>> requiredStandardSections, Set<StudentStatus> studentStatusSet) {
		try {
			final String reportName = "Student Discounts Assignment";
			final String sheetName = "StudentDiscountsAssignmentReports";

			Institute institute = instituteManager.getInstitute(instituteId);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			AcademicSession academicSession = null;
			if(academicSessionId > 0) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);
			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			for (int i = 0; i <  STUDENT_DISCOUNT_ASSIGNMENT.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(STUDENT_DISCOUNT_ASSIGNMENT[i], STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
			}

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, STUDENT_DISCOUNT_ASSIGNMENT.length - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			final Set<UUID> requiredStandards = requiredStandardSections.keySet();

			List<StudentFeeDiscountAssignmentMetadata> studentFeeDiscountAssignmentMetadataList = feeDiscountConfigurationManager
					.searchStudentsDiscountAssignment(instituteId, academicSessionId, null, null);

			final List<StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDatas = feePaymentInsightManager
					.getStudentPaymentStats(instituteId, academicSessionId, null, null, requiredStandards,
							studentStatusSet);

			if (CollectionUtils.isEmpty(studentFeeDiscountAssignmentMetadataList) && CollectionUtils.isEmpty(studentFeePaymentAggregatedDatas)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			Map<UUID, StudentFeeDiscountAssignmentMetadata> studentFeeDiscountAssignmentMetadataMap = new HashMap<>();
			for (StudentFeeDiscountAssignmentMetadata studentFeeDiscountAssignmentMetadata : studentFeeDiscountAssignmentMetadataList) {
				if(!studentFeeDiscountAssignmentMetadataMap.containsKey(studentFeeDiscountAssignmentMetadata.getStudentLite().getStudentId())) {
					studentFeeDiscountAssignmentMetadataMap.put(studentFeeDiscountAssignmentMetadata.getStudentLite().getStudentId(),
							studentFeeDiscountAssignmentMetadata);
				}
			}

			int srNo = 1;

			double finalTotalAssignedDiscountAmount = 0;
			double finalTotalInstantDiscountAmount = 0;
			double finalTotalDiscountAmount = 0;
			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			for (StudentFeePaymentAggregatedData studentFeePaymentAggregatedData : studentFeePaymentAggregatedDatas) {
				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
				UUID studentId = studentFeePaymentAggregatedData.getStudentId();
				final UUID standardId = studentFeePaymentAggregatedData.getStandardId();
				final Integer sectionId = studentFeePaymentAggregatedData.getSectionId();

				if (!CollectionUtils.isEmpty(requiredStandardSections) && !requiredStandardSections.containsKey(standardId)) {
					continue;
				}
				if (sectionId != null && !CollectionUtils.isEmpty(requiredStandardSections.get(standardId)) &&
						!requiredStandardSections.get(standardId).contains(sectionId)) {
					continue;
				}

				StudentStatus studentStatus = studentFeePaymentAggregatedData.getSessionStatus();
				if (!CollectionUtils.isEmpty(studentStatusSet) && !studentStatusSet.contains(studentStatus)) {
					continue;
				}

				StudentFeeDiscountAssignmentMetadata studentFeeDiscountAssignmentMetadata = studentFeeDiscountAssignmentMetadataMap.get(studentId);
				double assignedDiscountAmount = studentFeePaymentAggregatedData.getGivenDiscount()
						+ studentFeePaymentAggregatedData.getRemainingDiscountToBeGiven()
						- studentFeePaymentAggregatedData.getInstantDiscountGiven();
				double instantDiscountAmount = studentFeePaymentAggregatedData.getInstantDiscountGiven();
				double totalDiscountAmount = NumberUtils.addValues(assignedDiscountAmount, instantDiscountAmount);

				String discountsDisplay = "";
				if(studentFeeDiscountAssignmentMetadata != null) {
					List<FeeDiscountMetadata> feeDiscountMetadataList = studentFeeDiscountAssignmentMetadata.getFeeDiscountMetadataList();
					String delimiter = "";
					for (FeeDiscountMetadata feeDiscountMetadata : feeDiscountMetadataList) {
						String name = feeDiscountMetadata.getName();
						if (feeDiscountMetadata.getDiscountStructureType() == DiscountStructureType.SYSTEM) {
							name = feeDiscountMetadata.getMetadata().get("title");
						}
						discountsDisplay += delimiter + name;
						delimiter = ", ";
					}
				}

				/**
				 * skippign if no discount is assigned for taken for a particular student
				 */
				if(totalDiscountAmount == 0 && StringUtils.isBlank(discountsDisplay)) {
					continue;
				}

				finalTotalAssignedDiscountAmount += assignedDiscountAmount;
				finalTotalInstantDiscountAmount += instantDiscountAmount;
				finalTotalDiscountAmount += totalDiscountAmount;

				reportCellDetailsRow.add(new ReportCellDetails(srNo++, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedData.getAdmissionNumber(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedData.getStudentFullName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
						studentStatus), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedData.getFatherName() == null ? "" : studentFeePaymentAggregatedData.getFatherName(), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(getStandardDisplay(studentFeePaymentAggregatedData), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails( discountsDisplay, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(assignedDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(instantDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetails.add(reportCellDetailsRow);

			}


			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsRow.add(new ReportCellDetails("Total", INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(finalTotalAssignedDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(finalTotalInstantDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(finalTotalDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetails.add(reportCellDetailsRow);

			List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					STUDENT_DISCOUNT_ASSIGNMENT.length, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true,
					true, institute, STUDENT_DISCOUNT_ASSIGNMENT.length);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {
			logger.error("Error getting report {}, instituteId {}, academicSessionId {}", feesReportType, instituteId, academicSessionId, e);
		}
		return null;
	}

	public ReportDetails generateStudentLevelAggregatedMultiSessionDueReport(int instituteId, Set<Integer> academicSessionIds,
																			 Set<UUID> requiredStandards, Integer feeDueDate, Set<UUID> feeIds, UUID userId) {
		return generateStudentLevelAggregatedMultiSessionDueReport(instituteId, academicSessionIds, true,
				requiredStandards, feeDueDate, feeIds);
	}

	private ReportDetails generateStudentLevelAggregatedMultiSessionDueReport(int instituteId, Set<Integer> academicSessionIds,
																			  boolean includeDueStudentOnly,  Set<UUID> requiredStandards,
																			  Integer feeDueDate, Set<UUID> feeIds) {
		// Should be enhanced to take as input
		Set<StudentStatus> statusSet = new HashSet<>(Arrays.asList(StudentStatus.ENROLLED));
		final String reportName = "Student Level Aggregated Multi Session Fees Due";
		final String sheetName = "StudentLevelAggregatedMultiSessionFeesDueReport";
		try {
			Institute institute = instituteManager.getInstitute(instituteId);
			if (academicSessionIds == null) {
				logger.error("No sessions provided instituteId {}", instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No sessions provided"));
			}
			int limit = 3;
			int contentSize = 9;
			if (academicSessionIds.size() > limit) {
				logger.error("sessions provided exceeds limit of {}, instituteId {}", limit, instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Maximum of " + limit + " sessions can be requested"));
			}
			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			reportHeaderRow.add(new ReportCellDetails(reportName, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);
			List<AcademicSession> academicSessions = instituteManager.getAcademicSessionList(instituteId);
			List<AcademicSession> requiredSessions = new ArrayList<>();

			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			int totalColumns = 0;
			reportCellDetailsHeaderRow.add(new ReportCellDetails("S.No", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Admission Number", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Student Name", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Status", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Father Name", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Primary Contact", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Whatsapp Number", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Father Contact", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			reportCellDetailsHeaderRow.add(new ReportCellDetails("Mother Contact", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			LinkedHashMap<Integer, Double> sessionTotalDueAmountMap = new LinkedHashMap<>();
			for (AcademicSession academicSession : academicSessions) {
				if (academicSessionIds.contains(academicSession.getAcademicSessionId())) {
					requiredSessions.add(academicSession);
					reportCellDetailsHeaderRow.add(new ReportCellDetails(academicSession.getYearDisplayName(), STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
					totalColumns++;
					sessionTotalDueAmountMap.put(academicSession.getAcademicSessionId(), 0d);
				}
			}
			reportCellDetailsHeaderRow.add(new ReportCellDetails("Total Due Amount", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			totalColumns++;

			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, totalColumns - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			Map<UUID, Map<Integer, StudentFeePaymentAggregatedData>> studentAcademicSessionData = new HashMap<>();
			Map<UUID, StudentFeePaymentAggregatedData> studentMap = new HashMap<>();
			for (Integer academicSessionId : academicSessionIds) {
				final List<StudentFeePaymentAggregatedData> studentFeePaymentAggregatedDatas = feePaymentInsightManager
							.getStudentPaymentStats(instituteId, academicSessionId, feeDueDate, feeIds, requiredStandards, statusSet);
				if(CollectionUtils.isEmpty(studentFeePaymentAggregatedDatas)) {
					continue;
				}
				for (StudentFeePaymentAggregatedData studentFeePaymentAggregatedData : studentFeePaymentAggregatedDatas) {
					studentMap.put(studentFeePaymentAggregatedData.getStudentId(), studentFeePaymentAggregatedData);
					if (!studentAcademicSessionData.containsKey(studentFeePaymentAggregatedData.getStudentId())) {
						studentAcademicSessionData.put(studentFeePaymentAggregatedData.getStudentId(), new LinkedHashMap<>());
					}
					studentAcademicSessionData.get(studentFeePaymentAggregatedData.getStudentId()).put(academicSessionId, studentFeePaymentAggregatedData);
				}
			}
			List<StudentFeePaymentAggregatedData> sortedStudentFeePaymentAggregatedData = new ArrayList<>(studentMap.values());
			Collections.sort(sortedStudentFeePaymentAggregatedData, new Comparator<StudentFeePaymentAggregatedData>() {
				@Override
				public int compare(StudentFeePaymentAggregatedData s1, StudentFeePaymentAggregatedData s2) {
					return StringHelper.compareIgnoreCase(s1.getStudentFullName(),
									s2.getStudentFullName());
				}
			});
			int rowNum=1;
			int colSpan = 0;
			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();
			if (!CollectionUtils.isEmpty(sortedStudentFeePaymentAggregatedData)) {
			// Create Other rows and cells with inventory data
			int count = 1;

			double sumTotal = 0d;
			for (final StudentFeePaymentAggregatedData studentFeePaymentAggregatedDataEntry : sortedStudentFeePaymentAggregatedData) {
				Map<Integer, StudentFeePaymentAggregatedData> studentAllSessionDataMap = studentAcademicSessionData.get(studentFeePaymentAggregatedDataEntry.getStudentId());
				boolean anyFeeDue = false;
				for (StudentFeePaymentAggregatedData studentFeePaymentAggregatedData : studentAllSessionDataMap.values()) {
					if (studentFeePaymentAggregatedData.getDueAmount() > 0) {
						anyFeeDue = true;
						break;
					}
				}
				if (includeDueStudentOnly && !anyFeeDue) {
					continue;
				}

				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

				reportCellDetailsRow.add(new ReportCellDetails(count,INTEGER, contentSize, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getAdmissionNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getStudentFullName(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(StudentStatus.getDisplayName(
						studentFeePaymentAggregatedDataEntry.getSessionStatus()), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getFatherName(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getPrimaryContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getWhatsappNumber(), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));

				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getFatherContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsRow.add(new ReportCellDetails(studentFeePaymentAggregatedDataEntry.getMotherContactNumber(),STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));

				double total = 0d;
				for (AcademicSession academicSession : requiredSessions) {
					int academicSessionId = academicSession.getAcademicSessionId();
					if (studentAllSessionDataMap.containsKey(academicSessionId)) {
						double dueAmount = studentAllSessionDataMap.get(academicSessionId).getDueAmount();
						double amount = sessionTotalDueAmountMap.get(academicSession.getAcademicSessionId());
						sessionTotalDueAmountMap.put(academicSession.getAcademicSessionId(), NumberUtils.addValues(amount, dueAmount));
						reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(dueAmount), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
						total += dueAmount;
					} else {
						reportCellDetailsRow.add(new ReportCellDetails(NOT_AVAILABLE,STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
					}
				}
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(total), STRING, contentSize, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetails.add(reportCellDetailsRow);
				sumTotal += total;
				count++;
				rowNum++;
			}
			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();

			reportCellDetailsRow.add(new ReportCellDetails("Total", STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			colSpan++;

			for(Entry<Integer, Double> amount : sessionTotalDueAmountMap.entrySet()) {
				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(amount.getValue()),
						STRING,contentSize, BLACK_COLOR, WHITE_COLOR,true));
			}

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(sumTotal),
					STRING, contentSize, BLACK_COLOR, WHITE_COLOR,true));

			rowNum++;

			reportCellDetails.add(reportCellDetailsRow);
		}

		List<CellIndexes> mergeCellIndexesList = new ArrayList<CellIndexes>();
		cellIndexes = new CellIndexes(rowNum, rowNum, 0, colSpan);
		mergeCellIndexesList.add(cellIndexes);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true,
					totalColumns, headerMergeCellIndexesList,
					mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true, institute,
					totalColumns);

			reportSheetDetailsList.add(reportSheetDetails);

			return new ReportDetails(reportName, reportSheetDetailsList);
		} catch (ApplicationException e) {
			throw e;
		} catch (final Exception e) {
			logger.error("Error while generating {} report", reportName, e);
		}
		return null;
	}

	public ReportDetails generateDiscountSummaryReport(int instituteId, int academicSessionId,
				Map<UUID, List<Integer>> requiredStandardSections, Set<StudentStatus> studentStatusSet) {
		try {
			final String reportName = "Discount Summary Report";
			final String sheetName = "DiscountSummaryReport";

			List<DiscountStructureAmountMetadata> discountStructureAmountMetadataList = feePaymentInsightManager.getDiscountStructureAmountMetadata(
					instituteId, academicSessionId, requiredStandardSections, studentStatusSet, true);

			if (CollectionUtils.isEmpty(discountStructureAmountMetadataList)) {
				logger.error("Empty report {}", reportName);
				List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
				ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, null,null);
				reportSheetDetailsList.add(reportSheetDetails);
				return new ReportDetails(reportName, reportSheetDetailsList);
			}

			Institute institute = instituteManager.getInstitute(instituteId);

			List<List<ReportCellDetails>> headerReportCellDetails = new ArrayList<List<ReportCellDetails>>();
			AcademicSession academicSession = null;
			if(academicSessionId > 0) {
				academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
			}

			List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
			String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
			reportHeaderRow.add(new ReportCellDetails(heading, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));
			headerReportCellDetails.add(reportHeaderRow);
			List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();
			for (int i = 0; i <  DISCOUNT_SUMMARY_REPORT.length; i++) {
				reportCellDetailsHeaderRow.add(new ReportCellDetails(DISCOUNT_SUMMARY_REPORT[i], STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
			}

			List<CellIndexes> headerMergeCellIndexesList = new ArrayList<>();
			CellIndexes cellIndexes = new CellIndexes(0, 0, 0, DISCOUNT_SUMMARY_REPORT.length - 1);
			headerMergeCellIndexesList.add(cellIndexes);

			headerReportCellDetails.add(reportCellDetailsHeaderRow);

			int srNo = 1;
			double totalDiscountAmount = 0d;
			List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

			for (DiscountStructureAmountMetadata discountStructureAmountMetadata : discountStructureAmountMetadataList) {

				String discountStructureName = discountStructureAmountMetadata.getDiscountStructureName();
				Double discountStructureAmount = discountStructureAmountMetadata.getDiscountAmount();
//				/**
//				 * filtering discount which does not have any amount in them
//				 */
//				if(discountStructureAmount == null || discountStructureAmount == 0) {
//					continue;
//				}
				totalDiscountAmount = NumberUtils.addValues(totalDiscountAmount, discountStructureAmount);

				List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
				reportCellDetailsRow.add(new ReportCellDetails(srNo++, INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetailsRow.add(new ReportCellDetails(discountStructureName, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false, ReportHorizontalTextAlignment.LEFT, ReportVerticalTextAlignment.MIDDLE));

				reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(discountStructureAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));
				reportCellDetails.add(reportCellDetailsRow);

			}


			List<ReportCellDetails> reportCellDetailsRow = new ArrayList<ReportCellDetails>();
			reportCellDetailsRow.add(new ReportCellDetails("Total", INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(EMPTY_TEXT, STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));

			reportCellDetailsRow.add(new ReportCellDetails(NumberUtils.formatToIndianCurrency(totalDiscountAmount), STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, true));


			reportCellDetails.add(reportCellDetailsRow);

			List<CellIndexes> mergeCellIndexesList = new ArrayList<>();
			ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, true, DISCOUNT_SUMMARY_REPORT.length,
					headerMergeCellIndexesList, mergeCellIndexesList, headerReportCellDetails, reportCellDetails, true, true,
					institute, DISCOUNT_SUMMARY_REPORT.length);

			List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
			reportSheetDetailsList.add(reportSheetDetails);
			return new ReportDetails(reportName, reportSheetDetailsList);

		} catch (final Exception e) {
			e.printStackTrace();
			logger.error("Error getting instituteId {}, academicSessionId {}", instituteId, academicSessionId, e);
		}
		return null;
	}
}

