package com.lernen.cloud.core.lib.examination.reports.v2;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.api.examination.utility.ExamReportFiltrationCriteria;
import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.embrate.cloud.core.api.report.util.IReportStore;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.embrate.cloud.core.lib.report.factory.ReportRegistry;
import com.lernen.cloud.core.api.common.StringHelper;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.examination.report.StudentReportCardAttributeDetailsPayload;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;

public class StudentReportCardAttributeDetailsReportGenerator extends ReportGenerator {

    private static final Logger logger = LogManager.getLogger(StudentReportCardAttributeDetailsReportGenerator.class);

    private final UserPermissionManager userPermissionManager;
    private final ReportRegistry reportRegistry;

    public StudentReportCardAttributeDetailsReportGenerator(UserPermissionManager userPermissionManager,
            ReportRegistry reportRegistry) {
        this.userPermissionManager = userPermissionManager;
        this.reportRegistry = reportRegistry;
    }

    public ReportDetails generateReport(int instituteId, UUID userId, ExamReportFiltrationCriteria examReportFiltrationCriteria) {

        if (instituteId <= 0 || userId == null || examReportFiltrationCriteria == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }

        // Verify user permissions
        userPermissionManager.verifyAuthorisation(instituteId, userId,
                AuthorisationRequiredAction.GENERATE_EXAM_REPORT_CARD);

        return generateStudentReportCardAttributeDetailsReport(instituteId, examReportFiltrationCriteria);
    }

    private ReportDetails generateStudentReportCardAttributeDetailsReport(int instituteId, ExamReportFiltrationCriteria examReportFiltrationCriteria) {

        String reportId = "STUDENT_REPORT_CARD_ATTRIBUTE_DETAILS_REPORT";
        ReportBuilder<IReportPayload, IReportStore> reportBuilder = reportRegistry
                .getReportBuilders(Module.EXAMINATION).get(reportId);

        if (reportBuilder == null) {
            logger.error("Report builder not found for reportId: {}", reportId);
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Report builder not found"));
        }

        return reportBuilder.build(new StudentReportCardAttributeDetailsPayload(instituteId,
                examReportFiltrationCriteria.getAcademicSessionId(),
                examReportFiltrationCriteria.getStandardId(),
                examReportFiltrationCriteria.getSectionIdSet(),
                examReportFiltrationCriteria.getReportCardType(),
                convertToRequiredHeadersList(examReportFiltrationCriteria.getRequiredHeaders()),
                examReportFiltrationCriteria.getStudentSortingParameters()));
    }

    private List<String> convertToRequiredHeadersList(String requiredHeaders) {
        if (requiredHeaders == null || requiredHeaders.trim().isEmpty()) {
            return null;
        }
        return Arrays.asList(requiredHeaders.split(","));
    }
}
