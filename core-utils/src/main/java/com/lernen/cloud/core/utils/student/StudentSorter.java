package com.lernen.cloud.core.utils.student;

import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.utils.NumberUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class StudentSorter {

    public static <T> void sortStudents(
            List<T> studentList,
            StudentSortingParameters sortingParameter,
            StudentKeyExtractor<T> keyExtractor) {

        if (sortingParameter == null || studentList == null || studentList.isEmpty()) {
            return;
        }

        switch (sortingParameter) {
            case ADMISSION_NUMBER:
                Collections.sort(studentList, new Comparator<T>() {
                    @Override
                    public int compare(T s1, T s2) {
                        String a1 = keyExtractor.getAdmissionNumber(s1);
                        String a2 = keyExtractor.getAdmissionNumber(s2);

                        if (StringUtils.isBlank(a1) && StringUtils.isBlank(a2)) return 0;
                        if (StringUtils.isBlank(a1)) return 1;
                        if (StringUtils.isBlank(a2)) return -1;

                        boolean isNum1 = NumberUtils.isNumeric(a1);
                        boolean isNum2 = NumberUtils.isNumeric(a2);

                        if (isNum1 && isNum2) {
                            try {
                                return Integer.compare(Integer.parseInt(a1), Integer.parseInt(a2));
                            } catch (NumberFormatException e) {
                                // Fallback to string comparison if parsing fails
                                return a1.compareToIgnoreCase(a2);
                            }
                        }

                        if (isNum1) return -1; // numbers before strings
                        if (isNum2) return 1;

                        return a1.compareToIgnoreCase(a2);
                    }
                });
                break;
            case STUDENT_NAME:
                Collections.sort(studentList, new Comparator<T>() {
                    @Override
                    public int compare(T s1, T s2) {
                        return keyExtractor.getName(s1).compareToIgnoreCase(keyExtractor.getName(s2));
                    }
                });
                break;
            case ROLL_NUMBER:
                sortByRollNumberAndName(studentList, keyExtractor);
                break;
            case ADMISSION_DATE:
                Collections.sort(studentList, new Comparator<T>() {
                    @Override
                    public int compare(T s1, T s2) {
                        return NumberUtils.compare(keyExtractor.getAdmissionDate(s1), keyExtractor.getAdmissionDate(s2));
                    }
                });
                break;
            default:
                break;
        }
    }

    private static <T> void sortByRollNumberAndName(List<T> studentList, final StudentKeyExtractor<T> keyExtractor) {
        boolean rollNumberIsNumeric = true;
        for (T student : studentList) {
            String rollNumber = keyExtractor.getRollNumber(student);
            if (!NumberUtils.isNumeric(rollNumber)) {
                rollNumberIsNumeric = false;
                break;
            }
        }

        final boolean finalRollNumberIsNumeric = rollNumberIsNumeric;

        Collections.sort(studentList, new Comparator<T>() {
            @Override
            public int compare(T s1, T s2) {
                String section1 = keyExtractor.getSectionName(s1);
                String section2 = keyExtractor.getSectionName(s2);

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.compareToIgnoreCase(section2);
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                String rollNumber1 = keyExtractor.getRollNumber(s1);
                String rollNumber2 = keyExtractor.getRollNumber(s2);

                if (finalRollNumberIsNumeric) {
                    try {
                        int rollNumberInt1 = StringUtils.isBlank(rollNumber1) ? Integer.MAX_VALUE : Integer.parseInt(rollNumber1);
                        int rollNumberInt2 = StringUtils.isBlank(rollNumber2) ? Integer.MAX_VALUE : Integer.parseInt(rollNumber2);
                        int rollCompare = Integer.compare(rollNumberInt1, rollNumberInt2);
                        if (rollCompare != 0) {
                            return rollCompare;
                        }
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                } else if (rollNumber1 != null && rollNumber2 != null) {
                    int rollCompare = rollNumber1.compareToIgnoreCase(rollNumber2);
                    if (rollCompare != 0) {
                        return rollCompare;
                    }
                }

                int nameCompare = keyExtractor.getName(s1).compareToIgnoreCase(keyExtractor.getName(s2));
                if (nameCompare != 0) {
                    return nameCompare;
                }

                return keyExtractor.getAdmissionNumber(s1).compareToIgnoreCase(keyExtractor.getAdmissionNumber(s2));
            }
        });
    }

    public interface StudentKeyExtractor<T> {
        String getAdmissionNumber(T student);

        String getName(T student);

        String getRollNumber(T student);

        String getSectionName(T student);

        Integer getAdmissionDate(T student);
    }
}
