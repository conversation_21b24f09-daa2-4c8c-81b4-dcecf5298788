/**
 * 
 */
package com.embrate.cloud.core.api.noticeboard;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum NoticeBoardStatus {
	
	SAVED, SCHEDULED, BROADCASTED;
	
	public static NoticeBoardStatus getNoticeBoardStatus(String noticeBoardStatus){
		if(StringUtils.isBlank(noticeBoardStatus)){
			return null;
		}
		for(NoticeBoardStatus noticeBoardStatusEnum : NoticeBoardStatus.values()){
			if(noticeBoardStatusEnum.name().equalsIgnoreCase(noticeBoardStatus)){
				return noticeBoardStatusEnum;
			}
		}
		return null;
	}

}
