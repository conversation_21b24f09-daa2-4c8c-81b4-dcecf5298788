package com.lernen.cloud.core.api.fees;


/**
 * 
 * <AUTHOR>
 *
 */
public class AuthorizedFeeAssignmentRow {

	private final FeeConfigurationResponse feeConfigurationResponse;

	private final FeeHeadConfigurationResponse feeHeadConfigurationResponse;

	private final ModuleFeeProportion moduleFeeProportion;

	public AuthorizedFeeAssignmentRow(FeeConfigurationResponse feeConfigurationResponse,
			FeeHeadConfigurationResponse feeHeadConfigurationResponse, ModuleFeeProportion moduleFeeProportion) {
		this.feeConfigurationResponse = feeConfigurationResponse;
		this.feeHeadConfigurationResponse = feeHeadConfigurationResponse;
		this.moduleFeeProportion = moduleFeeProportion;
	}

	public FeeConfigurationResponse getFeeConfigurationResponse() {
		return feeConfigurationResponse;
	}

	public FeeHeadConfigurationResponse getFeeHeadConfigurationResponse() {
		return feeHeadConfigurationResponse;
	}

	public ModuleFeeProportion getModuleFeeProportion() {
		return moduleFeeProportion;
	}

	@Override
	public String toString() {
		return "AuthorizedFeeAssignmentRow [feeConfigurationResponse=" + feeConfigurationResponse
				+ ", feeHeadConfigurationResponse=" + feeHeadConfigurationResponse + ", moduleFeeProportion="
				+ moduleFeeProportion + "]";
	}

}
