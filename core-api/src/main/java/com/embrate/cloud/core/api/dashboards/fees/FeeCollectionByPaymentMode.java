package com.embrate.cloud.core.api.dashboards.fees;

import com.lernen.cloud.core.api.common.TransactionMode;
import com.lernen.cloud.core.api.user.Gender;

/**
 * <AUTHOR>
 */
public class FeeCollectionByPaymentMode {

    private final int instituteId;
    private final TransactionMode mode;
    private final double totalAmount;

    public FeeCollectionByPaymentMode(int instituteId, TransactionMode mode, double totalAmount) {
        this.instituteId = instituteId;
        this.mode = mode;
        this.totalAmount = totalAmount;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public TransactionMode getMode() {
        return mode;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    @Override
    public String toString() {
        return "FeeCollectionByPaymentMode{" +
                "instituteId=" + instituteId +
                ", mode=" + mode +
                ", totalAmount=" + totalAmount +
                '}';
    }
}