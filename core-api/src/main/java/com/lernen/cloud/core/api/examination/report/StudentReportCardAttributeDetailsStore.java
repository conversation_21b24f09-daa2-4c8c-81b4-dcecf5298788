package com.lernen.cloud.core.api.examination.report;

import java.util.List;

import com.embrate.cloud.core.api.report.util.IReportStore;

public class StudentReportCardAttributeDetailsStore implements IReportStore {

    private final List<ExamReportData> examReportDataList;
    private final String className;
    private final String academicSessionDisplayName;

    public StudentReportCardAttributeDetailsStore(List<ExamReportData> examReportDataList, String className,
            String academicSessionDisplayName) {
        this.examReportDataList = examReportDataList;
        this.className = className;
        this.academicSessionDisplayName = academicSessionDisplayName;
    }

    public List<ExamReportData> getExamReportDataList() {
        return examReportDataList;
    }

    public String getClassName() {
        return className;
    }

    public String getAcademicSessionDisplayName() {
        return academicSessionDisplayName;
    }

    @Override
    public String toString() {
        return "StudentReportCardAttributeDetailsStore [examReportDataList=" + examReportDataList + ", className="
                + className + ", academicSessionDisplayName=" + academicSessionDisplayName + "]";
    }
}
