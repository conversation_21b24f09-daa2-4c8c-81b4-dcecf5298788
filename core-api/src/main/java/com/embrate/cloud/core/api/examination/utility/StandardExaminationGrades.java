package com.embrate.cloud.core.api.examination.utility;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.ExamGrade;

public class StandardExaminationGrades implements Comparable<StandardExaminationGrades> {

	private int gradeId;

	private String gradeName;

	private double gradeValue;

	private double marksRangeStart;

	private double marksRangeEnd;

	private String rangeDisplayName;

	private String remarks;

	private Integer creditScore;

	public StandardExaminationGrades() {
	}

	public StandardExaminationGrades(int gradeId, String gradeName, double gradeValue, double marksRangeStart, double marksRangeEnd, String rangeDisplayName, String remarks, Integer creditScore) {
		this.gradeId = gradeId;
		this.gradeName = gradeName;
		this.gradeValue = gradeValue;
		this.marksRangeEnd = marksRangeEnd;
		this.marksRangeStart = marksRangeStart;
		this.rangeDisplayName = rangeDisplayName;
		this.remarks = remarks;
		this.creditScore = creditScore;
	}

	public String getGradeName() {
		return gradeName;
	}

	public void setGradeName(String gradeName) {
		this.gradeName = gradeName;
	}

	public int getGradeId() {
		return gradeId;
	}

	public void setGradeId(int gradeId) {
		this.gradeId = gradeId;
	}

	public double getGradeValue() {
		return gradeValue;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public void setGradeValue(double gradeValue) {
		this.gradeValue = gradeValue;
	}

	public double getMarksRangeStart() {
		return marksRangeStart;
	}

	public void setMarksRangeStart(double marksRangeStart) {
		this.marksRangeStart = marksRangeStart;
	}

	public double getMarksRangeEnd() {
		return marksRangeEnd;
	}

	public void setMarksRangeEnd(double marksRangeEnd) {
		this.marksRangeEnd = marksRangeEnd;
	}

	public String getRangeDisplayName() {
		return rangeDisplayName;
	}

	public void setRangeDisplayName(String rangeDisplayName) {
		this.rangeDisplayName = rangeDisplayName;
	}

	public Integer getCreditScore() { return creditScore;}

	public void setCreditScore(Integer creditScore) { this.creditScore = creditScore;}

	public static Map<UUID, Map<CourseType, List<StandardExaminationGrades>>> convertExamGradeListToMap(List<ExamGrade> examGradeList){
		Map<UUID, Map<CourseType, List<StandardExaminationGrades>>> gradeSchemeWithStandardDetails = new HashMap<>();
		for(ExamGrade examGrade : examGradeList){
			UUID standardId = examGrade.getStandardId();
        	CourseType courseType = examGrade.getCourseType();

			if(!gradeSchemeWithStandardDetails.containsKey(standardId)){
				gradeSchemeWithStandardDetails.put(standardId, new HashMap<>());
			}
			if(!gradeSchemeWithStandardDetails.get(standardId).containsKey(courseType)){
				gradeSchemeWithStandardDetails.get(standardId).put(courseType, new ArrayList<StandardExaminationGrades>());
			}
			StandardExaminationGrades standardExaminationGrades = new StandardExaminationGrades(examGrade.getGradeId(), examGrade.getGradeName(), examGrade.getGradeValue(), examGrade.getMarksRangeStart(), examGrade.getMarksRangeEnd(), examGrade.getRangeDisplayName(), examGrade.getRemarks(), examGrade.getCreditScore());
			gradeSchemeWithStandardDetails.get(standardId).get(courseType).add(standardExaminationGrades);
		}
		return gradeSchemeWithStandardDetails;
	}

	@Override
	public String toString() {
		return "StandardExaminationGrades{" +
				"gradeId=" + gradeId +
				", gradeName='" + gradeName + '\'' +
				", gradeValue=" + gradeValue +
				", marksRangeStart=" + marksRangeStart +
				", marksRangeEnd=" + marksRangeEnd +
				", rangeDisplayName='" + rangeDisplayName + '\'' +
				", remarks='" + remarks + '\'' +
				", creditScore=" + creditScore +
				'}';
	}

	@Override
	public int compareTo(StandardExaminationGrades o) {
		if(this.getGradeValue() == 0.0 && o.getGradeValue() == 0.0){
			return Double.compare(o.getMarksRangeStart(), this.getMarksRangeStart());
		}
		return Double.compare(o.getGradeValue(), this.getGradeValue());
	}
}
