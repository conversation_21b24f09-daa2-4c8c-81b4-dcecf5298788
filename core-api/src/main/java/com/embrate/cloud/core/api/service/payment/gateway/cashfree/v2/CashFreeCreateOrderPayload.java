package com.embrate.cloud.core.api.service.payment.gateway.cashfree.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class CashFreeCreateOrderPayload {

    @JsonProperty("order_id")
    private final String orderId;

    @JsonProperty("order_amount")
    private final double orderAmount;

    @JsonProperty("order_currency")
    private final String orderCurrency;

    @JsonProperty("customer_details")
    private final CashFreePaymentCustomerDetailsV2 customerDetails;

    @JsonProperty("order_meta")
    private final CashFreeOrderMetadata orderMetadata;

    @JsonProperty("order_expiry_time")
    private final String orderExpiryTime;

    @JsonProperty("order_note")
    private final String orderNote;

    public CashFreeCreateOrderPayload(String orderId, double orderAmount, String orderCurrency, CashFreePaymentCustomerDetailsV2 customerDetails, CashFreeOrderMetadata orderMetadata, String orderExpiryTime, String orderNote) {
        this.orderId = orderId;
        this.orderAmount = orderAmount;
        this.orderCurrency = orderCurrency;
        this.customerDetails = customerDetails;
        this.orderMetadata = orderMetadata;
        this.orderExpiryTime = orderExpiryTime;
        this.orderNote = orderNote;
    }

    public String getOrderId() {
        return orderId;
    }

    public double getOrderAmount() {
        return orderAmount;
    }

    public String getOrderCurrency() {
        return orderCurrency;
    }

    public CashFreePaymentCustomerDetailsV2 getCustomerDetails() {
        return customerDetails;
    }

    public CashFreeOrderMetadata getOrderMetadata() {
        return orderMetadata;
    }

    public String getOrderExpiryTime() {
        return orderExpiryTime;
    }

    public String getOrderNote() {
        return orderNote;
    }

    @Override
    public String toString() {
        return "CashFreeCreateOrderPayload{" +
                "orderId='" + orderId + '\'' +
                ", orderAmount=" + orderAmount +
                ", orderCurrency='" + orderCurrency + '\'' +
                ", customerDetails=" + customerDetails +
                ", orderMetadata=" + orderMetadata +
                ", orderExpiryTime='" + orderExpiryTime + '\'' +
                ", orderNote='" + orderNote + '\'' +
                '}';
    }
}
