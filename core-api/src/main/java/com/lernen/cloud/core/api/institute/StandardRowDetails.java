package com.lernen.cloud.core.api.institute;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.util.UUID;

public class StandardRowDetails implements Comparable<StandardRowDetails> {

	private int instituteId;

	private int academicSessionId;

	private UUID standardId;

	private String standardName;

	private Stream stream;

	private int level;

	private Integer sectionId;

	private String sectionName;

	private Integer studentCount;
	private String displayName;

	private String displayNameWithSection;

	public StandardRowDetails(int instituteId, int academicSessionId,
			UUID standardId, String standardName, Stream stream, int level,
			Integer sectionId, String sectionName, Integer studentCount) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.standardId = standardId;
		this.standardName = standardName;
		this.stream = stream;
		this.level = level;
		this.sectionId = sectionId;
		this.sectionName = sectionName;
		this.studentCount = studentCount;
		this.displayName = getStandardDisplayName(stream, standardName);
		this.displayNameWithSection = getStandardDisplayName(stream,
				standardName,
				StringUtils.isEmpty(sectionName)
						? null
						: sectionName);
	}

	public StandardRowDetails(int instituteId, int academicSessionId,
			UUID standardId, String standardName, Stream stream, int level,
			Integer sectionId, String sectionName) {
		this.instituteId = instituteId;
		this.academicSessionId = academicSessionId;
		this.standardId = standardId;
		this.standardName = standardName;
		this.stream = stream;
		this.level = level;
		this.sectionId = sectionId;
		this.sectionName = sectionName;
		this.displayName = getStandardDisplayName(stream, standardName);
		this.displayNameWithSection = getStandardDisplayName(stream,
				standardName,
				StringUtils.isEmpty(sectionName)
						? null
						: sectionName);
	}

	public int getInstituteId() {
		return instituteId;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public UUID getStandardId() {
		return standardId;
	}

	public String getStandardName() {
		return standardName;
	}

	public Stream getStream() {
		return stream;
	}

	public int getLevel() {
		return level;
	}

	public Integer getSectionId() {
		return sectionId;
	}

	public String getSectionName() {
		return sectionName;
	}

	public Integer getStudentCount() {
		return studentCount;
	}

	public void setStudentCount(Integer studentCount) {
		this.studentCount = studentCount;
	}

	public String getDisplayName() {
		return displayName;
	}

	public String getDisplayNameWithSection() {
		return displayNameWithSection;
	}

	@Override
	public String toString() {
		return "StandardRowDetails [instituteId=" + instituteId
				+ ", academicSessionId=" + academicSessionId + ", standardId="
				+ standardId + ", standardName=" + standardName + ", stream="
				+ stream + ", level=" + level + ", sectionId=" + sectionId
				+ ", sectionName=" + sectionName + ", studentCount="
				+ studentCount + "]";
	}


	@JsonIgnore
	public static String getStandardDisplayName(Stream stream, String standardName) {
		return stream == null || Stream.NA.equals(stream) ? standardName : standardName + " (" + stream.getDisplayName() + ")";
	}

	@JsonIgnore
	public static String getStandardDisplayName(Stream stream, String standardName, String sectionName) {
		String standardDisplayName = getStandardDisplayName(stream, standardName);
		return StringUtils.isBlank(sectionName) ? standardDisplayName : standardDisplayName + "-" + sectionName;
	}

	@Override
	public int compareTo(StandardRowDetails o) {
		if (this.level == o.level) {
			if(StringUtils.isBlank(this.sectionName)) {
				return 1;
			}
			if(StringUtils.isBlank(o.sectionName)) {
				return -1;
			}
			return this.sectionName.compareTo(o.sectionName);
		} else if (this.level < o.level) {
			return -1;
		} else {
			return 1;
		}
	}

}
