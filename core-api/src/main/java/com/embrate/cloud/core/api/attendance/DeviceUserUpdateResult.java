package com.embrate.cloud.core.api.attendance;

import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.user.UserType;

import java.util.UUID;

/**
 * <AUTHOR>
 */

public class DeviceUserUpdateResult {

    private final boolean success;

    public DeviceUserUpdateResult(boolean success) {
        this.success = success;
    }

    public boolean isSuccess() {
        return success;
    }

    @Override
    public String toString() {
        return "DeviceUserUpdateResult{" +
                "success=" + success +
                '}';
    }
}
