/**
 * 
 */
package com.embrate.cloud.core.api.discussionboard;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
public enum DiscussionBoardEntity {

	ONLINE_LECTURE, HOMEWORK, COMPLAIN, INQUIRY, DISCUSSION;

	public static DiscussionBoardEntity getDiscussionBoardEntity(
			String discussionBoardEntity) {
		if (StringUtils.isBlank(discussionBoardEntity)) {
			return null;
		}
		for (DiscussionBoardEntity discussionBoardEntityEnum : DiscussionBoardEntity
				.values()) {
			if (discussionBoardEntityEnum.name()
					.equalsIgnoreCase(discussionBoardEntity)) {
				return discussionBoardEntityEnum;
			}
		}
		return null;
	}
}
