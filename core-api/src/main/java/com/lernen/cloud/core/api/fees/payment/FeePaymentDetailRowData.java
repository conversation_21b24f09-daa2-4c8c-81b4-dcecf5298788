package com.lernen.cloud.core.api.fees.payment;

import java.util.UUID;

import com.lernen.cloud.core.api.fees.DiscountBasicInfo;
import com.lernen.cloud.core.api.fees.FeeConfigurationBasicInfo;
import com.lernen.cloud.core.api.fees.FeeHeadConfiguration;

/**
 * 
 * <AUTHOR>
 *
 */
public class FeePaymentDetailRowData {

	private final int instituteId;

	private final UUID studentId;

	private final FeeConfigurationBasicInfo feeConfigurationBasicInfo;

	private final FeeHeadConfiguration feeHeadConfiguration;

	private final double assignedAmount;

	private final double paidAmount;

	private final double instantDiscountAmount;

	private final DiscountBasicInfo discountBasicInfo;

	private final double discountAmount;

	private final double fineAmount;

	private final UUID discountGroupId;

	private final int transactionCount;

	public FeePaymentDetailRowData(int instituteId, UUID studentId, FeeConfigurationBasicInfo feeConfigurationBasicInfo,
			FeeHeadConfiguration feeHeadConfiguration, double assignedAmount, double paidAmount,
			double instantDiscountAmount, DiscountBasicInfo discountBasicInfo, double discountAmount, double fineAmount,
			UUID discountGroupId, int transactionCount) {
		this.instituteId = instituteId;
		this.studentId = studentId;
		this.feeConfigurationBasicInfo = feeConfigurationBasicInfo;
		this.feeHeadConfiguration = feeHeadConfiguration;
		this.assignedAmount = assignedAmount;
		this.paidAmount = paidAmount;
		this.instantDiscountAmount = instantDiscountAmount;
		this.discountBasicInfo = discountBasicInfo;
		this.discountAmount = discountAmount;
		this.fineAmount = fineAmount;
		this.discountGroupId = discountGroupId;
		this.transactionCount = transactionCount;
	}

	public int getInstituteId() {
		return instituteId;
	}

	public UUID getStudentId() {
		return studentId;
	}

	public FeeConfigurationBasicInfo getFeeConfigurationBasicInfo() {
		return feeConfigurationBasicInfo;
	}

	public FeeHeadConfiguration getFeeHeadConfiguration() {
		return feeHeadConfiguration;
	}

	public double getAssignedAmount() {
		return assignedAmount;
	}

	public double getPaidAmount() {
		return paidAmount;
	}

	public double getInstantDiscountAmount() {
		return instantDiscountAmount;
	}

	public DiscountBasicInfo getDiscountBasicInfo() {
		return discountBasicInfo;
	}

	public double getDiscountAmount() {
		return discountAmount;
	}

	public UUID getDiscountGroupId() {
		return discountGroupId;
	}

	public int getTransactionCount() {
		return transactionCount;
	}

	public double getFineAmount() {
		return fineAmount;
	}

	@Override
	public String toString() {
		return "FeePaymentDetailRowData [instituteId=" + instituteId + ", studentId=" + studentId
				+ ", feeConfigurationBasicInfo=" + feeConfigurationBasicInfo + ", feeHeadConfiguration="
				+ feeHeadConfiguration + ", assignedAmount=" + assignedAmount + ", paidAmount=" + paidAmount
				+ ", instantDiscountAmount=" + instantDiscountAmount + ", discountBasicInfo=" + discountBasicInfo
				+ ", discountAmount=" + discountAmount + ", fineAmount=" + fineAmount + ", discountGroupId="
				+ discountGroupId + ", transactionCount=" + transactionCount + "]";
	}

}
