package com.lernen.cloud.pdf.identitycard.student;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.configurations.StudentIdentityCardPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutData;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.AddressUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentIdentityCardGenerator10145_10146 extends GlobalStudentIdentityCardGenerator {

	public StudentIdentityCardGenerator10145_10146(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(StudentIdentityCardGenerator10145_10146.class);

	@Override
	public DocumentOutput generateIdentityCard(StudentManager studentManager, Institute institute, StudentTransportDetails studentTransportDetails,
											   StudentIdentityCardPreferences studentIdentityCardPreferences,
											   Student student, String documentName, StaffManager staffManager) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
					documentLayoutData.getDocumentLayoutSetup(), institute, student, studentManager, 1);
			document.close();
			return getA4LandscapeIdentityCard(documentName, documentOutput, PageSize.A4.rotate(), 2, 5, 20f, 8f, 15f);
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating identity cards for institute {}, student {}",
					institute.getInstituteId(), student.getStudentId(), e);
		}
		return null;
	}

	private void generateIdentityCardDetails(Document document, DocumentLayoutData documentLayoutData,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student,
											  StudentManager studentManager, int pageNumber) throws IOException {

		/**
		 * Student Details Front
		 */
		generateStudentFrontPageDetails(document, documentLayoutData, institute, pageNumber, studentManager,
				cambriaFont, cambriaBoldFont, documentLayoutSetup, student);
	}

	private void generateStudentFrontPageDetails(Document document, DocumentLayoutData documentLayoutData,
												 Institute institute, int pageNumber, StudentManager studentManager,
												 PdfFont cambriaFont, PdfFont cambriaBoldFont,
												 DocumentLayoutSetup documentLayoutSetup, Student student) throws IOException {
		/**
		 * Bottom bar, keep this on top as we are
		 * using canvas in generateInstituteHeader which
		 * require document to be created before it,
		 * ow error occurred
		 */
		generateBottomBar(document, cambriaBoldFont, documentLayoutSetup);

		/**
		 * Institute Header
		 */
		generateFrontPageHeader(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, pageNumber, student, studentManager);


		/**
		 * Student basic details
		 */
		generateStudentBasicDetails(document, cambriaBoldFont, cambriaFont, documentLayoutSetup, student);

		generateBarCodeImage(document, documentLayoutSetup);

	}

	protected void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										   float height, float offsetX, float offsetY) throws IOException {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	private void generateStudentBasicDetails(Document document,
											 PdfFont cambriaBoldFont, PdfFont cambriaFont, DocumentLayoutSetup documentLayoutSetup,
											 Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);

		float subToTop = 0;

		String fatherName = student.getStudentFamilyInfo() == null ? "" : student.getStudentFamilyInfo().getFathersName();
		String motherName = student.getStudentFamilyInfo() == null ? "" : student.getStudentFamilyInfo().getMothersName();

		String address = AddressUtils.getStudentAddress(student);
		if(isTextGreater(address, 50)) {
			subToTop += 11;
		}

		if(isTextGreater(fatherName, 25)) {
			subToTop += 11;
		}

		if(isTextGreater(motherName, 25)) {
			subToTop += 11;
		}

		table.setFixedPosition(0, 42 - subToTop, documentLayoutSetup.getPageSize().getWidth());


		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(cambriaFont).setFontSize(6f)
				.setTextAlignment(TextAlignment.CENTER).setPaddingTop(0f).setPaddingBottom(0f);

		List<Integer> rgbBlack = EColorUtils.hex2Rgb(EColorUtils.lightBlackHexCode);

		String studentNameClassString = student.getStudentBasicInfo().getName().toUpperCase() + " (" +
				student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection() + ")";
		Paragraph studentNameParagraph = getParagraph(studentNameClassString, rgbBlack);
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(studentNameParagraph,
				cellLayoutSetup.copy().setFontSize(7f).setPdfFont(cambriaBoldFont))));


		cellLayoutSetup.setFontSize(6.5f);
		List<Integer> keyLightBlueColor = EColorUtils.hex2Rgb("#1e52cf");


		cellLayoutSetup.setPaddingLeft(0f).setTextAlignment(TextAlignment.LEFT)
				.setPaddingLeft(15f).setPaddingRight(15f);

		Paragraph admissionNumberKeyValue = getKeyValueParagraph("Admission No.: ",
				student.getStudentBasicInfo().getAdmissionNumber(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(admissionNumberKeyValue, cellLayoutSetup.copy())));

		Paragraph fatherNameKeyValue = getKeyValueParagraph("Father’s Name: ",
				student.getStudentFamilyInfo().getFathersName(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(fatherNameKeyValue, cellLayoutSetup.copy())));

		Paragraph motherNameKeyValue = getKeyValueParagraph("Mother’s Name: ",
				student.getStudentFamilyInfo().getMothersName(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(motherNameKeyValue, cellLayoutSetup.copy())));

		Paragraph addressKeyValue = getKeyValueParagraph("Address: ",
				address, keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(addressKeyValue, cellLayoutSetup.copy())));

		String dobValue = student.getStudentBasicInfo().getDateOfBirth() == null ?
				"" : DateUtils.getFormattedDate(student.getStudentBasicInfo().getDateOfBirth(),
				"dd.MMM.yyyy", User.DFAULT_TIMEZONE);
		Paragraph dobKeyValue = getKeyValueParagraph("DOB: ", dobValue, keyLightBlueColor, rgbBlack,
				cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(dobKeyValue, cellLayoutSetup.copy())));

		Paragraph phoneNumberKeyValue = getKeyValueParagraph("Contact No.: ",
				student.getStudentBasicInfo().getPrimaryContactNumber(), keyLightBlueColor, rgbBlack, cambriaBoldFont, cambriaBoldFont);
		addRow(table, documentLayoutSetup, Arrays.asList(
				new CellData(phoneNumberKeyValue, cellLayoutSetup.copy())));


		document.add(table);
	}

	private void generateBottomBar(Document document, PdfFont cambriaBoldFont, DocumentLayoutSetup documentLayoutSetup) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup bottomBarCellLayoutSetup = new CellLayoutSetup();
		bottomBarCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setFontSize(6f).setPaddingTop(3f).setPaddingBottom(3f);

		bottomBarCellLayoutSetup.setBackgroundColor("#990000");

		table.setFixedPosition(0f, 0f, documentLayoutSetup.getPageSize().getWidth());
		addRow(table, documentLayoutSetup, Arrays.asList(
				getParagraph("")), bottomBarCellLayoutSetup.copy().setPaddingTop(1f));
		document.add(table);
	}

	private void generateFrontPageHeader(Document document,
										 DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										 DocumentLayoutSetup documentLayoutSetup, Institute institute, int pageNumber,
										 Student student, StudentManager studentManager) throws MalformedURLException, IOException {

		generateInstituteDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute);

		generateSessionDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
				documentLayoutSetup, institute, student);

		generateStudentImage(document, documentLayoutSetup, institute, studentManager, student);

	}

	private void generateSessionDetails(Document document,
										DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										DocumentLayoutSetup documentLayoutSetup, Institute institute, Student student) {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setPaddingBottom(0f).setPaddingTop(0f);

		centerCellLayoutSetup.setBackgroundColor("#990000");

		List<Integer> whiteColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

//		Float rgbInstituteNameFontSize = studentIdentityCardPreferences != null &&
//				studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
//				studentIdentityCardPreferences.getInstituteNameFontSize() : 9f;

		Float rgbInstituteNameFontSize = 7f;

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("IDENTITY CARD (" +
		student.getStudentAcademicSessionInfoResponse().getAcademicSession().getShortYearDisplayName() + ")", whiteColor)),
		centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(1f).setPaddingBottom(1f));
		document.add(table);
	}

	private void generateInstituteDetails(Document document,
										  DocumentLayoutData documentLayoutData, PdfFont cambriaBoldFont, PdfFont cambriaFont,
										  DocumentLayoutSetup documentLayoutSetup, Institute institute) throws IOException {

		Table table = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup centerCellLayoutSetup = new CellLayoutSetup();
		centerCellLayoutSetup.setPdfFont(cambriaBoldFont).setTextAlignment(TextAlignment.CENTER)
				.setPaddingLeft(30f).setPaddingBottom(0f).setPaddingTop(0f);

		centerCellLayoutSetup.setBackgroundColor("#01003c");

		List<Integer> whiteColor = EColorUtils.hex2Rgb(EColorUtils.WHITE_COLOR_HEX_CODE);

		List<Integer> yellowColor = EColorUtils.hex2Rgb("#ffff00");

//		Float rgbInstituteNameFontSize = studentIdentityCardPreferences != null &&
//				studentIdentityCardPreferences.getInstituteNameFontSize() != null ?
//				studentIdentityCardPreferences.getInstituteNameFontSize() : 8f;
//		Float rgbLetterHead1FontSize = studentIdentityCardPreferences != null &&
//				studentIdentityCardPreferences.getLetterHead1FontSize() != null ?
//				studentIdentityCardPreferences.getLetterHead1FontSize() : 4f;
//		Float rgbLetterHead2FontSize = studentIdentityCardPreferences != null &&
//				studentIdentityCardPreferences.getLetterHead2FontSize() != null ?
//				studentIdentityCardPreferences.getLetterHead2FontSize() : 4f;

		Float rgbInstituteNameFontSize = 8f;
		Float rgbLetterHead1FontSize = 6f;
		Float rgbLetterHead2FontSize = 6f;

		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase(), whiteColor)),
				centerCellLayoutSetup.copy().setFontSize(rgbInstituteNameFontSize).setPaddingTop(3f));

		centerCellLayoutSetup.setPdfFont(cambriaFont);
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Naibasti, Dadri, Gautam Buddh Nagar,", yellowColor)),
				centerCellLayoutSetup.copy().setFontSize(rgbLetterHead1FontSize));
		addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("U.P. - 203207; Ph : (+91)-**********", yellowColor)),
				centerCellLayoutSetup.copy().setFontSize(rgbLetterHead2FontSize).setPaddingBottom(3f));

		document.add(table);

		generateDynamicImageProvider(documentLayoutData, -5, 6, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

	}

	private void generateStudentImage(Document document, DocumentLayoutSetup documentLayoutSetup,
									  Institute institute, StudentManager studentManager, Student student) throws MalformedURLException, IOException {

		byte[] image = getStudentImage(institute.getInstituteId(), student, studentManager);
		if (image == null) {
			image = ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME);
		}

		float imageWidth = (documentLayoutSetup.getPageSize().getWidth() / 3) - 5f;
		float imageHeight = (documentLayoutSetup.getPageSize().getWidth() / 3) - 5f;
		float imageOffsetX = (documentLayoutSetup.getPageSize().getWidth() / 3) + 1.6f;
		float imageOffsetY = (documentLayoutSetup.getPageSize().getHeight() / 2) + 14f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight,
				imageOffsetX, imageOffsetY);

	}

	protected DocumentLayoutData generateIdentityCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		/**
		 * 	aadhar card size - 8.5cmx5.5cm
		 * 	in inches - 3.34646x2.16535
		 * 	1 inch  = 72 points
		 * 	3.34646*72 & 2.16535*72
		 * 	240.94512f X 155.9052f
		 */
		PageSize pageSize = new PageSize(156f, 241f);
		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(pageSize);
		float contentFontSize = 9f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

//		float logoWidth = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoWidth() != null
//				? studentIdentityCardPreferences.getInstituteLogoWidth() : 30f;
//		float logoHeight = studentIdentityCardPreferences != null && studentIdentityCardPreferences.getInstituteLogoHeight() != null
//				? studentIdentityCardPreferences.getInstituteLogoHeight() : 30f;

		float logoWidth = 30f;
		float logoHeight = 30f;

		int instituteId = institute.getInstituteId();
		DocumentLayoutData documentLayoutData = new DocumentLayoutData(document, documentLayoutSetup, getRegularFont(),
				getRegularBoldFont(),
				contentFontSize, 0f, logoWidth, logoHeight,
				LogoProvider.INSTANCE.getLogo(instituteId),
				null, null);

		documentLayoutData.setImageFrame(ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME));
		return documentLayoutData;
	}

	public DocumentLayoutSetup initDocumentLayoutSetup(PageSize defaultPageSize) {
		return initDocumentLayoutSetup(false, defaultPageSize,
				0f, 0f, 0f, 0f);
	}

	@Override
	public DocumentOutput generateIdentityCards(StudentManager studentManager, Institute institute, List<StudentTransportDetails> studentTransportDetails,
												StudentIdentityCardPreferences studentIdentityCardPreferences, List<Student> students,
												String documentName, StaffManager staffManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutData documentLayoutData = generateIdentityCardLayoutData(institute, documentOutput);
			Document document = documentLayoutData.getDocument();

			PdfFont cambriaBoldFont = getCambriaBoldFont();
			PdfFont cambriaFont = getCambriaFont();

			int pageNumber = 1;
			for (Student student : students) {

				generateIdentityCardDetails(document, documentLayoutData, cambriaBoldFont, cambriaFont,
						documentLayoutData.getDocumentLayoutSetup(), institute, student,
						studentManager, pageNumber);

				if (pageNumber != students.size()) {
					documentLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;

			}

			documentLayoutData.getDocument().close();

			return getA4LandscapeIdentityCard(documentName, documentOutput, PageSize.A4.rotate(), 2, 5, 20f, 8f, 15f);
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("Error while generating student identity card institute {}", institute.getInstituteId(), e);
		}
		return null;
	}

	private void generateBarCodeImage(Document document, DocumentLayoutSetup documentLayoutSetup) throws IOException {

		byte[] image = ImageProvider.INSTANCE.getImage(ImageProvider._10145_10146_STUDENT_ICARD_BARCODE);
		if (image == null) {
			return;
		}

		float imageWidth = documentLayoutSetup.getPageSize().getWidth() - 10f;
		float imageHeight = 16f;

		float imageOffsetX = 5f;
		float imageOffsetY = 10f;

		generateImage(document, documentLayoutSetup, image, imageWidth, imageHeight, imageOffsetX, imageOffsetY);

	}
}
