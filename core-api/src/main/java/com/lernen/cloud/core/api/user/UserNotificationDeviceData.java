package com.lernen.cloud.core.api.user;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.collections.CollectionUtils;

import com.embrate.cloud.core.api.application.mobile.MobileAppPlatform;

/**
 * 
 * <AUTHOR>
 *
 */
public class UserNotificationDeviceData {

	private final UUID userId;

	private final List<UserAppToken> appPlatformTokens;

	public UserNotificationDeviceData(UUID userId,
			List<UserAppToken> appPlatformTokens) {
		this.userId = userId;
		this.appPlatformTokens = appPlatformTokens;
	}

	public UUID getUserId() {
		return userId;
	}

	public List<UserAppToken> getAppPlatformTokens() {
		return appPlatformTokens;
	}

	private Map<MobileAppPlatform, List<UserAppToken>> getTokenMap(
			List<UserAppToken> userAppTokens) {

		final Map<MobileAppPlatform, List<UserAppToken>> tokensMap = new HashMap<>();
		if (CollectionUtils.isEmpty(userAppTokens)) {
			return tokensMap;
		}

		for (UserAppToken userAppToken : userAppTokens) {
			if (tokensMap.containsKey(userAppToken.getAppPlatform())) {
				tokensMap.get(userAppToken.getAppPlatform()).add(userAppToken);
			} else {
				List<UserAppToken> platformTokens = new ArrayList<>();
				platformTokens.add(userAppToken);
				tokensMap.put(userAppToken.getAppPlatform(), platformTokens);
			}
		}
		return tokensMap;
	}

	@Override
	public String toString() {
		return "UserNotificationDeviceData [userId=" + userId
				+ ", appPlatformTokens=" + appPlatformTokens + "]";
	}

}
