package com.embrate.cloud.core.api.hpc.web;

import com.embrate.cloud.core.api.hpc.layout.HPCSection;
import com.embrate.cloud.core.api.hpc.utils.HPCExamType;
import com.embrate.cloud.core.api.hpc.utils.HPCFieldStatus;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class HPCWebForm {

	private final String name;
	private final List<HPCWebSection> teacherSections;
	private final List<HPCWebSection> parentSections;
	private final HPCFilledStatus teacherSectionStatus;
	private final HPCFilledStatus parentSectionStatus;
	private final HPCFieldStatus teacherSectionFieldStatus;
	private final HPCFieldStatus parentSectionFieldStatus;

	public HPCWebForm(String name, List<HPCWebSection> teacherSections, List<HPCWebSection> parentSections,
					  HPCFieldStatus teacherSectionFieldStatus, HPCFieldStatus parentSectionFieldStatus) {
		this.name = name;
		this.teacherSections = teacherSections;
		this.parentSections = parentSections;
		this.teacherSectionStatus = computeHPCFilledStatus(teacherSections);
		this.parentSectionStatus = computeHPCFilledStatus(parentSections);
		this.teacherSectionFieldStatus = teacherSectionFieldStatus;
		this.parentSectionFieldStatus = parentSectionFieldStatus;
	}

	private HPCFilledStatus computeHPCFilledStatus(List<HPCWebSection> hpcWebSections) {
		// Iterate over the child containers and determine the parent's status
		for (HPCWebSection hpcWebSection : hpcWebSections) {
			HPCFilledStatus hpcFilledStatus = hpcWebSection.getContainer().getHpcFilledStatus();
            if (hpcFilledStatus == HPCFilledStatus.PENDING) {
				return HPCFilledStatus.PENDING;
            }
		}
		return HPCFilledStatus.COMPLETED;
	}

	public String getName() {
		return name;
	}

	public List<HPCWebSection> getTeacherSections() {
		return teacherSections;
	}

	public List<HPCWebSection> getParentSections() {
		return parentSections;
	}

	public HPCFilledStatus getTeacherSectionStatus() {
		return teacherSectionStatus;
	}

	public HPCFilledStatus getParentSectionStatus() {
		return parentSectionStatus;
	}

	public HPCFieldStatus getParentSectionFieldStatus() {
		return parentSectionFieldStatus;
	}

	public HPCFieldStatus getTeacherSectionFieldStatus() {
		return teacherSectionFieldStatus;
	}

	@Override
	public String toString() {
		return "HPCWebForm{" +
				"name='" + name + '\'' +
				", teacherSections=" + teacherSections +
				", parentSections=" + parentSections +
				", teacherSectionStatus=" + teacherSectionStatus +
				", parentSectionStatus=" + parentSectionStatus +
				", parentSectionFieldStatus=" + parentSectionFieldStatus +
				", teacherSectionFieldStatus=" + teacherSectionFieldStatus +
				'}';
	}
}
