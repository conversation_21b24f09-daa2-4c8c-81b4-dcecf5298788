package com.embrate.cloud.core.api.homework;

import com.lernen.cloud.core.api.course.Course;

import java.util.List;

/**
 * <AUTHOR>
 * @created_at 02/11/23 : 22:44
 **/
public class DatewiseHomeworkDetails implements Comparable<DatewiseHomeworkDetails> {

    private final int date;

    private final List<HomeworkDetails> homeworkDetailsList;

    public DatewiseHomeworkDetails(int date, List<HomeworkDetails> homeworkDetailsList) {
        this.date = date;
        this.homeworkDetailsList = homeworkDetailsList;
    }

    public int getDate() {
        return date;
    }

    public List<HomeworkDetails> getHomeworkDetailsList() {
        return homeworkDetailsList;
    }

    @Override
    public String toString() {
        return "DatewiseHomeworkDetails{" +
                "date=" + date +
                ", homeworkDetailsList=" + homeworkDetailsList +
                '}';
    }


    @Override
    public int compareTo(DatewiseHomeworkDetails o) {
        return Integer.compare(o.date, this.date);
    }
}

