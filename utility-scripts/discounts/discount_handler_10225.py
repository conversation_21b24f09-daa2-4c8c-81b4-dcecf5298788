import sys
import requests
import json

headers =  {"Content-Type": "application/json"}
BASE_URL = "https://api.embrate.com"
ACCESS_TOKEN = ""

# BASE_URL = "http://127.0.0.1:8080/data-server"
# ACCESS_TOKEN = "29a1b343-6947-40f0-93a0-d393fccc4284"

CREATE_STUDENT_DISCOUNT_STRUCTURE_API_URL = BASE_URL + "/2.0/fee-discount/add-student-discount-structure/{student_id}?institute_id={institute_id}&academic_session_id={academic_session_id}&user_id={user_id}&access_token={access_token}"
CREATE_DISCOUNT_STRUCTURE_API_URL = BASE_URL + "/2.0/fee-discount/discount-structure?institute_id={institute_id}&academic_session_id={academic_session_id}&user_id={user_id}&access_token={access_token}"
ASSIGN_DISCOUNT_STRUCTURE_API_URL = BASE_URL + "/2.0/fee-discount/assign-discount-structure?institute_id={institute_id}&academic_session_id={academic_session_id}&user_id={user_id}&access_token={access_token}"
GET_DISCOUNT_STRUCTURE_API_URL = BASE_URL + "/2.0/fee-discount/discount-structure?institute_id={institute_id}&academic_session_id={academic_session_id}&access_token={access_token}"

GET_FEE_API_URL = BASE_URL + "/2.0/fees/fee-configuration/instituteId/{institute_id}?academic_session_id={academic_session_id}&access_token={access_token}"
GET_FEE_HEAD_API_URL = BASE_URL + "/2.0/fees/fee-head-configuration/instituteId/{institute_id}?access_token={access_token}"

GET_STUDENT_API_URL = BASE_URL + "/2.0/student/institute-students/{institute_id}/lite?access_token={access_token}"


def get_fees(institute_id, academic_session_id):
    response = requests.get(GET_FEE_API_URL.format(institute_id = institute_id, academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    fee_map = {}
    for fee in data:
        fee_map[fee['feeConfigurationBasicInfo']['feeName'].lower()] = fee['feeConfigurationBasicInfo']['feeId']

    return fee_map

def get_fee_heads(institute_id):
    response = requests.get(GET_FEE_HEAD_API_URL.format(institute_id = institute_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    fee_head_map = {}
    for fee_head in data:
        fee_head_map[fee_head['feeHeadConfiguration']['feeHead'].lower()] = fee_head['feeHeadConfiguration']['feeHeadId']

    return fee_head_map

def get_discount_structures(institute_id, academic_session_id):
    response = requests.get(GET_DISCOUNT_STRUCTURE_API_URL.format(institute_id = institute_id,  academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    data = json.loads(response.content)
    discount_map = {}
    for discount in data:
        discount_map[discount['feeDiscountMetadata']['name'].lower()] = discount['feeDiscountMetadata']['discountStructureId']

    return discount_map

def get_students(institute_id, academic_session_id):
    print("request send")
    response = requests.get(GET_STUDENT_API_URL.format(institute_id = institute_id,  academic_session_id = academic_session_id, access_token = ACCESS_TOKEN), headers=headers)
    print("response received")
    data = json.loads(response.content)
    student_map = {}
    for student in data:
        student_map[student['admissionNumber'].lower()] = student['studentId']

    return student_map


def create_discount_structure(institute_id, academic_session_id, user_id, payload):
    try:
        print("\n creating structure = " + str(payload) + "\n")
        response = requests.post(CREATE_DISCOUNT_STRUCTURE_API_URL.format(institute_id = institute_id, academic_session_id = academic_session_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(payload), headers=headers)
        if response.status_code == 200:
            print("Success for payload " + str(payload))
            return True
        else:
            print("Error " + str(response.status_code) + " for " + str(payload) + " error = " + str(response.text))
            return False
    except Exception as e:
        print("Error for payload " + str(payload))
        return False

def create_student_system_discount_structure(institute_id, academic_session_id, user_id, student_id, payload):
    try:
        print("\n creating system structure & assignment = " + str(payload) + "\n")
        response = requests.post(CREATE_STUDENT_DISCOUNT_STRUCTURE_API_URL.format(student_id = student_id, institute_id = institute_id, academic_session_id = academic_session_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(payload), headers=headers)
        if response.status_code == 200:
            print("Success for payload " + str(payload))
            return True
        else:
            print("Error " + str(response.status_code) + " for " + str(payload) + " error = " + str(response.text))
            return False
    except Exception as e:
        print("Error for payload " + str(payload))
        return False

def assign_discount_structure(institute_id, academic_session_id, user_id, payload):
    try:
        print("\n Assigning structure = " + str(payload) + "\n")
        response = requests.post(ASSIGN_DISCOUNT_STRUCTURE_API_URL.format(institute_id = institute_id, academic_session_id = academic_session_id, user_id = user_id, access_token = ACCESS_TOKEN), data=json.dumps(payload), headers=headers)
        if response.status_code == 200:
            print("Success for payload " + str(payload))
            return True
        else:
            print("Error " + str(response.status_code) + " for " + str(payload) + " error = " + str(response.text))
            return False
    except Exception as e:
        print("Error for payload " + str(payload))
        print(e)
        return False

def create_all_discount_structures(institute_id, academic_session_id, user_id, discount_structures_map, fee_map, fee_head_map, dry_run ):
    final_structures = []
    for dis_structure_name in discount_structures_map:
        dis_structure = discount_structures_map[dis_structure_name]
        fee_id_fee_heads_list = []
        for fee_name in dis_structure["fee_payload"]:
            if fee_name not in fee_map:
                print("Error fee not present = " + fee_name)
                continue
            fee_payload = dis_structure["fee_payload"][fee_name]
            fee_id = fee_map[fee_name]
            fee_head_amount_list = []
            for fee_head_name in fee_payload:
                if fee_head_name not in fee_head_map:
                    print("Error fee head not present = " + fee_head_name)
                    continue
                fh_payload = fee_payload[fee_head_name]
                fee_head_id = fee_head_map[fee_head_name]
                amount = float(fh_payload["amount"].replace(",",""))
                fee_head_amount_list.append({"feeHeadId" : fee_head_id, "feeEntity" : "INSTITUTE", "amount" : amount , "isPercentage" : fh_payload["percent"]})
            fee_id_fee_heads_list.append({"feeId" : fee_id, "feeHeadAmountList" : fee_head_amount_list})
        entity_fee_assignment_payloads = [{"entityId" : str(institute_id), "feeEntity" : "INSTITUTE", "feeIdFeeHeadsList" : fee_id_fee_heads_list}]

        final_structures.append({"structureName" : dis_structure_name, 'discountStructureType' : "CUSTOM", "description" : dis_structure['description'], "entityFeeAssignmentPayloads" : entity_fee_assignment_payloads})

    print(final_structures)


    total = 0
    success = 0
    failure = 0
    if dry_run == False:
        for structure in final_structures:
            total += 1
            if(create_discount_structure(institute_id, academic_session_id, user_id, [structure])):
                success += 1
            else:
                failure += 1

    print("structure creation total = {total}, success = {success}, failure = {failure}".format(total = total, success = success, failure = failure))

    return final_structures

def create_and_assign_system_discount_structures(institute_id, academic_session_id, user_id, final_student_system_structure_discount_map, fee_map, fee_head_map, student_map, dry_run ):
    final_structures = {}
    for adm in final_student_system_structure_discount_map:
        if adm not in student_map:
           print("Error admission number  not present = " + adm)
           continue
        student_id = student_map[adm]

        dis_structure = final_student_system_structure_discount_map[adm]
        fee_id_fee_heads_list = []
        for fee_name in dis_structure["fee_payload"]:
            if fee_name not in fee_map:
                print("Error fee not present = " + fee_name)
                continue
            fee_payload = dis_structure["fee_payload"][fee_name]
            fee_id = fee_map[fee_name]
            fee_head_amount_list = []
            for fee_head_name in fee_payload:
                if fee_head_name not in fee_head_map:
                    print("Error fee head not present = " + fee_head_name)
                    continue
                fh_payload = fee_payload[fee_head_name]
                fee_head_id = fee_head_map[fee_head_name]
                amount = float(fh_payload["amount"])
                fee_head_amount_list.append({"feeHeadId" : fee_head_id, "feeEntity" : "INSTITUTE", "amount" : amount , "isPercentage" : fh_payload["percent"]})
            fee_id_fee_heads_list.append({"feeId" : fee_id, "feeHeadAmountList" : fee_head_amount_list})
        entity_fee_assignment_payloads = [{"entityId" : str(institute_id), "feeEntity" : "INSTITUTE", "feeIdFeeHeadsList" : fee_id_fee_heads_list}]

        final_structures[student_id] = {"structureName" : dis_structure['name'], 'metadata' : {'title' : dis_structure['name']}, 'discountStructureType' : "SYSTEM", "description" : dis_structure['description'], "entityFeeAssignmentPayloads" : entity_fee_assignment_payloads}

    print(final_structures)


    total = 0
    success = 0
    failure = 0
    if dry_run == False:
        for student_id in final_structures:
            payload = final_structures[student_id]
            total += 1
            if(create_student_system_discount_structure(institute_id, academic_session_id, user_id, student_id, [payload])):
                success += 1
            else:
                failure += 1

    print("system structure creation and assignment total = {total}, success = {success}, failure = {failure}".format(total = total, success = success, failure = failure))

    return final_structures

def assign_all_discount_structures(institute_id, academic_session_id, user_id, assignment_payload, student_map, discount_map, dry_run):
    final_student_map = {}
    for adm in assignment_payload:
        if adm not in student_map:
            print("Student not found = " + adm)
            continue
        student_id =  student_map[adm]
        final_discounts = set()

        for discount_original in assignment_payload[adm]:
            discount = discount_original.lower()
            if discount not in discount_map:
                print("Discount not found = " + discount)
                continue
            final_discounts.add(discount_map[discount])

        final_student_map[student_id] = final_discounts

    print(final_student_map)

    total = 0
    success = 0
    failure = 0
    if dry_run == False:
        for student in final_student_map:
            total += 1
            payload = final_student_map[student]
            s_list = []
            for sid in payload:
                s_list.append(sid)
            final_payload = {"studentIds" : [student], "discountStructureIds" : s_list}
            if(assign_discount_structure(institute_id, academic_session_id, user_id, final_payload)):
                success += 1
            else:
                failure += 1

    print("assignment total = {total}, success = {success}, failure = {failure}".format(total = total, success = success, failure = failure))



def get_payloads_from_file(file_path, header):
    file = open(file_path, "r")
    success_count = 0
    failure_count = 0
    exception_count = 0

    admission_number_index  = 0
    system_structure_index  = 6
    dis_structure_name_index  = 1
    fee_id_index  = 2
    fee_head_id_index  = 3
    amount_index  = 4
    percent_index  = 5
    remark_index  = 7

    discount_structure_map = {}
    student_discount_assignment_map = {}

    student_system_structure_discount_map = {}

    structure_count = 1
    for line in file:
        if header:
            header = False
            continue
        tokens = line.split("\n")[0].strip().split(",")
        admission_number_original = tokens[admission_number_index].strip()
        admission_number = admission_number_original.lower()
        system_structure = tokens[system_structure_index].strip().lower()
        dis_structure_name = tokens[dis_structure_name_index].strip()
        fee_id = tokens[fee_id_index].strip().lower()
        fee_head_id = tokens[fee_head_id_index].strip().lower()
        amount = tokens[amount_index].strip().replace(",","")
        percent_str = tokens[percent_index].strip()
        remark = ""
        if remark_index < len(tokens):
            remark = tokens[remark_index].strip()

        percent_bool = False
        if percent_str == "Yes":
            percent_bool = True
        else:
            percent_bool = False

        final_structure_name = dis_structure_name
        if system_structure == "yes":
#             final_structure_name = "{dis_structure_name} - {admission_number_val} - {structure_count_val}".format(admission_number_val=admission_number_original, dis_structure_name = dis_structure_name, structure_count_val = structure_count)
#             structure_count += 1
            if admission_number in student_system_structure_discount_map:
                system_structure = student_system_structure_discount_map[admission_number]
                system_structure['names'].add(final_structure_name)
                system_structure['descriptions'].add(remark)
                ds = system_structure["fee_payload"]
                if fee_id in ds:
                    fee_map_existing = ds[fee_id]
                    if fee_head_id in fee_map_existing:
                        # Assuming same percent flag for all student based system discounts
                        fee_map_existing[fee_head_id] = {'amount' : float(amount) + float(fee_map_existing[fee_head_id][amount]), 'percent' : percent_bool}
                    else:
                        fee_map_existing[fee_head_id] = {'amount' : float(amount), 'percent' : percent_bool}
                else:
                    ds[fee_id] = {fee_head_id : {'amount' : float(amount), 'percent' : percent_bool}}
            else:
                structures = set()
                structures.add(final_structure_name)

                descriptions = set()
                descriptions.add(remark)
                student_system_structure_discount_map[admission_number] = {'names' : structures, 'descriptions' : descriptions, "fee_payload" : {fee_id : {fee_head_id : {'amount' : float(amount), 'percent' : percent_bool}}}}
        else :
            if admission_number in student_discount_assignment_map:
                structures = student_discount_assignment_map[admission_number]
                structures.add(final_structure_name)
                student_discount_assignment_map[admission_number] = structures
            else:
                structures = set()
                structures.add(final_structure_name)
                student_discount_assignment_map[admission_number] = structures


            if final_structure_name in discount_structure_map:
                ds = discount_structure_map[final_structure_name]["fee_payload"]
                if fee_id in ds:
                    fee_map_existing = ds[fee_id]
                    fee_map_existing[fee_head_id] = {'amount' : amount, 'percent' : percent_bool}
                else:
                    ds[fee_id] = {fee_head_id : {'amount' : amount, 'percent' : percent_bool}}
            else:
                discount_structure_map[final_structure_name] = {"description" : remark, "fee_payload" : {fee_id : {fee_head_id : {'amount' : amount, 'percent' : percent_bool}}}}

    print(discount_structure_map)
    print("\n-----\n")
    print(student_discount_assignment_map)
    print("\n-----\n")
    print(student_system_structure_discount_map)

    final_student_system_structure_discount_map = {}
    for adm in student_system_structure_discount_map:
        data = student_system_structure_discount_map[adm]
        final_name = ""
        delimiter = ""
        for name in data['names']:
            final_name = final_name + delimiter + name
            delimiter = "/"

        final_desc = ""
        desc_delimiter = ""
        for description in data['descriptions']:
            if description is None or description.strip() == '':
                continue
            final_desc = final_desc + desc_delimiter + description.strip()
            desc_delimiter = "/"
        final_student_system_structure_discount_map[adm] = {'name' : final_name, 'description' : final_desc, "fee_payload" : data['fee_payload']}

    print("\n-----\n")
    print(final_student_system_structure_discount_map)

    file.close()

    return {"discount_structure_map" : discount_structure_map, "student_discount_assignment_map" : student_discount_assignment_map, 'final_student_system_structure_discount_map' : final_student_system_structure_discount_map}


def create_and_assign_structures(institute_id, academic_session_id, user_id, file_path, header, dry_run):
    final_payloads = get_payloads_from_file(file_path, header)

    fee_map = get_fees(institute_id, academic_session_id)
    print(fee_map)

    fee_head_map = get_fee_heads(institute_id)
    print(fee_head_map)

    student_map = get_students(institute_id, academic_session_id)
    print(student_map)

    create_all_discount_structures(institute_id, academic_session_id, user_id, final_payloads["discount_structure_map"], fee_map, fee_head_map, dry_run)
    create_and_assign_system_discount_structures(institute_id, academic_session_id, user_id, final_payloads["final_student_system_structure_discount_map"], fee_map, fee_head_map, student_map, dry_run)

    discount_map = get_discount_structures(institute_id, academic_session_id)
    print(discount_map)
    assign_all_discount_structures(institute_id, academic_session_id, user_id, final_payloads["student_discount_assignment_map"], student_map,  discount_map, dry_run)



# create_and_assign_structures(10406, 293, "4838e830-2da4-400c-a3eb-b183b85707b0", "/Users/<USER>/Desktop/discount_assignment_data.csv", True, True)
# create_and_assign_structures(10406, 293, "4838e830-2da4-400c-a3eb-b183b85707b0", "/Users/<USER>/Desktop/discount_assignment_data.csv", True, False)

# create_and_assign_structures(10227, 364, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Desktop/fee_discount_data_output.csv", True, False)
# create_and_assign_structures(10225, 108, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Desktop/10225/final_student_discount_1022_22_23.csv", True, False)
# create_and_assign_structures(10228, 143, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Embrate/10228/discount_assign_10228_2223.csv", True, False)
# create_and_assign_structures(10226, 127, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Embrate/10225_ALL/10226/discount_assign_10226_127.csv", True, False)
# create_and_assign_structures(10275, 171, "ebe55c66-03bd-4191-9c2f-c8de53c3d583", "/Users/<USER>/Embrate/data_ingestion/10275/discount_10275_171.csv", True, True)
# create_and_assign_structures(10275, 171, "ebe55c66-03bd-4191-9c2f-c8de53c3d583", "/home/<USER>/data_ingestion/discount_10275_171.csv", True, True)
# create_and_assign_structures(10355, 244, "21338671-11b5-4695-a904-cf6e736eec8a", "/Users/<USER>/Lernen/data_ingestion/10355_discount_data_23-24.csv", True, True)
# create_and_assign_structures(10355, 244, "21338671-11b5-4695-a904-cf6e736eec8a", "/Users/<USER>/Lernen/data_ingestion/10355_discount_data_23-24.csv", True, False)

# create_and_assign_structures(10225, 251, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Desktop/fee_discount_10225_2122.csv", True, True)
# create_and_assign_structures(10225, 251, "f0484d4b-9608-4d5a-a97b-6ef919022b10", "/Users/<USER>/Desktop/fee_discount_10225_2122.csv", True, False)
