package com.embrate.cloud.pdf.admission.form;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.student.FilterationCriteria;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.transport.StudentTransportDetails;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.lib.transport.configuration.TransportAssignmentManager;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class AdmissionFormHandler {

	private static final Logger logger = LogManager.getLogger(AdmissionFormHandler.class);

	private final AdmissionFormGeneratorFactory admissionFormGeneratorFactory;
	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final TransportAssignmentManager transportAssignmentManager;

	public AdmissionFormHandler(InstituteManager instituteManager, StudentManager studentManager, TransportAssignmentManager transportAssignmentManager, AssetProvider assetProvider) {
		this.admissionFormGeneratorFactory = new AdmissionFormGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.transportAssignmentManager = transportAssignmentManager;
	}

	public DocumentOutput generateAdmissionForm(int instituteId, UUID studentId, int academicSessionId) {
		if (instituteId <= 0 || academicSessionId <= 0 || studentId == null) {
			logger.error("Invalid institute id or academicSession id or student id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or student id"));
		}

		// List<Student> studentInAllSessions = studentManager.getStudentInAllSessions(instituteId, studentId);
		// if (CollectionUtils.isEmpty(studentInAllSessions)) {
		// 	logger.error("No student found in institute {} with student id {}", instituteId, studentId);
		// 	return null;
		// }
		// Student student = studentInAllSessions.get(0);
		final Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		logger.info("Generating admission form for instituteId {}, student id {} ", instituteId, studentId);

		AdmissionFormGenerator admissionFormGenerator = admissionFormGeneratorFactory
				.getAdmissionFormGenerator(instituteId);
		String fileName = student.getStudentBasicInfo().getName() + ".pdf";

		StudentTransportDetails studentTransportDetails = transportAssignmentManager.getStudentCurrentTransportDetails(instituteId, academicSessionId,studentId);
	
		return admissionFormGenerator.generateAdmissionForm(studentManager, instituteManager.getInstitute(instituteId),
				student.getStudentAcademicSessionInfoResponse().getAcademicSession() ,studentTransportDetails,
				true, student, fileName);
	}

	public DocumentOutput generateBulkAdmissionForm(int instituteId, int academicSessionId, FilterationCriteria filterationCriteria) {
		if (instituteId <= 0 || academicSessionId <= 0) {
			logger.error("Invalid institute id or academicSession id or student id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or student id"));
		}

		final List<Student> studentList = studentManager.searchStudentsInAcademicSesisonWithFilter(instituteId,
				academicSessionId, new HashSet<>(Arrays.asList(StudentStatus.ENROLLED)), filterationCriteria);

		logger.info("Generating bulk admission form for instituteId {}, academic session id {} ", instituteId, academicSessionId);

		AdmissionFormGenerator admissionFormGenerator = admissionFormGeneratorFactory
				.getAdmissionFormGenerator(instituteId);
		String fileName = "BulkAdmissionForm.pdf";

		//TODO:Add support of transport in bulk admission forms
		//Currently bulk admission form generation is only supported for 10130 institute
		return admissionFormGenerator.generateBulkAdmissionForm(studentManager, instituteManager.getInstitute(instituteId),
				instituteManager.getAcademicSession(instituteId, academicSessionId),
				null, true, studentList, fileName);
	}

	public DocumentOutput generateStaticAdmissionForm(int instituteId, int academicSessionId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id"));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academicSession id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid academicSession id"));
		}

		logger.info("Generating static admission form for instituteId {}, ", instituteId);

		AcademicSession academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
		boolean generateSpecificStaticForm = admissionFormGeneratorFactory.generateSpecificStaticForm(instituteId);
		String fileName = "AdmissionForm" + academicSession.getShortYearDisplayName() + ".pdf";
		if(generateSpecificStaticForm){
			StaticAdmissionFormGenerator staticAdmissionFormGenerator = admissionFormGeneratorFactory
					.getStaticAdmissionFormGenerator(instituteId);

			return staticAdmissionFormGenerator.generateStaticAdmissionForm(instituteManager.getInstitute(instituteId),
					academicSession, fileName);
		}

		AdmissionFormGenerator admissionFormGenerator = admissionFormGeneratorFactory
				.getAdmissionFormGenerator(instituteId);
		
		return admissionFormGenerator.generateAdmissionForm(studentManager, instituteManager.getInstitute(instituteId), academicSession ,null,
				false, null, fileName);
	}

}
