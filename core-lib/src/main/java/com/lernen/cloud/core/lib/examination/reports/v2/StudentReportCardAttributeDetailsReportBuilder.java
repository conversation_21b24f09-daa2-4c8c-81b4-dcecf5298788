package com.lernen.cloud.core.lib.examination.reports.v2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.report.config.PDFConfigs;
import com.itextpdf.kernel.geom.PageSize;
import com.lernen.cloud.core.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.lernen.cloud.core.api.examination.report.ExamReportCardConfiguration;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.StudentReportCardAttributeDetailsPayload;
import com.lernen.cloud.core.api.examination.report.StudentReportCardAttributeDetailsStore;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.examination.ExamReportCardManager;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.lib.examination.reports.v2.StudentReportSortingService;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.embrate.cloud.core.utils.institute.StandardUtils;

public class StudentReportCardAttributeDetailsReportBuilder extends
        ReportBuilder<StudentReportCardAttributeDetailsPayload, StudentReportCardAttributeDetailsStore> {

    private static final Logger logger = LogManager.getLogger(StudentReportCardAttributeDetailsReportBuilder.class);

    private final ExamReportCardManager examReportCardManager;
    private final StudentReportSortingService studentReportSortingService;

    public StudentReportCardAttributeDetailsReportBuilder(UserPermissionManager userPermissionManager,
            InstituteManager instituteManager, ExamReportCardManager examReportCardManager) {
        super(userPermissionManager, instituteManager);
        this.examReportCardManager = examReportCardManager;
        this.studentReportSortingService = new StudentReportSortingService();
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata("STUDENT_REPORT_CARD_ATTRIBUTE_DETAILS_REPORT",
                "Student Report Card Attribute Details", Module.EXAMINATION,
                "Report containing student report card attribute details");
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return Arrays.asList(
                ReportHeaderAttribute.EXAM_SR_NO,
                ReportHeaderAttribute.ADMISSION_No,
                ReportHeaderAttribute.ROLL_NO,
                ReportHeaderAttribute.NAME.markMandatory(),
                ReportHeaderAttribute.CLASS,
                ReportHeaderAttribute.FATHER_NAME,
                ReportHeaderAttribute.STUDENT_MOTHER_NAME,
                ReportHeaderAttribute.DOB,
                ReportHeaderAttribute.STUDENT_PRIMARY_CONTACT,
                ReportHeaderAttribute.PERCENTAGE,
                ReportHeaderAttribute.GRADE,
                ReportHeaderAttribute.DIVISION,
                ReportHeaderAttribute.RESULT,
                ReportHeaderAttribute.RANK,
                ReportHeaderAttribute.ATTENDANCE,
                ReportHeaderAttribute.STUDENT_SESSION_HEIGHT,
                ReportHeaderAttribute.STUDENT_SESSION_WEIGHT,
                ReportHeaderAttribute.REMARKS,
                ReportHeaderAttribute.TC_REMARKS,
                new ReportHeaderAttribute("date_of_result_declaration", "Date of Result Declaration", "ExamInfo")
        );
    }

    @Override
    public void verifyAuthorisation(StudentReportCardAttributeDetailsPayload payload) {
//        userPermissionManager.verifyAuthorisation(payload.getInstituteId(), null,
//                AuthorisationRequiredAction.GENERATE_EXAM_REPORT_CARD);
    }

    @Override
    public void payloadVerification(StudentReportCardAttributeDetailsPayload payload) {
        if (payload.getInstituteId() <= 0 || payload.getAcademicSessionId() <= 0 ||
            payload.getStandardId() == null || payload.getReportType() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }
    }

    @Override
    public StudentReportCardAttributeDetailsStore getStore(StudentReportCardAttributeDetailsPayload payload) {
        int instituteId = payload.getInstituteId();
        int academicSessionId = payload.getAcademicSessionId();
        UUID standardId = payload.getStandardId();
        Set<Integer> sectionIdSet = payload.getSectionIdSet();
        String reportType = payload.getReportType();

        // Get exam report card configuration
        ExamReportCardConfiguration examReportCardConfiguration = examReportCardManager
                .getExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);

        if (examReportCardConfiguration == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Exam report card configuration not found"));
        }

        // Fetch class exam report data
        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportType, true);

        if (CollectionUtils.isEmpty(classExamReportData)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam report data found"));
        }

        // Apply sorting if specified
        StudentSortingParameters sortingParameters = getSortingParametersFromPayload(payload);
        if (sortingParameters != null) {
            classExamReportData = studentReportSortingService.sortExamReportData(classExamReportData, sortingParameters);
        }

        // Get standard and class name
        Standard standard = instituteManager.getStandardByStandardId(instituteId, academicSessionId, standardId);
        String className = StandardUtils.getClassName(sectionIdSet, standard);

        // Get academic session display name
        String academicSessionDisplayName = instituteManager.getAcademicSession(instituteId, academicSessionId).getDisplayName();

        return new StudentReportCardAttributeDetailsStore(classExamReportData, className, academicSessionDisplayName);
    }

    @Override
    public String getDownloadReportName(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        return "Student_Report_Card_Attribute_Details_" + store.getClassName() + "_" +
               store.getAcademicSessionDisplayName();
    }

    @Override
    public ReportConfigs getConfigs(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        return new ReportConfigs(null, null, new PDFConfigs(PageSize.A4.rotate()),
                null); // Use default configs
    }

    @Override
    public ReportHeader getHeader(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        List<String> requiredHeaders = payload.getRequiredHeaders();
        List<ReportHeaderAttribute> reportHeaderAttributeList = convertToReportHeaderAttributes(requiredHeaders);
        List<ReportRow> headerRows = new ArrayList<>();
        List<ReportCellDetails> headerCells = new ArrayList<>();

        // Create header based on required headers or use all headers if none specified
        if (CollectionUtils.isEmpty(reportHeaderAttributeList)) {
            reportHeaderAttributeList = getStaticHeaders();
        }

        for (ReportHeaderAttribute headerKey : reportHeaderAttributeList) {
            String headerTitle = headerKey.getDisplayName();
            headerCells.add(createCell(headerTitle, true));
        }

        headerRows.add(new ReportRow(headerCells));
        return new ReportHeader(headerRows, null);
    }

    @Override
    public ReportBody getBody(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        List<String> requiredHeaders = payload.getRequiredHeaders();
        List<ReportHeaderAttribute> reportHeaderAttributeList = convertToReportHeaderAttributes(requiredHeaders);
        if (CollectionUtils.isEmpty(reportHeaderAttributeList)) {
            reportHeaderAttributeList = getStaticHeaders();
        }

        List<ReportRow> bodyRows = new ArrayList<>();
        List<ExamReportData> examReportDataList = store.getExamReportDataList();

        int serialNumber = 1;
        for (ExamReportData examReportData : examReportDataList) {
            List<ReportCellDetails> rowCells = new ArrayList<>();

            for (ReportHeaderAttribute headerKey : reportHeaderAttributeList) {
                String cellValue = getCellValue(headerKey, examReportData, serialNumber);
                rowCells.add(createCell(cellValue, false));
            }

            bodyRows.add(new ReportRow(rowCells));
            serialNumber++;
        }

        return new ReportBody(bodyRows, null);
    }

    private String getCellValue(ReportHeaderAttribute headerKey, ExamReportData examReportData, int serialNumber) {
        StudentLite studentLite = examReportData.getStudentLite();

        if (headerKey.equals(ReportHeaderAttribute.EXAM_SR_NO)) {
            return String.valueOf(serialNumber);
        } else if (headerKey.equals(ReportHeaderAttribute.ADMISSION_No)) {
            return studentLite.getAdmissionNumber() != null ? studentLite.getAdmissionNumber() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.ROLL_NO)) {
            return studentLite.getStudentSessionData() != null && studentLite.getStudentSessionData().getRollNumber() != null ?
                    studentLite.getStudentSessionData().getRollNumber() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.NAME)) {
            return studentLite.getName() != null ? studentLite.getName() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.CLASS)) {
            return studentLite.getStudentSessionData() != null && studentLite.getStudentSessionData().getStandardNameWithSection() != null ?
                    studentLite.getStudentSessionData().getStandardNameWithSection() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.FATHER_NAME)) {
            return studentLite.getFathersName() != null ? studentLite.getFathersName() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.STUDENT_MOTHER_NAME)) {
            return studentLite.getMothersName() != null ? studentLite.getMothersName() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.DOB)) {
            return studentLite.getDateOfBirth() != null && studentLite.getDateOfBirth() > 0 ? DateUtils.getFormattedDate(studentLite.getDateOfBirth()) : "";
        } else if (headerKey.equals(ReportHeaderAttribute.STUDENT_PRIMARY_CONTACT)) {
            return studentLite.getPrimaryContactNumber() != null ? studentLite.getPrimaryContactNumber() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.PERCENTAGE)) {
            Double percentage = examReportData.getPercentage();
            return percentage != null ? String.format("%.2f", percentage) : "";
        } else if (headerKey.equals(ReportHeaderAttribute.GRADE)) {
            return examReportData.getTotalGrade() != null ? examReportData.getTotalGrade().getGradeName() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.DIVISION)) {
            return ExamMarksUtils.getDivision(examReportData.getPercentage());
        } else if (headerKey.equals(ReportHeaderAttribute.RESULT)) {
            return examReportData.getExamResultStatus() != null ? examReportData.getExamResultStatus().name() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.RANK)) {
            return examReportData.getRank() != null ? String.valueOf(examReportData.getRank()) : "";
        } else if (headerKey.equals(ReportHeaderAttribute.ATTENDANCE)) {
            Integer attendedDays = examReportData.getTotalAttendedDays();
            Integer totalDays = examReportData.getTotalWorkingDays();
            if (attendedDays != null && totalDays != null) {
                return attendedDays + "/" + totalDays;
            }
            return "";
        } else if (headerKey.equals(ReportHeaderAttribute.STUDENT_SESSION_HEIGHT)) {
            return examReportData.getHeight() != null ? examReportData.getHeight() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.STUDENT_SESSION_WEIGHT)) {
            return examReportData.getWeight() != null ? examReportData.getWeight() : "";
        } else if (headerKey.equals(ReportHeaderAttribute.REMARKS)) {
            return examReportData.getRemarks() != null ? examReportData.getRemarks() : "";
        } else if (headerKey.equals("tc_remarks")) {
            return examReportData.getPrincipalRemarks() != null ? examReportData.getPrincipalRemarks() : "";
        } else if (headerKey.equals("date_of_result_declaration")) {
            return examReportData.getDateOfResultDeclaration() != null && examReportData.getDateOfResultDeclaration() > 0 ? DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()) : "";
        }
        return "";
    }

    /**
     * Extracts sorting parameters from the payload
     */
    private StudentSortingParameters getSortingParametersFromPayload(StudentReportCardAttributeDetailsPayload payload) {
        return payload.getStudentSortingParameters();
    }

    /**
     * Converts string header keys to ReportHeaderAttribute objects
     */
    private List<ReportHeaderAttribute> convertToReportHeaderAttributes(List<String> headerKeys) {
        if (CollectionUtils.isEmpty(headerKeys)) {
            return getStaticHeaders();
        }

        List<ReportHeaderAttribute> headerAttributes = new ArrayList<ReportHeaderAttribute>();
        List<ReportHeaderAttribute> staticHeaders = getStaticHeaders();

        for (String headerKey : headerKeys) {
            ReportHeaderAttribute matchingAttribute = findHeaderAttributeByKey(staticHeaders, headerKey);
            if (matchingAttribute != null) {
                headerAttributes.add(matchingAttribute);
            }
        }

        return headerAttributes;
    }

    /**
     * Finds a ReportHeaderAttribute by its key from the static headers list
     */
    private ReportHeaderAttribute findHeaderAttributeByKey(List<ReportHeaderAttribute> staticHeaders, String key) {
        for (ReportHeaderAttribute attribute : staticHeaders) {
            if (attribute.getKey().equals(key)) {
                return attribute;
            }
        }
        return null;
    }
}
