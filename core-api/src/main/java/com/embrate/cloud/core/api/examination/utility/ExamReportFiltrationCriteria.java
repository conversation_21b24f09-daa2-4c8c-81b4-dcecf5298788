package com.embrate.cloud.core.api.examination.utility;

import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.student.StudentSortingParameters;

import java.util.Set;
import java.util.UUID;

public class ExamReportFiltrationCriteria {

    private int academicSessionId;
    private UUID standardId;
    private Integer sectionId;
    private UUID examId;
    private UUID courseId;
    private CourseType courseType;
    private DownloadFormat downloadFormat;
    private String reportCardType;
    private UUID staffId;
    private Set<Integer> sectionIdSet;
    private Set<UUID> examIdSet;
    private Set<UUID> courseIdSet;
    private Set<UUID> compareCummulativeWithExamIdSet;
    private Integer rankTill;
    private boolean excludeCoScholasticSubjects;
    private String requiredHeaders;
    private boolean isSortStudentOnRank;
    private Set<UUID> additionalCoursesSet;
    private Set<MarksDisplayType> scholasticMarksDisplayTypeSet;
    private Set<MarksDisplayType> coScholasticMarksDisplayTypeSet;
    private boolean showClassAverageDetails;
    private boolean showStaffDetails;
    private StudentSortingParameters studentSortingParameters;

    public ExamReportFiltrationCriteria() {
    }

    public ExamReportFiltrationCriteria(int academicSessionId, UUID standardId, Integer sectionId, UUID examId, UUID courseId, CourseType courseType, DownloadFormat downloadFormat, String reportCardType, UUID staffId, Set<Integer> sectionIdSet, Set<UUID> examIdSet, Set<UUID> courseIdSet, Set<UUID> compareCummulativeWithExamIdSet, Integer rankTill, boolean excludeCoScholasticSubjects, String requiredHeaders, boolean isSortStudentOnRank, Set<UUID> additionalCoursesSet, Set<MarksDisplayType> scholasticMarksDisplayTypeSet, Set<MarksDisplayType> coScholasticMarksDisplayTypeSet, boolean showClassAverageDetails, boolean showStaffDetails, StudentSortingParameters studentSortingParameters) {
        this.academicSessionId = academicSessionId;
        this.standardId = standardId;
        this.sectionId = sectionId;
        this.examId = examId;
        this.courseId = courseId;
        this.courseType = courseType;
        this.downloadFormat = downloadFormat;
        this.reportCardType = reportCardType;
        this.staffId = staffId;
        this.sectionIdSet = sectionIdSet;
        this.examIdSet = examIdSet;
        this.courseIdSet = courseIdSet;
        this.compareCummulativeWithExamIdSet = compareCummulativeWithExamIdSet;
        this.rankTill = rankTill;
        this.excludeCoScholasticSubjects = excludeCoScholasticSubjects;
        this.requiredHeaders = requiredHeaders;
        this.isSortStudentOnRank = isSortStudentOnRank;
        this.additionalCoursesSet = additionalCoursesSet;
        this.scholasticMarksDisplayTypeSet = scholasticMarksDisplayTypeSet;
        this.coScholasticMarksDisplayTypeSet = coScholasticMarksDisplayTypeSet;
        this.showClassAverageDetails = showClassAverageDetails;
        this.showStaffDetails = showStaffDetails;
        this.studentSortingParameters = studentSortingParameters;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public void setAcademicSessionId(int academicSessionId) {
        this.academicSessionId = academicSessionId;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public void setStandardId(UUID standardId) {
        this.standardId = standardId;
    }

    public Integer getSectionId() {
        return sectionId;
    }

    public void setSectionId(Integer sectionId) {
        this.sectionId = sectionId;
    }

    public UUID getExamId() {
        return examId;
    }

    public void setExamId(UUID examId) {
        this.examId = examId;
    }

    public UUID getCourseId() {
        return courseId;
    }

    public void setCourseId(UUID courseId) {
        this.courseId = courseId;
    }

    public CourseType getCourseType() {
        return courseType;
    }

    public void setCourseType(CourseType courseType) {
        this.courseType = courseType;
    }

    public DownloadFormat getDownloadFormat() {
        return downloadFormat;
    }

    public void setDownloadFormat(DownloadFormat downloadFormat) {
        this.downloadFormat = downloadFormat;
    }

    public String getReportCardType() {
        return reportCardType;
    }

    public void setReportCardType(String reportCardType) {
        this.reportCardType = reportCardType;
    }

    public UUID getStaffId() {
        return staffId;
    }

    public void setStaffId(UUID staffId) {
        this.staffId = staffId;
    }

    public Set<Integer> getSectionIdSet() {
        return sectionIdSet;
    }

    public void setSectionIdSet(Set<Integer> sectionIdSet) {
        this.sectionIdSet = sectionIdSet;
    }

    public Set<UUID> getExamIdSet() {
        return examIdSet;
    }

    public void setExamIdSet(Set<UUID> examIdSet) {
        this.examIdSet = examIdSet;
    }

    public Set<UUID> getCourseIdSet() {
        return courseIdSet;
    }

    public void setCourseIdSet(Set<UUID> courseIdSet) {
        this.courseIdSet = courseIdSet;
    }

    public Set<UUID> getCompareCummulativeWithExamIdSet() {
        return compareCummulativeWithExamIdSet;
    }

    public void setCompareCummulativeWithExamIdSet(Set<UUID> compareCummulativeWithExamIdSet) {
        this.compareCummulativeWithExamIdSet = compareCummulativeWithExamIdSet;
    }

    public Integer getRankTill() {
        return rankTill;
    }

    public void setRankTill(Integer rankTill) {
        this.rankTill = rankTill;
    }

    public boolean isExcludeCoScholasticSubjects() {
        return excludeCoScholasticSubjects;
    }

    public void setExcludeCoScholasticSubjects(boolean excludeCoScholasticSubjects) {
        this.excludeCoScholasticSubjects = excludeCoScholasticSubjects;
    }

    public String getRequiredHeaders() {
        return requiredHeaders;
    }

    public void setRequiredHeaders(String requiredHeaders) {
        this.requiredHeaders = requiredHeaders;
    }

    public boolean isSortStudentOnRank() {
        return isSortStudentOnRank;
    }

    public void setSortStudentOnRank(boolean sortStudentOnRank) {
        isSortStudentOnRank = sortStudentOnRank;
    }

    public Set<UUID> getAdditionalCoursesSet() {
        return additionalCoursesSet;
    }

    public void setAdditionalCoursesSet(Set<UUID> additionalCoursesSet) {
        this.additionalCoursesSet = additionalCoursesSet;
    }

    public Set<MarksDisplayType> getScholasticMarksDisplayTypeSet() {
        return scholasticMarksDisplayTypeSet;
    }

    public void setScholasticMarksDisplayTypeSet(Set<MarksDisplayType> scholasticMarksDisplayTypeSet) {
        this.scholasticMarksDisplayTypeSet = scholasticMarksDisplayTypeSet;
    }

    public Set<MarksDisplayType> getCoScholasticMarksDisplayTypeSet() {
        return coScholasticMarksDisplayTypeSet;
    }

    public void setCoScholasticMarksDisplayTypeSet(Set<MarksDisplayType> coScholasticMarksDisplayTypeSet) {
        this.coScholasticMarksDisplayTypeSet = coScholasticMarksDisplayTypeSet;
    }

    public boolean isShowClassAverageDetails() {
        return showClassAverageDetails;
    }

    public void setShowClassAverageDetails(boolean showClassAverageDetails) {
        this.showClassAverageDetails = showClassAverageDetails;
    }

    public boolean isShowStaffDetails() {
        return showStaffDetails;
    }

    public void setShowStaffDetails(boolean showStaffDetails) {
        this.showStaffDetails = showStaffDetails;
    }

    public StudentSortingParameters getStudentSortingParameters() {
        return studentSortingParameters;
    }

    public void setStudentSortingParameters(StudentSortingParameters studentSortingParameters) {
        this.studentSortingParameters = studentSortingParameters;
    }

    @Override
    public String toString() {
        return "ExamReportFiltrationCriteria{" +
                "academicSessionId=" + academicSessionId +
                ", standardId=" + standardId +
                ", sectionId=" + sectionId +
                ", examId=" + examId +
                ", courseId=" + courseId +
                ", courseType=" + courseType +
                ", downloadFormat=" + downloadFormat +
                ", reportCardType='" + reportCardType + '\'' +
                ", staffId=" + staffId +
                ", sectionIdSet=" + sectionIdSet +
                ", examIdSet=" + examIdSet +
                ", courseIdSet=" + courseIdSet +
                ", compareCummulativeWithExamIdSet=" + compareCummulativeWithExamIdSet +
                ", rankTill=" + rankTill +
                ", excludeCoScholasticSubjects=" + excludeCoScholasticSubjects +
                ", requiredHeaders='" + requiredHeaders + '\'' +
                ", isSortStudentOnRank=" + isSortStudentOnRank +
                ", additionalCoursesSet=" + additionalCoursesSet +
                ", scholasticMarksDisplayTypeSet=" + scholasticMarksDisplayTypeSet +
                ", coScholasticMarksDisplayTypeSet=" + coScholasticMarksDisplayTypeSet +
                ", showClassAverageDetails=" + showClassAverageDetails +
                ", showStaffDetails=" + showStaffDetails +
                ", studentSortingParameters=" + studentSortingParameters +
                '}';
    }
}
