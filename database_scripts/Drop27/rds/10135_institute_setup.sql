use lernen

INSERT INTO institute (institute_id, institute_name, address_line_1, address_line_2, city, state, country, zipcode, landmark, logo_url, email, phone_number, letter_head_line1, letter_head_line2, institute_unique_code)
VALUES (10135,'LALA CENTRAL SCHOOL', 'NEAR BY-PASS, DHANIPUR ROAD', 'LALA, HAILAKANDI', 'LALA', 'ASSAM', 'India', '788163', '', '/static/core/images/10135_logo.png', '<EMAIL>', '9401957939', 'NEAR BY-PASS, DHANIPUR ROAD', 'LALA, HAILAKANDI, ASSAM - 788163', UUID());

INSERT INTO academic_session(institute_id, start_year, end_year, start_month, end_month)
values(10135, 2022, 2023, 4, 3);

insert into fee_category (institute_id, fee_category, description) values (10135, 'Academic Fee' ,'Academic Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Hostel Fee' ,'Hostel Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Transport Fee' ,'Transport Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Admission Fee' ,'Admission Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Examination Fee' ,'Examination Related Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Miscellaneous Fee' ,'Miscellaneous Fees');
insert into fee_category (institute_id, fee_category, description) values (10135, 'Other Fee' ,'Any Other Fees');


INSERT INTO fee_head_configuration(institute_id, fee_category_id, fee_head, refundable, description, fee_head_type, fee_head_tag)
VALUES(10135, (SELECT fee_category_id from fee_category where institute_id = 10135 and fee_category = 'Transport Fee'), 'Transport', 1, null, 'SYSTEM', 'TRANSPORT');


insert into categories (institute_id,category_name,genders) values (10135,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10135,'books',0);
insert into categories (institute_id,category_name) values (10135,'clothing');
insert into categories (institute_id,category_name) values (10135,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10135,'note book',0,0);
insert into categories (institute_id,category_name) values (10135,'art & craft');
insert into categories (institute_id,category_name) values (10135,'personal care');
insert into categories (institute_id,category_name) values (10135,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10135,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10135,'accessories');
insert into categories (institute_id,category_name) values (10135,'furniture');
insert into categories (institute_id,category_name) values (10135,'electronics');
insert into categories (institute_id,category_name) values (10135,'sports');


insert into colors (institute_id, color_name) values (10135,'maroon');
insert into colors (institute_id, color_name) values (10135,'black');
insert into colors (institute_id, color_name) values (10135,'brown');
insert into colors (institute_id, color_name) values (10135,'white');
insert into colors (institute_id, color_name) values (10135,'red');
insert into colors (institute_id, color_name) values (10135,'yellow');
insert into colors (institute_id, color_name) values (10135,'blue');
insert into colors (institute_id, color_name) values (10135,'navy blue');
insert into colors (institute_id, color_name) values (10135,'green');
insert into colors (institute_id, color_name) values (10135,'dark green');
insert into colors (institute_id, color_name) values (10135,'pink');
insert into colors (institute_id, color_name) values (10135,'purple');
insert into colors (institute_id, color_name) values (10135,'grey');
insert into colors (institute_id, color_name) values (10135,'olive');
insert into colors (institute_id, color_name) values (10135,'cyan');
insert into colors (institute_id, color_name) values (10135,'magenta');


insert into counters (institute_id, counter_type, count, counter_prefix) values (10135, 'FEE_INVOICE', 1, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10135, 'AUDIO_VOICE_CALL_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values (10135, 'SMS_COUNTER', 0, "");
insert into counters (institute_id, counter_type, count, counter_prefix) values(10135, 'REGISTRATION_NUMBER', 1, 'REG-');
insert into counters (institute_id, counter_type, count, counter_prefix) values(10135, 'ADMISSION_NUMBER', 1, 'LCS-');
insert into counters (institute_id, counter_type, count, counter_prefix) values(10135, 'STAFF_NUMBER', 1, 'LC-');

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'enable_permission', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'exam_admit_card_preferences', 'include_class_roll_number', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'institute_name_in_sms', 'LALA CENTRAL SCHOOL');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'institute_unique_code', 'lcs135');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'sms_service_enabled', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'sms_preferences', 'buffer_sms_count', 0);
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'inventory_preferences', 'inventory_institutes_scope', '[10135]');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'registration_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'admission_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'staff_counter', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'meta_data', 'module_access', '["ADMISSION","FEES","TRANSPORT","STORE","COURSES","EXAMINATION","ATTENDANCE","LECTURE_MANAGEMENT","HOMEWORK_MANAGEMENT","NOTICE_BOARD_MANAGEMENT","STUDENT_MANAGEMENT","STAFF_MANAGEMENT","INCOME_EXPENSE","USER_MANAGEMENT","AUDIT_LOGS","COMMUNICATION", "SALARY_MANAGEMENT","STAFF_ATTENDANCE"]');


insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10135, "Total", "SYSTEM", "NUMBER", 1);
insert into exam_dimensions(institute_id,dimension_name,dimension_type,evaluation_type,is_total) value(10135, "Grade", "SYSTEM", "GRADE", 1);

--insert into default_fee_assignment_structure_meta_data values(10135, "Installments", "1aed4d2d-2d8e-4741-8b4c-6454126c1701", "ENROLLMENT");
--insert into default_fee_assignment_structure select "1aed4d2d-2d8e-4741-8b4c-6454126c1701", (select standard_id from standards where institute_id=10135 and standard_name = '12th' and stream = 'COMMERCE'), "CLASS", fee_id , 80, 6900 from fee_configuration where institute_id = 10105 and fee_type = "REGULAR" and academic_session_id = 63;

insert into standards_metadata select 10135, 63, standard_id, 1, 0 from standards where institute_id = 10135;


--0.905 - 1.1 : A+
--0.805 - 0.905 : A
--0.705 - 0.805 : B+
--0.605 - 0.705 : B
--0.505 - 0.605 : C+
--0.405 - 0.505 : C
--0.325 - 40 : D
--0.0 - 0.325 : F

----add range display name also
SET @institute_id := 10135;
SET @academic_session_id := 63;
SET @course_type := "SCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.805, 0.905, '81-90' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 7, 0.705, 0.805, '71-80' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 6, 0.605, 0.705, '61-70' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 5, 0.505 , 0.605, '51-60' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 4, 0.405 , 0.505, '41-50' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'F', 2, 0.0 , 0.325, '00-32' from standards where institute_id = @institute_id;

-- Setting CO-SCHOLASTIC Grades
SET @course_type := "COSCHOLASTIC";

insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'A', 8, 0.805, 0.905, '81-90' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B+', 7, 0.705, 0.805, '71-80' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'B', 6, 0.605, 0.705, '61-70' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C+', 5, 0.505 , 0.605, '51-60' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'C', 4, 0.405 , 0.505, '41-50' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40' from standards where institute_id = @institute_id;
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
select @institute_id, @academic_session_id, standard_id, @course_type, 'F', 2, 0.0 , 0.325, '00-32' from standards where institute_id = @institute_id;


--select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10135 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 63;
--insert into default_fee_assignment_structure select "348bfd28-7193-4cf1-ac4b-18ebb0ce1b4d", (select standard_id from standards where institute_id=10135 and standard_name = 'Standard-VI'), "CLASS", fee_id , 150, 1400 from fee_configuration where institute_id = 10135 and fee_type = "REGULAR" and academic_session_id = 63;

insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'web_ui_preferences', 'background_image_enable', 'true');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'web_ui_preferences', 'background_image_url', '/static/core/images/10135_bg_logo.png');
insert into configuration (entity, entity_id, config_type, config_key, config_value) values ('INSTITUTE', '10135', 'web_ui_preferences', 'school_name_logo_position', 'CENTER');




SET @academic_session_id := 169;
SET @course_type := "SCHOLASTIC";
SET @standard_id := '74b90748-add3-11ed-bbe2-0aa7a9710bf1';
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'A', 8, 0.805, 0.905, '81-90');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'B+', 7, 0.705, 0.805, '71-80');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'B', 6, 0.605, 0.705, '61-70');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'C+', 5, 0.505 , 0.605, '51-60');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'C', 4, 0.405 , 0.505, '41-50');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'F', 2, 0.0 , 0.325, '00-32');

SET @course_type := "COSCHOLASTIC";
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'A+', 9, 0.905, 1.1, '91-100');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'A', 8, 0.805, 0.905, '81-90');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'B+', 7, 0.705, 0.805, '71-80');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'B', 6, 0.605, 0.705, '61-70');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'C+', 5, 0.505 , 0.605, '51-60');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'C', 4, 0.405 , 0.505, '41-50');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'D', 3, 0.325 , 0.405, '33-40');
insert into examination_grades (institute_id, academic_session_id, standard_id, course_type, grade_name, grade_value, marks_range_start, marks_range_end, range_display_name)
values(10135, @academic_session_id, @standard_id, @course_type, 'F', 2, 0.0 , 0.325, '00-32');
