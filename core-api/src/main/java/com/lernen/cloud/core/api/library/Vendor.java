package com.lernen.cloud.core.api.library;

import java.util.UUID;

public class Vendor {

    private UUID vendorId;
    private String vendorName;
    private String address;
    private String phoneNumber;
    private String email;
    private String gstNumber;
    private String accountType;
    private String bankName;
    private String accountHolderName;
    private String accountNumber;
    private String ifscCode;
    
    public Vendor(){
        
    }

    public Vendor(UUID vendorId, String vendorName, String address, String phoneNumber, String email,
            String gstNumber, String accountType, String bankName, String accountHolderName, String accountNumber,
            String ifscCode) {
        this.vendorId = vendorId;
        this.vendorName = vendorName;
        this.address = address;
        this.phoneNumber = phoneNumber;
        this.email = email;
        this.gstNumber = gstNumber;
        this.accountType = accountType;
        this.bankName = bankName;
        this.accountHolderName = accountHolderName;
        this.accountNumber = accountNumber;
        this.ifscCode = ifscCode;
    }

    public UUID getVendorId() {
        return vendorId;
    }

    public void setVendorId(UUID vendorId) {
        this.vendorId = vendorId;
    }

    public String getVendorName() {
        return vendorName;
    }

    public void setVendorName(String vendorName) {
        this.vendorName = vendorName;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGstNumber() {
        return gstNumber;
    }

    public void setGstNumber(String gstNumber) {
        this.gstNumber = gstNumber;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getIfscCode() {
        return ifscCode;
    }

    public void setIfscCode(String ifscCode) {
        this.ifscCode = ifscCode;
    }

    @Override
    public String toString() {
        return "Vendor [vendorId=" + vendorId + ", vendorName=" + vendorName
                + ", address=" + address + ", phoneNumber=" + phoneNumber + ", email=" + email + ", gstNumber="
                + gstNumber + ", accountType=" + accountType + ", bankName=" + bankName + ", accountHolderName="
                + accountHolderName + ", accountNumber=" + accountNumber + ", ifscCode=" + ifscCode + "]";
    }

    
}
