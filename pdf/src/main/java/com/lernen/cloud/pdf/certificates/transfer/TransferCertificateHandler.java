/**
 * 
 */
package com.lernen.cloud.pdf.certificates.transfer;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.student.RelievedMetadataVariables;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.student.StudentSorter;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class TransferCertificateHandler {
	
	private static final Logger logger = LogManager.getLogger(TransferCertificateHandler.class);

	private final TransferCertificateGeneratorFactory transferCertificateGeneratorFactory;
	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final UserPreferenceSettings userPreferenceSettings;

	public TransferCertificateHandler(InstituteManager instituteManager, StudentManager studentManager,
									  UserPreferenceSettings userPreferenceSettings, AssetProvider assetProvider) {
		this.transferCertificateGeneratorFactory = new TransferCertificateGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	public DocumentOutput generateTransferCertificate(int instituteId, UUID studentId) {		
		
		if (instituteId <= 0 || studentId == null) {
			logger.error("Invalid institute id or student id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or student id"));
		}
		
		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}
		
		List<Student> studentInAllSessions = studentManager.getStudentInAllSessions(instituteId, studentId);
		if (CollectionUtils.isEmpty(studentInAllSessions)) {
			logger.error("No student found in institute {} with student id {}", instituteId, studentId);
			return null;
		}
		Collections.sort(studentInAllSessions, new Comparator<Student>() {
			@Override
			public int compare(Student s1, Student s2) {
				return s1.getStudentAcademicSessionInfoResponse().getAcademicSession().getSessionStartTime() -
						s2.getStudentAcademicSessionInfoResponse().getAcademicSession().getSessionStartTime();
			}
		});
		Student student = studentInAllSessions.get(studentInAllSessions.size() - 1);
		Integer academicSessionId = student.getStudentBasicInfo().getRelievedMetadata() == null ? null :
				student.getStudentBasicInfo().getRelievedMetadata().get(RelievedMetadataVariables.LAST_ACTIVE_SESSION) == null ?
				null : Integer.parseInt(student.getStudentBasicInfo().getRelievedMetadata().get(RelievedMetadataVariables.LAST_ACTIVE_SESSION));
		if(academicSessionId != null) {
			for(Student studentEntry : studentInAllSessions) {
				if(studentEntry.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId() == academicSessionId) {
					student = studentEntry;
					break;
				}
			}
		}
		logger.info("Generating tc certificate form for instituteId {}, student id {} ", instituteId, studentId);

		MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		TransferCertificateGenerator transferCertificateGenerator = transferCertificateGeneratorFactory.getFeeTCGenerator(
				instituteId, metaDataPreferences.isDetailedTCEnabled());

		String fileName = student.getStudentBasicInfo().getAdmissionNumber() + "_Transfer_Certificate.pdf";
		
		return transferCertificateGenerator.generateTransferCertificate(studentManager, institute, student, fileName);
	}

	public DocumentOutput generateBulkTransferCertificate(int instituteId, int academicSessionId, List<UUID> studentIds){
		if (instituteId <= 0 || academicSessionId <= 0 || CollectionUtils.isEmpty(studentIds)) {
			logger.error("Invalid institute id or academic session id or student ids");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid institute id or academic session id or student ids"));
		}
		final Institute institute = instituteManager.getInstitute(instituteId);
		if (institute == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Unable to get institute"));
		}
		MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		if(!metaDataPreferences.isDetailedTCEnabled()){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Your Institute Don't Have Access To Generate Bulk Tc"));
		}
		Set<StudentStatus> studentStatusSet = new HashSet<>();
		studentStatusSet.add(StudentStatus.ENROLLED);
		studentStatusSet.add(StudentStatus.RELIEVED);
		List<Student> studentList = studentManager.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds, studentStatusSet);
		if(CollectionUtils.isEmpty(studentList)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Student Details Not Found For The Selected Session"));
		}
		StudentSorter.sortStudents(studentList, 
			StudentSortingParameters.ADMISSION_NUMBER, 
			new StudentSorter.StudentKeyExtractor<Student>() {

				@Override
				public String getAdmissionNumber(Student student) {
					return student.getStudentBasicInfo().getAadharNumber();
				}

				@Override
				public String getName(Student student) {
					return student.getStudentBasicInfo().getName();
				}

				@Override
				public String getRollNumber(Student student) {
					return student.getStudentAcademicSessionInfoResponse().getRollNumber();
				}

				@Override
				public String getSectionName(Student student) {
					List<StandardSections> sections = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
                    return (sections != null && !sections.isEmpty()) ? sections.get(0).getSectionName() : null;
				}

				@Override
				public Integer getAdmissionDate(Student student) {
					return student.getStudentBasicInfo().getAdmissionDate();
				}
			}
		);
		logger.info("Generating tc certificate form for instituteId {}, student ids {} ", instituteId, studentIds);

		TransferCertificateGenerator transferCertificateGenerator = transferCertificateGeneratorFactory.getFeeTCGenerator(
				instituteId, metaDataPreferences.isDetailedTCEnabled());

		return transferCertificateGenerator.generateBulkTransferCertificate(studentManager, institute, studentList, "Bulk_Transfer_Certificate.pdf");
	}

}
