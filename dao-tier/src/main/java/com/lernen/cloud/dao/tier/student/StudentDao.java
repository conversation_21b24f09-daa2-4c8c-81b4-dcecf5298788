package com.lernen.cloud.dao.tier.student;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserData;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserIdentityData;
import com.embrate.cloud.core.api.student.cache.InstituteSessionCacheKey;
import com.embrate.cloud.core.api.student.management.StudentManagementField;
import com.embrate.cloud.core.api.student.management.StudentManagementUpdateFieldPayload;
import com.embrate.cloud.core.api.student.management.StudentManagementUpdateFieldStudentData;
import com.embrate.cloud.core.api.student.management.StudentManagementUpdateFieldValue;
import com.embrate.cloud.core.utils.cache.CacheGZIPSerializer;
import com.embrate.cloud.dao.tier.wallet.UserWalletDao;
import com.google.gson.Gson;
import com.lernen.cloud.core.api.common.*;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.exception.*;
import com.lernen.cloud.core.api.institute.CounterData;
import com.lernen.cloud.core.api.institute.InstituteHouse;
import com.lernen.cloud.core.api.institute.StandardSectionGenderCount;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.student.lite.StudentLiteRowV2;
import com.lernen.cloud.core.api.student.lite.StudentSearchCacheData;
import com.lernen.cloud.core.api.student.lite.StudentSearchEntry;
import com.lernen.cloud.core.api.user.*;
import com.lernen.cloud.core.api.user.biometric.FaceData;
import com.lernen.cloud.core.api.user.biometric.FingerprintData;
import com.lernen.cloud.core.api.user.biometric.RFIDCardData;
import com.lernen.cloud.core.api.user.biometric.UserBiometricIdentificationData;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.SharedConstants;
import com.lernen.cloud.core.utils.UserNameUtils;
import com.lernen.cloud.core.utils.cache.CacheFactory;
import com.lernen.cloud.core.utils.cache.ICache;
import com.lernen.cloud.core.utils.cache.ICacheLoader;
import com.lernen.cloud.core.utils.crypto.CryptoUtils;
import com.lernen.cloud.core.utils.exceptions.ExceptionHandling;
import com.lernen.cloud.dao.tier.institute.InstituteDao;
import com.lernen.cloud.dao.tier.institute.mappers.StandardSectionGenderCountRowMapper;
import com.lernen.cloud.dao.tier.student.mappers.*;
import com.lernen.cloud.dao.tier.student.mappers.lite.StudentLiteV2RowMapper;
import com.lernen.cloud.dao.tier.user.mappers.UserRowMapper;
import org.apache.commons.lang.WordUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.lernen.cloud.core.utils.StringHelper.*;
import static com.lernen.cloud.core.utils.biometric.BiometricUtils.*;

public class StudentDao {

	private static final Logger logger = LogManager.getLogger(StudentDao.class);

	private final JdbcTemplate jdbcTemplate;
	private final TransactionTemplate transactionTemplate;
	private final InstituteDao instituteDao;
	private final UserWalletDao userWalletDao;

	private static final Gson GSON = SharedConstants.GSON;

	private static final StudentRowMapper STUDENT_ROW_MAPPER = new StudentRowMapper();

	private static final StudentLiteV2RowMapper STUDENT_LITE_V2_ROW_MAPPER = new StudentLiteV2RowMapper();



	private static final UserBiometricIdentificationRowMapper USER_BIOMETRIC_IDENTIFICATION_ROW_MAPPER = new UserBiometricIdentificationRowMapper();
	private static final StudentWithoutSessionRowMapper STUDENT_WITHOUT_SESSION_ROW_MAPPER = new StudentWithoutSessionRowMapper();
	private static final StudentCastReportRowMapper STUDENT_CAST_REPORT_ROW_MAPPER = new StudentCastReportRowMapper();
	private static final StudentGenderReportRowMapper STUDENT_GENDER_REPORT_ROW_MAPPER = new StudentGenderReportRowMapper();
	private static final StudentReligionReportRowMapper STUDENT_RELIGION_REPORT_ROW_MAPPER = new StudentReligionReportRowMapper();
	private static final StudentHouseSummaryReportRowMapper STUDENT_HOUSE_SUMMARY_REPORT_ROW_MAPPER = new StudentHouseSummaryReportRowMapper();
	private static final StudentWithWalletDetailsRowMapper STUDENT_WITH_WALLET_DETAILS_ROW_MAPPER = new StudentWithWalletDetailsRowMapper();
	private static final StudentSiblingWithoutSessionRowMapper STUDENT_SIBLING_WITHOUT_SESSION_ROW_MAPPER = new StudentSiblingWithoutSessionRowMapper();
	private static final StudentBasicInfoRowMapper STUDENT_BASIC_INFO_ROW_MAPPER = new StudentBasicInfoRowMapper();
	private static final StandardSectionGenderCountRowMapper STANDARD_SECTION_GENDER_COUNT_ROW_MAPPER = new StandardSectionGenderCountRowMapper();
	private static final StudentSiblingRowMapper STUDENT_SIBLING_ROW_MAPPER = new StudentSiblingRowMapper();
	private static final UserRowMapper USER_ROW_MAPPER = new UserRowMapper();

	private static final String TEMP_REGISTRATION_SUFFIX = "-temp";
	private static final String STUDENT = "Student";
	// TODO : Is RTE session based field
	private static final String ADD_STUDENT_DETAILS = "insert into students(institute_id, registration_request_number, registration_number, online_registration_number, admission_number, pen_number, "
			+ " student_id, admission_date, online_registration_date, registration_date, name, date_of_birth, birth_place, gender, category, religion, rte, aadhar_number, mother_tongue, "
			+ " area_type, specially_abled, bpl, permanent_address, permanent_city, permanent_state, permanent_zipcode, permanent_country, "
			+ " present_address, present_city, present_state, present_zipcode, present_country, nationality, primary_contact_number, primary_email, father_name, mother_name, "
			+ " father_qualification, mother_qualification, father_occupation, father_annual_income, mother_occupation, mother_annual_income, father_contact_number, mother_contact_number, father_aadhar_number, mother_aadhar_number, father_pan_card_details, mother_pan_card_details, "
			+ " family_approx_income, gardians_details, previous_school_name, class_passed, previous_school_medium, result, percentage, year_of_passing,"
			+ " blood_group, blood_pressure, pulse, height, weight, date_of_physical_examination, admission_academic_session, caste, whatsapp_number, is_sponsored, "
			+ " permanent_post_office, permanent_police_station, present_post_office, present_police_station, is_admission_tc_based, previous_school_tc_number, "
			+ " house_id, admission_class, specially_abled_type, student_name_as_per_aadhar, child_category_criteria, is_hosteller, apaar_id)"
			+ " values(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
			+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, "
			+ " ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";


	private static final String ADD_ACADEMIC_SESSION_STUDENT_DETAILS = "insert into student_academic_session_details(academic_session_id, student_id, standard_id, section_id, roll_number, medium, session_status, is_new_admission, hostel_id)"
			+ "values(?, ?, ?, ?, ?, ?, ?, ?, ?)";

	private static final String GET_STUDENT_SIBLING_REPORT = "select students.*,standards.*,standard_section_mapping.*,student_academic_session_details.*,academic_session.*"
															+" from students inner join student_academic_session_details  on students.student_id = student_academic_session_details.student_id"
															+" left join academic_session  on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
															+" join standards  on student_academic_session_details.standard_id = standards.standard_id"
															+" left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id"
															+" and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id"
															+" and student_academic_session_details.section_id = standard_section_mapping.section_id   where students.institute_id = ?"
															+" and student_academic_session_details.academic_session_id = ?";

	private static final String CREATE_STUDENT_WALLET = "insert into user_wallet(institute_id, user_id, user_type) values(?, ?, ?)";

	private static final String GET_STUDENT_BY_ACADEMIC_SESSION = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where"
			+ " students.institute_id = ? and students.student_id = ? and student_academic_session_details.academic_session_id = ?";

	private static final String GET_STUDENT_REPEATERS = "select students.*, academic_session.*, standards.*,standard_section_mapping.*, student_academic_session_details.* from students " +
			" join student_academic_session_details on student_academic_session_details.student_id = students.student_id " +
			" join standards on student_academic_session_details.standard_id = standards.standard_id " +
			" join academic_session on academic_session.academic_session_id = ? " +
			" left join standard_section_mapping on standards.standard_id = standard_section_mapping.standard_id and standard_section_mapping.section_id = student_academic_session_details.section_id and standard_section_mapping.academic_session_id = ? " +
			" where students.institute_id = ? and student_academic_session_details.session_status = 'ENROLLED' " +
			" and student_academic_session_details.student_id in " +
			" (select student_id from student_academic_session_details where student_id in " +
			" (select student_id from student_academic_session_details where academic_session_id in (?, ?) " +
			" group by student_id, standard_id having count(*) > 1) and academic_session_id = ?) " +
			" group by students.student_id, standards.standard_id having count(*)>1";

    private static final String GET_RELIGION_BY_INSTITUTE = "select distinct students.religion from students where students.religion is "
			+ " not NULL and students.religion <> \"\" and students.institute_id = ?";

	private static final String GET_STUDENT_IN_ACADEMIC_SESSIONS = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where"
			+ " students.institute_id = ? and students.student_id = ?  %s";
	
	private static final String GET_STUDENT_BASIC_INFO = "select students.* from students where"
			+ " students.institute_id = ? ";

	private static final String GET_STUDENT_BY_LATEST_ACADEMIC_SESSION = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.*, hostel_management.* "
			+ " from students left join (select student_academic_session_details.* from "
			+ " student_academic_session_details join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id"
			+ " where student_academic_session_details.student_id = ? order by academic_session.end_year desc limit 1) as student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " left join hostel_management on student_academic_session_details.hostel_id = hostel_management.hostel_id where "
			+ " students.student_id = ? and student_academic_session_details.session_status in %s ";

	private static final String GET_STUDENTS_BY_ACADEMIC_SESSION = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.*, hostel_management.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " left join hostel_management on student_academic_session_details.hostel_id = hostel_management.hostel_id where "
			+ " students.institute_id = ? and student_academic_session_details.academic_session_id = ? and students.student_id in %s and "
			+ " student_academic_session_details.session_status = ? order by standards.level, students.name";

	private static final String GET_STUDENTS_BY_ACADEMIC_SESSION_AND_STATUS = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where"
			+ " students.institute_id = ? and student_academic_session_details.academic_session_id = ? and students.student_id in %s and "
			+ " student_academic_session_details.session_status in %s order by standards.level, students.name";

	private static final String GET_STUDENT_BY_ACADEMIC_SESSION_ADMISSION_NUMBER = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id left join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id where"
			+ " students.institute_id = ? and student_academic_session_details.academic_session_id = ? %s ";

	private static final String GET_STUDENT_BY_DEVICE_USER_ID =  "select students.* from students where"
			+ " device_user_id = ? %s";

	private static final String UPDATE_STUDENT_DEVICE_USER_ID =  "update students set device_user_id = ? where " +
			" institute_id = ? and student_id = ?";
	private static final String UPDATE_STUDENT = "update students set ";
	private static final String BASIC_INFO_QUERY = " %s %s pen_number = ?, admission_date = ?, registration_date = ?, name = ?, date_of_birth = ?, "
			+ " birth_place = ?, gender = ?, category = ?, religion = ?, rte = ?, aadhar_number = ?, mother_tongue = ?, area_type = ?, "
			+ " specially_abled = ?, bpl = ?, permanent_address = ?, permanent_city = ?, permanent_state = ?, permanent_post_office = ?, permanent_police_station = ?, "
			+ " permanent_zipcode = ?, permanent_country = ?, present_address = ?, present_city = ?, present_state = ?, present_post_office = ?, present_police_station = ?, "
			+ " present_zipcode = ?, present_country = ?, nationality = ?, primary_contact_number = ?, primary_email = ?, caste = ?, whatsapp_number = ?, is_sponsored = ?, "
			+ " house_id = ?, admission_class = ?, specially_abled_type = ?, student_name_as_per_aadhar = ?, child_category_criteria = ?, is_hosteller = ?, apaar_id = ?";
	private static final String FAMILY_INFO_QUERY = " father_name = ?, mother_name = ?, father_qualification = ?, mother_qualification = ?, father_occupation = ?, father_annual_income = ?, mother_occupation = ?, mother_annual_income = ?, father_contact_number = ?, mother_contact_number = ?, father_aadhar_number = ?, mother_aadhar_number = ?, father_pan_card_details = ?, mother_pan_card_details = ?, family_approx_income = ? ";
	private static final String PREVIOUS_SCHOOL_INFO_QUERY = " previous_school_name = ?, class_passed = ?, previous_school_medium = ?, result = ?, percentage = ?, year_of_passing = ?, is_admission_tc_based = ?, previous_school_tc_number = ? ";
	private static final String MEDICAL_INFO_QUERY = " blood_group = ?, blood_pressure = ?, pulse = ?, height = ?, weight = ?, date_of_physical_examination = ? ";

	private static final String SEARCH_STUDENTS_IN_ACADEMIC_SESSION = "select students.*, academic_session.*, standards.*,"
			+ " standard_section_mapping.*, student_academic_session_details.*, institute_houses.name, hostel_management.* "
			+ " from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id left join institute_houses on students.house_id = institute_houses.house_id left join hostel_management on student_academic_session_details.hostel_id = hostel_management.hostel_id ";

	private static final String COUNT_RESULT_SEARCH_STUDENTS_IN_ACADEMIC_SESSION = "select count(*) from students left join student_academic_session_details on"
			+ " students.student_id = student_academic_session_details.student_id left join academic_session on"
			+ " student_academic_session_details.academic_session_id = academic_session.academic_session_id join standards on"
			+ " student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on"
			+ " standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ " student_academic_session_details.academic_session_id and "
			+ " student_academic_session_details.section_id = standard_section_mapping.section_id ";

	private static final String STUDENT_SEARCH_CONDITION = " concat_ws('/', students.name, students.admission_number, students.registration_number, students.father_name, standards.standard_name) like ? ";

	private static final String STUDENT_SEARCH_CONDITION_WITHOUT_SESSION = " concat_ws('/', students.name, students.admission_number, students.registration_number, students.father_name) like ? ";

////////////	V2 Lite student search
	private static final String LITE_SEARCH_STUDENTS_IN_ACADEMIC_SESSION = "select students.student_id, students.name," +
		" students.admission_number, students.father_name, standards.standard_name, standards.level, standards.stream, " +
		" standard_section_mapping.section_name from students join " +
		" student_academic_session_details on student_academic_session_details.academic_session_id = ? and students.student_id = student_academic_session_details.student_id " +
		" join standards on student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on " +
		" standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = " +
		" student_academic_session_details.academic_session_id and " +
		" student_academic_session_details.section_id = standard_section_mapping.section_id where students.institute_id = ? " +
		" and student_academic_session_details.session_status in %s and concat_ws('/', students.name, students.admission_number, students.registration_number, students.father_name, standards.standard_name) like ?";

	private static final String ALL_STUDENT_LITE_SEARCH_DATA_IN_SESSION = "select students.student_id, students.name," +
			" students.admission_number, students.father_name, standards.standard_name, standards.level, standards.stream, " +
			" standard_section_mapping.section_name, student_academic_session_details.session_status from students join " +
			" student_academic_session_details on student_academic_session_details.academic_session_id = ? and students.student_id = student_academic_session_details.student_id " +
			" join standards on student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on " +
			" standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = " +
			" student_academic_session_details.academic_session_id and " +
			" student_academic_session_details.section_id = standard_section_mapping.section_id where students.institute_id = ? ";

////////////////////
	private static final String AND = " and ";
	private static final String WHERE = " where ";

	private static final String WHERE_CLAUSE_FOR_STUDENT_SEARCH = " students.institute_id = ? and student_academic_session_details.academic_session_id = ? "
			+ " and student_academic_session_details.session_status in %s %s %s %s %s";

	private static final String WHERE_CLAUSE_FOR_RELIVED_STUDENT_SEARCH = " students.institute_id = ? "
			+ " and student_academic_session_details.academic_session_id = ? and students.final_status in ('RELIEVED') "
			+ " and student_academic_session_details.session_status not in ('DELETED') %s %s %s";

	private static final String ORDER_BY_LIMIT_OFFSET_CLAUSE = " order by standards.level, students.name limit ? offset ? ";

	private static final String WHERE_INSTITUTE_CLAUSE = " students.institute_id = ? ";

	private static final String STUDENT_STATUS_CONDITION = " student_academic_session_details.session_status in %s ";

	private static final String WHERE_ACADEMIC_SESSION_CLAUSE = " student_academic_session_details.academic_session_id = ? ";

	private static final String NEW_ADMISSION_CONDITION = " and student_academic_session_details.is_new_admission = ? ";

	private static final String RTE_CONDITION = " and  students.rte = ? ";

	private static final String CATEGORY_CONDITION = " and  students.category in %s ";

	private static final String GENDER_CONDITION = " and  students.gender in %s ";

	private static final String RELIGION_CONDITION = " and  students.religion in %s ";

	private static final String AREA_TYPE_CONDITION = " and  students.area_type in %s ";

	private static final String SPECIALLY_ABLED_CONDITION = " and  students.specially_abled = ? ";

	private static final String BPL_CONDITION = " and  students.bpl = ? ";

	private static final String STATE_CONDITION = " and ( students.present_state in %s or students.permanent_state in %s ) ";

	private static final String INSTITUTE_HOUSE_CONDITION = " and students.house_id in %s ";

	private static final String ORDER_BY_CLAUSE = " order by standards.level, students.name ";

	private static final String ORDER_BY_STUDENT_NAME_CLAUSE = " order by students.name ";

	private static final String HOSTELLER_CONDITION = " students.is_hosteller = ? ";

	private static final String START_DATE_CLAUSE = " and students.admission_date >= ? ";

	private static final String END_DATE_CLAUSE = " and students.admission_date <= ? ";

	private static final String COUNT_WHERE_CLAUSE_FOR_STUDENT_SEARCH = "students.institute_id = ? and "
			+ "student_academic_session_details.academic_session_id = ? and student_academic_session_details.session_status in %s %s %s %s %s";

	private static final String GET_CLASS_STUDENTS_IN_ACADEMIC_SESSION = "select students.*, student_academic_session_details.*, "
			+ "academic_session.*, standards.*, standard_section_mapping.*, hostel_management.* from students left join student_academic_session_details on "
			+ "students.student_id = student_academic_session_details.student_id join academic_session on "
			+ "student_academic_session_details.academic_session_id = academic_session.academic_session_id join standards on "
			+ "student_academic_session_details.standard_id = standards.standard_id left join standard_section_mapping on "
			+ "standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = "
			+ "student_academic_session_details.academic_session_id and "
			+ "student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ "left join hostel_management on student_academic_session_details.hostel_id = hostel_management.hostel_id "
			+ "where student_academic_session_details.academic_session_id = ? %s %s %s and "
			+ "students.institute_id = ? %s order by standards.level, students.name";

	private static final String STANDARD_CLAUSE = " student_academic_session_details.standard_id in ";

	private static final String SECTION_CLAUSE = " and student_academic_session_details.section_id in ";

//	private static final String OR_SECTION_CLAUSE = " or student_academic_session_details.section_id in ";

	private static final String HOSTEL_CLAUSE = " and student_academic_session_details.hostel_id in ";

	private static final String TAGGED_ACTION_CLAUSE = "json_contains(student_tags, json_object('taggedActions',  ? )) ";

	private static final String SEARCH_STUDENTS_WITHOUT_ACADEMIC_SESSION = "select * from students ";

	private static final String WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION = " students.institute_id = ? "
			+ " and students.final_status in %s order by students.name, students.father_name";

	private static final String WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION_MULTIPLE_INSTITUTES = " students.institute_id in %s "
			+ " and students.final_status in %s order by students.name, students.father_name";

	private static final String UPDATE_STUDENT_ACADEMIC_SESSION_DETAILS = "update student_academic_session_details set section_id = ?, is_new_admission = ? "
			+ " where student_id = ? and academic_session_id = ? and standard_id = ?";

	private static final String UPDATE_STUDENT_SESSION_STATUS = "update student_academic_session_details set session_status = ? "
			+ " where student_id = ? and academic_session_id = ?";

	private static final String UPDATE_STUDENT_STATUS = "update students " +
			" set final_status = ?, relieved_metadata = ?, relieve_date = ?, relieve_reason = ?, tc_variables = ?, tc_details = ? " +
			" where student_id = ? and institute_id = ? ";

	private static final String GET_STUDENT_BY_ID = "select * from students where student_id = ?";

	private static final String GET_STUDENT_INSTITUTE = "select institute_id from students where student_id = ?";

	private static final String GET_MULTIPLE_STUDENT_BY_IDS = "select * from students where student_id in (%s)";

	private static final String UPDATE_STUDENT_DOCUMENTS = "update students set student_documents = ? where student_id = ?";

	/**
	 * Condition for ENROLMENT_PENDING is must for concurrent update. In such cases,
	 * this cmd is executed again even if student is enrolled
	 */
	private static final String ADMIT_STUDENT = "update students set final_status = ?, admission_number = ?, admission_date = ?, house_id = ?, sibling_group_id = ? where institute_id = ? and student_id = ? "
			+ " and final_status = 'ENROLMENT_PENDING' ";

	private static final String NOT_IN_USER_CLAUSE = " students.admission_number not in (select user_institute_id from users)";

	private static final String GET_CAST_WISE_REPORT = " select students.institute_id, count(students.student_id) as student_count, "
			+ " category, gender, specially_abled, student_academic_session_details.standard_id, standards.standard_name, standards.stream from students  "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? and student_academic_session_details.session_status = 'ENROLLED' "
			+ " group by category,gender,specially_abled,student_academic_session_details.standard_id, standards.standard_name "
			+ " order by standards.level ";

	private static final String GET_RELIGION_WISE_REPORT = " select students.institute_id, count(students.student_id) as student_count, "
			+ " religion, gender, student_academic_session_details.standard_id, standards.standard_name, standards.stream from students  "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? and student_academic_session_details.session_status = 'ENROLLED' "
			+ " group by religion, gender, student_academic_session_details.standard_id, standards.standard_name "
			+ " order by standards.level ";

	private static final String GET_HOUSE_SUMMARY_REPORT = "select students.institute_id, count(students.student_id) as student_count, "
			+ " institute_houses.name,gender,student_academic_session_details.standard_id, standards.standard_name, standards.stream from students "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " and student_academic_session_details.academic_session_id = ? "
			+ " inner join institute_houses on students.house_id = institute_houses.house_id "
			+ " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? "
			+ " and student_academic_session_details.session_status = 'ENROLLED' "
			+ " group by students.gender,institute_houses.name,student_academic_session_details.standard_id, standards.standard_name "
			+ " order by standards.level";


	private static final String GET_GENDER_WISE_REPORT = " select students.institute_id, count(students.student_id) as student_count, "
			+ " gender, student_academic_session_details.standard_id, standards.standard_name, standards.stream from students  "
			+ " inner join student_academic_session_details on student_academic_session_details.student_id = students.student_id  "
			+ " inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? and student_academic_session_details.session_status = 'ENROLLED' "
			+ " group by gender, student_academic_session_details.standard_id, standards.standard_name "
			+ " order by standards.level ";

	private static final String UPDATE_STUDENT_SESSION_DETAILS = "update student_academic_session_details ";

	private static final String COMMA = " , ";

	private static final String SET = " set ";

	private static final String UPDATE_SECTION_CLAUSE = "section_id = ? ";

	private static final String UPDATE_ROLL_NUMBER_CLAUSE = "roll_number = ? ";

	private static final String UPDATE_HEIGHT_CLAUSE = "height = ? ";

	private static final String UPDATE_WEIGHT_CLAUSE = "weight = ? ";

	private static final String UPDATE_BOARD_REGISTRATION_CLAUSE = "board_registration_number = ? ";

	private static final String UPDATE_MEDIUM = "medium = ? ";

	private static final String UPDATE_IS_NEW_ADMISSION = " is_new_admission = ? ";

	private static final String UPDATE_STUDENT_CLAUSE = "update students ";

	private static final String UPDATE_REGISTRATION_NUMBER_CLAUSE = " registration_number = ? ";
	private static final String UPDATE_ADMISSION_NUMBER_CLAUSE = " admission_number = ? ";
	private static final String UPDATE_PRIMARY_CONTACT_NUMBER_CLAUSE = "primary_contact_number = ? ";

	private static final String UPDATE_PRIMARY_EMAIL_CLAUSE = "primary_email = ? ";

	private static final String UPDATE_MOTHER_NAME_CLAUSE = "mother_name = ? ";

	private static final String UPDATE_FATHER_NAME_CLAUSE = "father_name = ? ";

	private static final String UPDATE_FATHER_CONTACT_NUMBER_CLAUSE = "father_contact_number = ? ";

	private static final String UPDATE_MOTHER_CONTACT_NUMBER_CLAUSE = "mother_contact_number = ? ";

	private static final String UPDATE_CASTE_CLAUSE = "caste = ? ";

	private static final String UPDATE_CATEGORY_CLAUSE = "category = ? ";

	private static final String UPDATE_BLOOD_GROUP_CLAUSE = "blood_group = ? ";

	private static final String UPDATE_PEN_CLAUSE = "pen_number = ? ";

	private static final String UPDATE_AADHAR_NUMBER_CLAUSE = "aadhar_number = ? ";
	private static final String UPDATE_ADMISSION_DATE_CLAUSE = "admission_date = ? ";
	private static final String UPDATE_DATE_OF_BIRTH_CLAUSE = "date_of_birth = ? ";
	private static final String UPDATE_FATHERS_OCCUPATION_CLAUSE = "father_occupation = ? ";

	private static final String UPDATE_STUDENT_ID_WHERE_CONDITION = " where student_id = ? ";

	private static final String UPDATE_STUDENT_SESSION_DETAILS_WHERE_CONDITION = " where student_id = ? "
			+ " and academic_session_id = ? ";

	private static final String STUDENTS_IN_ALL_ACADEMIC_SESSION = " select students.*, academic_session.*, standards.*, "
			+ " standard_section_mapping.*, student_academic_session_details.* from students "
			+ " inner join student_academic_session_details on students.student_id = student_academic_session_details.student_id "
			+ " left join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
			+ " left join standards on student_academic_session_details.standard_id = standards.standard_id "
			+ " left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id =  student_academic_session_details.academic_session_id and  student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " where student_academic_session_details.session_status in ('ENROLLED') and students.final_status in ('ENROLLED') ";

	private static final String GET_STUDENTS_WITH_WALLET = "select * from students " +
			" left join student_academic_session_details on students.student_id = student_academic_session_details.student_id " +
			" inner join user_wallet on (user_wallet.user_id = students.student_id and user_wallet.institute_id = students.institute_id and user_wallet.user_type = 'STUDENT') " +
			" join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id " +
			" join standards on student_academic_session_details.standard_id = standards.standard_id " +
			" left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id and student_academic_session_details.section_id = standard_section_mapping.section_id " +
			" where student_academic_session_details.academic_session_id = ? %s %s " +
			" and students.institute_id = ? order by standards.level, students.name";

	private static final String WHERE_STUDENT_STATUS_IN_CLAUSE = " and student_academic_session_details.session_status in ";

	private static final String WHERE_STUDENT_STANDARD_IN_CLAUSE = " and student_academic_session_details.standard_id in ";

	private static final String PERCENTAGE_S = " %s ";

	private static final String UPDATE_STUDENT_STANDARD = "update student_academic_session_details "
			+ " set standard_id = ?, section_id = ? "
			+ " where academic_session_id = ? and student_id = ? ";

	private static final String UPDATE_STUDENT_SIBLING_DETAILS = "update students "
			+ " set sibling_group_id = ? where student_id in %s ";

	private static final String DELETE_STUDENT_SIBLING_DETAILS_BY_ID = "update students "
			+ " set sibling_group_id = ? where sibling_group_id = ? ";

	private static final String GET_STUDENT_SIBLING_DETAILS = "select * from students "
			+ " where students.institute_id = ? and students.sibling_group_id is not null ";

	private static final String GET_STUDENT_SIBLING_WITH_SESSION_DETAILS = "select * from students "
			+" inner join student_academic_session_details on students.student_id = student_academic_session_details.student_id and student_academic_session_details.academic_session_id = ? "
			+" inner join academic_session on student_academic_session_details.academic_session_id = academic_session.academic_session_id "
			+" inner join standards on student_academic_session_details.standard_id = standards.standard_id "
			+" left join standard_section_mapping on standard_section_mapping.standard_id = standards.standard_id "
			+" and standard_section_mapping.academic_session_id = student_academic_session_details.academic_session_id "
			+" and student_academic_session_details.section_id = standard_section_mapping.section_id "
			+ " where students.institute_id = ? and student_academic_session_details.academic_session_id = ? "
			+ " and students.sibling_group_id is not null ";

	private static final String SIBLING_GROUP_ID_CLAUSE = " and students.sibling_group_id = ? ";

	private static final String SESSION_STATUS_CLAUSE = " and student_academic_session_details.session_status <> 'DELETED' ";

	private static final String GET_BIRTHDAY_STUDENT_BASIC_INFO_FOR_INSTITUTE = "select * from students "
			+ " where institute_id = ? and MONTH(date_of_birth) = ? and DAY(date_of_birth) = ? and final_status = 'ENROLLED' ";

	private static final String GET_BIRTHDAY_STUDENT_USER_INFO_FOR_INSTITUTE = "select users.* from students "
			+ " inner join users on students.student_id = users.user_id and students.institute_id = users.institute_id "
			+ " where MONTH(date_of_birth) = ? and DAY(date_of_birth) = ? and final_status = 'ENROLLED' and users.user_status = 'ENABLED' ";

	private static final String STUDENT_ID_SIBLING_GROUP_ID_CLAUSE = " and students.sibling_group_id = (select sibling_group_id from students where student_id = ?) ";

	private static final String GET_STUDENT_BIOMETRIC_DATA_BY_ID = "select device_user_id, rfid_card_data, fingerprint_data, " +
			"face_data from students where student_id = ? %s ";

	private static final String UPDATE_STUDENT_BIOMETRIC_DEVICE_IDENTIFICATION = "update students set rfid_card_data = ?, fingerprint_data = ?, face_data = ? where student_id = ?";

	private static final String UPDATE_STUDENT_TC_DETAILS = "update students set tc_details = ? where student_id = ? and institute_id = ? ";

	public static final String SELECT_USER_COUNT = "select count(*) from users where user_id = ?";
	public static final String SELECT_USER_IDS = "select user_id from users where user_id in %s";

	public static final String UPDATE_USER_NAME_AND_USER_INSTITUTE_ID = "update users set %s user_institute_id = ?, email = ?, phone_number = ? where user_id = ?";

	public static final String UPDATE_HOUSE_ID_BY_STUDENT_ID = " update students set house_id = ? where student_id = ? and institute_id = ? ";

	public static final String UPDATE_SIBLING_GROUP_ID_BY_STUDENT_ID = " update students set sibling_group_id = ? where student_id = ? and institute_id = ? ";

	private static final String SOFT_DELETE_STUDENT_INFO = "update students "
			+ " set admission_number = ?, registration_number = ?, final_status = ? " +
			" where institute_id = ? and student_id = ?";

	private static final String SOFT_DELETE_STUDENT_ALL_SESSION_INFO = "update student_academic_session_details "
			+ " set session_status = ? where student_id = ?";

	private static final String GET_STANDARD_DETAILS_WITH_STUDENT_COUNT_AND_GENDER_COUNT =
					"SELECT standards.*, standard_section_mapping.*, student_counts.student_count, gender_counts.gender, gender_counts.gender_count FROM standards " +
					"LEFT JOIN standard_section_mapping ON standards.standard_id = standard_section_mapping.standard_id AND standard_section_mapping.academic_session_id = ? " +
					"LEFT JOIN (SELECT standard_id, section_id, COUNT(*) AS student_count FROM student_academic_session_details " +
					"JOIN students ON student_academic_session_details.student_id = students.student_id WHERE academic_session_id = ? AND student_academic_session_details.session_status IN (%s) " +
					"GROUP BY standard_id, section_id) AS student_counts ON standards.standard_id = student_counts.standard_id AND student_counts.section_id <=> standard_section_mapping.section_id " +
					"LEFT JOIN (SELECT standard_id, section_id, gender, COUNT(*) AS gender_count FROM student_academic_session_details " +
					"JOIN students ON student_academic_session_details.student_id = students.student_id WHERE academic_session_id = ? AND student_academic_session_details.session_status IN (%s) " +
					"GROUP BY standard_id, section_id, gender) AS gender_counts ON standards.standard_id = gender_counts.standard_id AND gender_counts.section_id <=> standard_section_mapping.section_id " +
					"WHERE standards.institute_id = ? %s %s ";

	private static final String MULTIPLE_STANDARD_ID_CLAUSE = " AND standards.standard_id IN ( ";

	private static final String MULTIPLE_SECTION_ID_CLAUSE = " AND (standard_section_mapping.section_id IS NULL OR standard_section_mapping.section_id IN ( ";


	private static final String UPDATE_STUDENT_TAG_BY_STUDENT_ID = " update students set student_tags = ? where student_id = ? and institute_id = ? ";


	private final ICache<InstituteSessionCacheKey, byte[]> studentSearchCache;

	private final CacheGZIPSerializer<StudentSearchCacheData> studentSearchCacheGZIPSerializer;

	public StudentDao(JdbcTemplate jdbcTemplate, TransactionTemplate transactionTemplate, InstituteDao instituteDao,
					  UserWalletDao userWalletDao, CacheFactory cacheFactory) {
		this.jdbcTemplate = jdbcTemplate;
		this.transactionTemplate = transactionTemplate;
		this.instituteDao = instituteDao;
		this.userWalletDao = userWalletDao;
		// TODO: revisit threshold for institute session combination
		// Per entry storage size is ~40 bytes, 100 * 5000 (Student Count) * 40 = 20 MB data in memory. May consider increasing this value in future
		this.studentSearchCache = cacheFactory.getInMemoryLRUCache(100, new StudentDao.StudentSearchCacheLoader());
		this.studentSearchCacheGZIPSerializer = new CacheGZIPSerializer();
	}

	private class StudentSearchCacheLoader implements ICacheLoader<InstituteSessionCacheKey, byte[]> {
		@Override
		public byte[] load(InstituteSessionCacheKey key) {
			StopWatch stopWatch = new StopWatch();
			stopWatch.start();
			try {
				List<StudentLiteRowV2> allStudentList = fetchAllStudentSearchDataUncached(key.getInstituteId(), key.getAcademicSessionId());
				Collections.sort(allStudentList, new Comparator<StudentLiteRowV2>() {
					@Override
					public int compare(StudentLiteRowV2 o1, StudentLiteRowV2 o2) {
						int standardCompare = o1.getLevel() - o2.getLevel();
						if (standardCompare != 0) {
							return standardCompare;
						}

						if (o1.getSectionName() != null && o2.getSectionName() != null) {
							int sectionCompare = o1.getSectionName().compareToIgnoreCase(o2.getSectionName());
							if (sectionCompare != 0) {
								return sectionCompare;
							}
						}

						return o1.getName().compareToIgnoreCase(o2.getName());
					}
				});

				List<StudentSearchCacheData> cacheDataList = new ArrayList<>();
				for (StudentLiteRowV2 row : allStudentList) {
					StringBuilder sb = new StringBuilder();
					sb.append(WordUtils.capitalizeFully(row.getName())).append(" (").append(row.getAdmNum()).append(", ");
					if (StringUtils.isNotBlank(row.getfName())) {
						sb.append(WordUtils.capitalizeFully(row.getfName())).append(", ");
					}
					sb.append(row.getClsSec()).append(")");
					cacheDataList.add(new StudentSearchCacheData(row.getId(), sb.toString(), row.getStatus()));
				}
				byte [] result = studentSearchCacheGZIPSerializer.serialize(cacheDataList);
				stopWatch.stop();
				logger.info("Fetched all student for key {} in {} ms", key.getKeyId(), stopWatch.getTime(TimeUnit.MILLISECONDS));
				return result;
			} catch (Exception e) {
				logger.error("Error while fetching data from DB and serializing for key {}", key, e);
				throw new EmbrateRunTimeException("Error while fetching data from DB and serializing for " + key.getKeyId());
			}


		}
	}

	private void invalidateStudentSearchCache(int instituteId, int academicSessionId){
		studentSearchCache.delete(new InstituteSessionCacheKey(instituteId, academicSessionId));
	}

	public Double getWalletAmount(UUID studentId, DBLockMode dbLockMode) {
		return userWalletDao.getUserWalletAmount(studentId, dbLockMode);
	}

	public UUID addStudent(StudentPayload studentPayload, boolean registrationCounter) {
		try {
			final UUID studentId = transactionTemplate.execute(new TransactionCallback<UUID>() {

				@Override
				public UUID doInTransaction(TransactionStatus status) {
					final UUID studentUUID = addStudentDetails(studentPayload, registrationCounter);
					if (studentUUID == null) {
						throw new RuntimeException("Unable to create student");
					}
					final int rows = jdbcTemplate.update(CREATE_STUDENT_WALLET, studentPayload.getInstituteId(),
							studentUUID.toString(), UserType.STUDENT.name());
					if (rows != 1) {
						throw new RuntimeException("Unable to create student wallet");
					}
					return studentUUID;
				}
			});
			return studentId;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to execute transaction", e);
		} finally {
			invalidateStudentSearchCache(studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession());
		}
		return null;
	}

	private String getRegistrationNumber(String registrationNumber, CounterType counterType, int instituteId,
										 boolean registrationCounter) {
		if (!registrationCounter) {
			return registrationNumber;
		}

		final CounterData counterData = instituteDao.getCounter(instituteId, counterType, true);
		if (counterData == null) {
			throw new EmbrateRunTimeException(
					counterType.name() + " counter is not present for institute " + instituteId);
		}
		return counterData.getFullCounterValue();
	}

	private UUID addStudentDetails(StudentPayload studentPayload, boolean registrationCounter) {
		final UUID studentId = UUID.randomUUID();
		int instituteId = studentPayload.getInstituteId();
		String registrationRequestNumber = studentPayload.getStudentBasicInfo().getRegistrationRequestNumber();
		if (StringUtils.isBlank(registrationRequestNumber)) {
			logger.error("Invalid registrationRequestNumber {} , instituteId {}", registrationRequestNumber,
					instituteId);
			return null;
		}

		boolean success = false;
		try {
			String registrationNumber = studentPayload.getStudentBasicInfo().getRegistrationNumber();
			final CounterType counterType = CounterType.REGISTRATION_NUMBER;
			if (registrationCounter) {
				registrationNumber = getRegistrationNumber(registrationNumber, counterType,
						studentPayload.getInstituteId(), registrationCounter);
			}
			logger.info(
					"Creating student with registrationRequestNumber, {}, registrationNumber {} for student {}, institute {}, registrationCounter {}",
					registrationRequestNumber, registrationNumber, studentId, instituteId, registrationCounter);
			final int row = jdbcTemplate.update(ADD_STUDENT_DETAILS, studentPayload.getInstituteId(),
					registrationRequestNumber, registrationNumber,
					studentPayload.getStudentBasicInfo().getOnlineRegistrationNumber(),
					registrationNumber + TEMP_REGISTRATION_SUFFIX,
					studentPayload.getStudentBasicInfo().getPenNumber(), studentId.toString(),
					(studentPayload.getStudentBasicInfo().getAdmissionDate() != null)
							&& (studentPayload.getStudentBasicInfo().getAdmissionDate() > 0)
							? new Timestamp(studentPayload.getStudentBasicInfo().getAdmissionDate() * 1000l)
							: null,
					(studentPayload.getStudentBasicInfo().getOnlineRegistrationDate() != null)
							&& (studentPayload.getStudentBasicInfo().getOnlineRegistrationDate() > 0)
							? new Timestamp(
							studentPayload.getStudentBasicInfo().getOnlineRegistrationDate() * 1000l)
							: null,
					(studentPayload.getStudentBasicInfo().getRegistrationDate() != null)
							&& (studentPayload.getStudentBasicInfo().getRegistrationDate() > 0)
							? new Timestamp(
							studentPayload.getStudentBasicInfo().getRegistrationDate() * 1000l)
							: null,
					capitalizeFirstOfAllWords(studentPayload.getStudentBasicInfo().getName()),
					(studentPayload.getStudentBasicInfo().getDateOfBirth() != null)
							&& (studentPayload.getStudentBasicInfo().getDateOfBirth() > 0)
							? new Timestamp(studentPayload.getStudentBasicInfo().getDateOfBirth() * 1000l)
							: null,
					studentPayload.getStudentBasicInfo().getBirthPlace(),
					studentPayload.getStudentBasicInfo().getGender() == null ? null
							: studentPayload.getStudentBasicInfo().getGender().name(),
					studentPayload.getStudentBasicInfo().getUserCategory() == null ? null
							: studentPayload.getStudentBasicInfo().getUserCategory().name(),
					studentPayload.getStudentBasicInfo().getReligion(), studentPayload.getStudentBasicInfo().isRte(),
					studentPayload.getStudentBasicInfo().getAadharNumber(),
					studentPayload.getStudentBasicInfo().getMotherTongue(),
					studentPayload.getStudentBasicInfo().getAreaType() == null ? null
							: studentPayload.getStudentBasicInfo().getAreaType().name(),
					studentPayload.getStudentBasicInfo().isSpeciallyAbled(),
					studentPayload.getStudentBasicInfo().isBpl(),
					studentPayload.getStudentBasicInfo().getPermanentAddress(),
					studentPayload.getStudentBasicInfo().getPermanentCity(),
					studentPayload.getStudentBasicInfo().getPermanentState(),
					studentPayload.getStudentBasicInfo().getPermanentZipcode(),
					studentPayload.getStudentBasicInfo().getPermanentCountry(),
					studentPayload.getStudentBasicInfo().getPresentAddress(),
					studentPayload.getStudentBasicInfo().getPresentCity(),
					studentPayload.getStudentBasicInfo().getPresentState(),
					studentPayload.getStudentBasicInfo().getPresentZipcode(),
					studentPayload.getStudentBasicInfo().getPresentCountry(),
					studentPayload.getStudentBasicInfo().getNationality(),
					studentPayload.getStudentBasicInfo().getPrimaryContactNumber(),
					studentPayload.getStudentBasicInfo().getPrimaryEmail(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: capitalizeFirstOfAllWords(studentPayload.getStudentFamilyInfo().getFathersName()),
					studentPayload.getStudentFamilyInfo() == null ? null
							: capitalizeFirstOfAllWords(studentPayload.getStudentFamilyInfo().getMothersName()),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersQualification(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersQualification(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersOccupation(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersAnnualIncome(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersOccupation(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersAnnualIncome(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersContactNumber(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersContactNumber(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersAadharNumber(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersAadharNumber(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getFathersPanCardDetails(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getMothersPanCardDetails(),
					studentPayload.getStudentFamilyInfo() == null ? null
							: studentPayload.getStudentFamilyInfo().getApproxFamilyIncome(),
					studentPayload.getStudentGuardianInfoList() == null ? null
							: GSON.toJson(studentPayload.getStudentGuardianInfoList()),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getSchoolName(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getClassPassed(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getMedium(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getResult(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getPercentage(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null
							: studentPayload.getStudentPreviousSchoolInfo().getYearOfPassing(),
			(studentPayload.getStudentMedicalInfo() == null)
							|| (studentPayload.getStudentMedicalInfo().getBloodGroup() == null) ? null
							: studentPayload.getStudentMedicalInfo().getBloodGroup().name(),
					studentPayload.getStudentMedicalInfo() == null ? null
							: studentPayload.getStudentMedicalInfo().getBloodPressure(),
					studentPayload.getStudentMedicalInfo() == null ? null
							: studentPayload.getStudentMedicalInfo().getPulse(),
					studentPayload.getStudentMedicalInfo() == null ? null
							: studentPayload.getStudentMedicalInfo().getHeight(),
					studentPayload.getStudentMedicalInfo() == null ? null
							: studentPayload.getStudentMedicalInfo().getWeight(),
					(studentPayload.getStudentMedicalInfo() != null)
							&& (studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() != null)
							&& (studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() > 0)
							? new Timestamp(
							studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination()
									* 1000l)
							: null,
					studentPayload.isNewAdmission() ? studentPayload.getAdmissionAcademicSession() : null,
					studentPayload.getStudentBasicInfo().getCaste(),
					studentPayload.getStudentBasicInfo().getWhatsappNumber(),
					studentPayload.getStudentBasicInfo().isSponsored(),
					studentPayload.getStudentBasicInfo().getPermanentPostOffice(),
					studentPayload.getStudentBasicInfo().getPermanentPoliceStation(),
					studentPayload.getStudentBasicInfo().getPresentPostOffice(),
					studentPayload.getStudentBasicInfo().getPresentPoliceStation(),
					studentPayload.getStudentPreviousSchoolInfo() != null && studentPayload.getStudentPreviousSchoolInfo().isAdmissionTcBased(),
					studentPayload.getStudentPreviousSchoolInfo() == null ? null : studentPayload.getStudentPreviousSchoolInfo().getTcNumber(),
					studentPayload.getStudentBasicInfo().getInstituteHouseId() == null ? null : studentPayload.getStudentBasicInfo().getInstituteHouseId().toString(),
					studentPayload.getStudentBasicInfo().getAdmissionInClass(),
					studentPayload.getStudentBasicInfo().getSpeciallyAbledType(),
					studentPayload.getStudentBasicInfo().getStudentNameAsPerAadhar(),
					studentPayload.getStudentBasicInfo().getChildCategoryCriteria() == null ? null
							: studentPayload.getStudentBasicInfo().getChildCategoryCriteria().name(),
					studentPayload.getStudentBasicInfo().isHosteller(),
					studentPayload.getStudentBasicInfo().getApaarIdNo());
			if (row == 1) {
				success = addAcademicSessionStudentDetails(instituteId, studentPayload.getAdmissionAcademicSession(), studentId,
						studentPayload.getStandardId(), studentPayload.getSectionId(), studentPayload.getRollNumber(),
						studentPayload.getMedium(), StudentStatus.ENROLMENT_PENDING, studentPayload.isNewAdmission(), studentPayload.getHostelId());
			}

			if (success && registrationCounter
					&& !instituteDao.incrementCounter(studentPayload.getInstituteId(), counterType)) {
				logger.error(
						"Error while updating counter for student with registrationRequestNumber {}, registrationNumber {} for student {}, institute {}, registrationCounter {}",
						registrationRequestNumber, registrationNumber, studentId, instituteId, registrationCounter);
				throw new RuntimeException(
						"Unable to increment registration counter for institute " + studentPayload.getInstituteId());
			}

		} catch (final DataAccessException e) {
			logger.error(
					"Exception while adding student with registrationRequestNumber {}, registrationNumber {} for student {}, institute {}, registrationCounter {}",
					registrationRequestNumber, studentPayload.getStudentBasicInfo().getRegistrationNumber(), studentId,
					instituteId, registrationCounter, e);
			ExceptionHandling.HandleException(e, STUDENT, "registration number");

		} catch (final Exception e) {
			logger.error(
					"Exception while adding student with registrationRequestNumber {}, registrationNumber {} for student {}, institute {}, registrationCounter {}",
					registrationRequestNumber, studentPayload.getStudentBasicInfo().getRegistrationNumber(), studentId,
					instituteId, registrationCounter, e);
		}
		return success ? studentId : null;
	}

	public boolean addAcademicSessionStudentDetails(int instituteId, int academicSession, UUID studentId, UUID standardId,
													Integer sectionId, String rollNumber,
													Medium medium, StudentStatus studentStatus, boolean isNewAdmission, UUID hostelId) {
		try {
			return jdbcTemplate.update(ADD_ACADEMIC_SESSION_STUDENT_DETAILS, academicSession, studentId.toString(),
					standardId.toString(), sectionId, rollNumber, medium == null ? null : medium.name(), studentStatus.name(),
					isNewAdmission, hostelId == null ? null : hostelId.toString()) == 1;
		} catch (final Exception e) {
			logger.error(
					"error while adding academic session {} for student {}, standardId {}, sectionId {}, rollNumber {}",
					academicSession, studentId, standardId, sectionId, rollNumber, e);
		} finally {
			invalidateStudentSearchCache(instituteId, academicSession);
		}
		return false;

	}

	public List<Student> getAllStudentWithoutSession(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return getStudentWithHouseDetails(instituteId, StudentRowMapper.getStudentList(
					jdbcTemplate.query(GET_STUDENT_BASIC_INFO, args, STUDENT_WITHOUT_SESSION_ROW_MAPPER)));
		} catch (final Exception e) {
			logger.error("Exception while getting students for instituteId {}", instituteId, e);
		}

		return null;
	}

	public Student getStudentByAcademicSessionStudentId(int instituteId, int academicSessionId, UUID studentId) {
		try {
			final Object[] args = { instituteId, studentId.toString(), academicSessionId };
			return getStudentWithHouseDetails(instituteId,
					jdbcTemplate.queryForObject(GET_STUDENT_BY_ACADEMIC_SESSION, args, STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}

		return null;
	}

	public List<Student> getStudentInAcademicSessions(int instituteId, List<Integer> academicSessionIds,
													  UUID studentId) {
		try {
			final StringBuilder inQuery = new StringBuilder();
			inQuery.append(" and student_academic_session_details.academic_session_id in (");
			String delimiter = "";
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(studentId.toString());
			for (final Integer academicSessionId : academicSessionIds) {
				args.add(academicSessionId);
				inQuery.append(delimiter).append(" ? ");
				delimiter = ",";
			}
			inQuery.append(")");
			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_STUDENT_IN_ACADEMIC_SESSIONS, inQuery.toString()),
					args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching student {}", studentId, e);
		}

		return null;
	}

	public List<Student> getStudentInAllSessions(int instituteId, UUID studentId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(studentId.toString());
			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_STUDENT_IN_ACADEMIC_SESSIONS, ""), args.toArray(),
					STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching student {}", studentId, e);
		}

		return null;
	}

	public Student getStudent(UUID studentId) {
		try {
			final Object[] args = { studentId.toString() };
			Student student = StudentRowMapper.getStudent(
					jdbcTemplate.queryForObject(GET_STUDENT_BY_ID, args, STUDENT_WITHOUT_SESSION_ROW_MAPPER));
			if(student== null) {
				return null;
			}
			return getStudentWithHouseDetails(student.getInstituteId(), student);
		} catch (final Exception e) {
			logger.error("Exception while fetching student {}", studentId, e);
		}

		return null;
	}

	public Integer getStudentInstitute(UUID studentId) {
		try {
			final Object[] args = { studentId.toString() };
			return jdbcTemplate.queryForInt(GET_STUDENT_INSTITUTE, args);
		} catch (final Exception e) {
			logger.error("Exception while fetching student {}", studentId, e);
		}

		return null;
	}



	public List<Student> getStudent(List<UUID> studentIds) {
		try {
			if(CollectionUtils.isEmpty(studentIds)) {
				return null;
			}
			final List<Object> args = new ArrayList<Object>();
			String inQuery = "";
			String delimiter = "";
			for (UUID studentId : studentIds) {
				args.add(studentId.toString());
				inQuery += delimiter + "? ";
				delimiter = ",";
			}
			return StudentRowMapper
					.getStudentList(jdbcTemplate.query(String.format(GET_MULTIPLE_STUDENT_BY_IDS, inQuery),
							args.toArray(), STUDENT_WITHOUT_SESSION_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while fetching students", e);
		}
		return null;
	}

	public boolean updateDocuments(UUID studentId, List<Document<StudentDocumentType>> studentDocuments) {
		try {
			final Object[] args = { GSON.toJson(studentDocuments), studentId.toString() };
			return jdbcTemplate.update(UPDATE_STUDENT_DOCUMENTS, args) == 1;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating student documents {}", studentId);
		} catch (final Exception e) {
			logger.error("Exception while updating student documents {}", studentId, e);
		}
		return false;
	}

	public List<Student> getStudentByAcademicSessionStudentIds(int instituteId, int academicSessionId,
															   List<UUID> studentIds, StudentStatus studentStatus) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			final StringBuilder inQuery = new StringBuilder();
			inQuery.append("(");
			boolean first = true;
			for (final UUID uuid : studentIds) {
				args.add(uuid.toString());
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");
			args.add(studentStatus.name());
			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_STUDENTS_BY_ACADEMIC_SESSION, inQuery.toString()),
					args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching student for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}

		return null;
	}

	public List<Student> getStudentByAcademicSessionStudentIds(int instituteId, int academicSessionId,
															   List<UUID> studentIds, Set<StudentStatus> studentStatusSet) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);
			final StringBuilder inQuery = new StringBuilder();
			inQuery.append("(");
			boolean first = true;
			for (final UUID uuid : studentIds) {
				args.add(uuid.toString());
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");

			final StringBuilder statusInQuery = new StringBuilder();
			statusInQuery.append("(");
			first = true;
			for (final StudentStatus studentStatus : studentStatusSet) {
				if(studentStatus == null) {
					continue;
				}
				args.add(studentStatus.name());
				if (first) {
					statusInQuery.append("?");
					first = false;
					continue;
				}
				statusInQuery.append(", ?");
			}
			statusInQuery.append(")");
			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_STUDENTS_BY_ACADEMIC_SESSION_AND_STATUS, inQuery.toString(), statusInQuery.toString()),
					args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while fetching student for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}

		return null;
	}

	public List<Student> getClassRepeatersReport(int instituteId,int academicSessionId,int previousAcademicSessionId){
		try{
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(academicSessionId);
			args.add(instituteId);
            args.add(academicSessionId);
            args.add(previousAcademicSessionId);
            args.add(academicSessionId);
			return jdbcTemplate.query(GET_STUDENT_REPEATERS,args.toArray(),STUDENT_ROW_MAPPER);

		}catch (final Exception e) {
			logger.error("Exception while fetching repeated student for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;

	}

	public List<Student> getStudentByDeviceUserId(String deviceUserId, Set<Integer> institutes) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(deviceUserId);
			StringBuilder inQuery = new StringBuilder();
			inQuery.append(" and students.institute_id in ( ");
			String delimiter = "";
			for(Integer instituteId : institutes){
				args.add(instituteId);
				inQuery.append(delimiter).append(" ? ");
				delimiter = ",";
			}
			inQuery.append(")");

			return StudentRowMapper.getStudentList(
					jdbcTemplate.query(String.format(GET_STUDENT_BY_DEVICE_USER_ID, inQuery), args.toArray(), STUDENT_WITHOUT_SESSION_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Exception while getting student with device id {}, institutes {}", deviceUserId, institutes, e);
		}
		return null;
	}

	public List<Student> getStudentByAcademicSessionAdmissionNumber(int instituteId, int academicSessionId,
															  Set<String> admissionNumberSet) {
		try {
			List<Object> args = new ArrayList<>();
			args.add(instituteId);
			args.add(academicSessionId);

			StringBuilder admissionNumberInQuery = new StringBuilder();
			admissionNumberInQuery.append("and students.admission_number in ");
			admissionNumberInQuery.append("(");
			boolean firstStatus = true;
			for (final String admissionNumber : admissionNumberSet) {
				if(StringUtils.isBlank(admissionNumber)) {
					continue;
				}
				args.add(admissionNumber);
				if (firstStatus) {
					admissionNumberInQuery.append("?");
					firstStatus = false;
					continue;
				}
				admissionNumberInQuery.append(", ?");
			}
			admissionNumberInQuery.append(")");

			return getStudentWithHouseDetails(instituteId, (jdbcTemplate.query(
					String.format(GET_STUDENT_BY_ACADEMIC_SESSION_ADMISSION_NUMBER, admissionNumberInQuery),
					args.toArray(), STUDENT_ROW_MAPPER)));
		} catch (final Exception e) {
			logger.error("Exception", e);
		}

		return null;
	}

	public Student getStudentByLatestAcademicSession(UUID studentId, List<StudentStatus> studentStatusList) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(studentId.toString());
			args.add(studentId.toString());
			String statusParams = "(";
			String delimiter = "";
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
				statusParams += delimiter;
				statusParams += " ?";
				delimiter = ",";
			}
			statusParams += ")";
			Student student = jdbcTemplate.queryForObject(String.format(GET_STUDENT_BY_LATEST_ACADEMIC_SESSION, statusParams),
					args.toArray(), STUDENT_ROW_MAPPER);
			if(student == null){
				return null;
			}
			return getStudentWithHouseDetails(student.getInstituteId(), student);
		} catch (final Exception e) {
			logger.error("Exception for student {}", studentId, e);
		}

		return null;
	}

	public boolean relieveStudent(UUID studentId, int instituteId, StudentStatus studentStatus,
								  StudentRelievePayload studentRelievePayload,
								  List<StudentSessionStatusDetails> studentSessionStatusDetailsList) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if ((studentStatus != StudentStatus.RELIEVED) && (studentStatus != StudentStatus.NSO)) {
						logger.error("Invalid status change {} in relieving  student {}, institute {}", studentStatus,
								studentId, instituteId);
						return false;
					}
					final long relieveTime = studentRelievePayload.getRelieveDate() * 1000l;
					final String tcVariables = CollectionUtils.isEmpty(studentRelievePayload.getTcVariables()) ? null
							: GSON.toJson(studentRelievePayload.getTcVariables());
					final String relievedMetadata = CollectionUtils.isEmpty(studentRelievePayload.getRelievedMetadata()) ? null
							: GSON.toJson(studentRelievePayload.getRelievedMetadata());
					final Object[] args = {studentStatus.name(), relievedMetadata, new Timestamp(relieveTime),
							studentRelievePayload.getRelieveReason(), tcVariables, null,
							studentId.toString(), instituteId};

					if (jdbcTemplate.update(UPDATE_STUDENT_STATUS, args) != 1) {
						logger.error("Unable to update student status for student id {} and institute id {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to update student status");
					}

					boolean result = updateStudentSessionStatus(studentSessionStatusDetailsList);
					if(!result) {
						logger.error("Unable to update student status for student id {} and institute id {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to update student status");
					}
					return true;
				}
			});
			return success;
			//TODO:update student session status
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error(dataAccessException);
		} catch (final Exception e) {
			logger.error("Exception while relieving student {}", studentId, e);
		} finally {
			for(StudentSessionStatusDetails studentSessionStatusDetails: studentSessionStatusDetailsList){
				invalidateStudentSearchCache(instituteId, studentSessionStatusDetails.getAcademicSessionId());
			}
		}

		return false;
	}

	public boolean updateStudentSessionStatus(List<StudentSessionStatusDetails> studentSessionStatusDetailsList) {
		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(StudentSessionStatusDetails studentSessionStatusDetails : studentSessionStatusDetailsList) {
			final List<Object> args2 = new ArrayList<>();

			args2.add(studentSessionStatusDetails.getStudentStatus().name());
			args2.add(studentSessionStatusDetails.getStudentId().toString());
			args2.add(studentSessionStatusDetails.getAcademicSessionId());
			count++;
			batchInsertArgs.add(args2.toArray());
		}

		final int[] rows = jdbcTemplate.batchUpdate(UPDATE_STUDENT_SESSION_STATUS, batchInsertArgs);
		if (rows.length != count) {
			return false;
		}
		for (final int rowCount : rows) {
			if (rowCount != 1) {
				return false;
			}
		}
		return true;
	}

	public boolean enrollStudent(UUID studentId, int instituteId, StudentStatus studentStatus,
								 List<StudentSessionStatusDetails> studentSessionStatusDetailsList) {
		try {
			final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					if ((studentStatus != StudentStatus.ENROLLED)) {
						logger.error("Invalid status change {} in enrolling  student {}, institute {}", studentStatus,
								studentId, instituteId);
						return false;
					}
					final Object[] args = {studentStatus.name(), null, null, null, null, null, studentId.toString(), instituteId};

					if (jdbcTemplate.update(UPDATE_STUDENT_STATUS, args) != 1) {
						logger.error("Unable to update student status for student id {} and institute id {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to update student status");
					}

					boolean result = updateStudentSessionStatus(studentSessionStatusDetailsList);
					if(!result) {
						logger.error("Unable to update student status for student id {} and institute id {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to update student status");
					}
					return true;
				}
			});
			return success;

		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error(dataAccessException);
		} catch (final Exception e) {
			logger.error("Exception while relieving student {}", studentId, e);
		} finally {
			for(StudentSessionStatusDetails studentSessionStatusDetails: studentSessionStatusDetailsList){
				invalidateStudentSearchCache(instituteId, studentSessionStatusDetails.getAcademicSessionId());
			}
		}

		return false;
	}

	public UUID updateStudent(StudentPayload studentPayload, int academicSessionId,
							  MetaDataPreferences metaDataPreferences) {
		try {
			return transactionTemplate.execute(new TransactionCallback<UUID>() {
				@Override
				public UUID doInTransaction(TransactionStatus status) {
					boolean appendComma = false;
					final StringBuilder query = new StringBuilder();
					query.append(UPDATE_STUDENT);
					final List<Object> args = new ArrayList<>();

					if (studentPayload.getStudentBasicInfo() != null) {
						final StringBuilder basicInfoQuery = getBasicInfoQuery(studentPayload, args, metaDataPreferences);
						query.append(basicInfoQuery);
						appendComma = true;
					}
					if (studentPayload.getStudentFamilyInfo() != null) {
						if (appendComma) {
							query.append(", ");
						}
						appendComma = true;
						final StringBuilder familyQuery = getFamilyInfoQuery(studentPayload, args);
						query.append(familyQuery);
					}

					if (!CollectionUtils.isEmpty(studentPayload.getStudentGuardianInfoList())) {
						if (appendComma) {
							query.append(", ");
						}
						appendComma = true;
						query.append("gardians_details = ?");
						args.add(studentPayload.getStudentGuardianInfoList() == null ? null
								: GSON.toJson(studentPayload.getStudentGuardianInfoList()));
					}
					if (studentPayload.getStudentPreviousSchoolInfo() != null) {
						if (appendComma) {
							query.append(", ");
						}
						appendComma = true;
						final StringBuilder previousSchoolInfoQuery = getPreviousSchoolInfoQuery(studentPayload, args);
						query.append(previousSchoolInfoQuery);
					}
					if (studentPayload.getStudentMedicalInfo() != null) {
						if (appendComma) {
							query.append(", ");
						}
						appendComma = true;
						final StringBuilder medicalInfoQuery = getMedicalInfoQuery(studentPayload, args);
						query.append(medicalInfoQuery);
					}

					query.append(" where institute_id = ? and student_id = ?");
					args.add(studentPayload.getInstituteId());
					args.add(studentPayload.getStudentId().toString());
					if (jdbcTemplate.update(query.toString(), args.toArray()) != 1) {
						logger.error("Unable to update student {}", studentPayload.getStudentId());
						throw new EmbrateRunTimeException("Unable to update student");
					}

					if (jdbcTemplate.update(UPDATE_STUDENT_ACADEMIC_SESSION_DETAILS, studentPayload.getSectionId(),
							studentPayload.isNewAdmission(),
							studentPayload.getStudentId().toString(), academicSessionId,
							studentPayload.getStandardId().toString()) != 1) {
						logger.error("Unable to update student session details {}, {}", studentPayload.getStudentId(), academicSessionId);
						throw new EmbrateRunTimeException("Unable to update student session details");
					}
					if (jdbcTemplate.queryForInt(SELECT_USER_COUNT, studentPayload.getStudentId().toString()) == 1) {
						List<Object> userArgs = new ArrayList<>();
						StringBuilder queryUser = new StringBuilder();
						if(metaDataPreferences.isAdmissionNumberAuthFlow()
								&& !metaDataPreferences.isAdmissionCounter()) {
							String userName = UserNameUtils.getFullUserName(studentPayload.getStudentBasicInfo().getAdmissionNumber().trim(),
									metaDataPreferences.getInstituteUniqueCode());
							userArgs.add(userName);
							queryUser.append(" user_name = ?, ");
						}

						userArgs.add(studentPayload.getStudentBasicInfo().getAdmissionNumber().trim());
						userArgs.add(CryptoUtils.encrypt(studentPayload.getStudentBasicInfo().getPrimaryEmail()));
						userArgs.add(CryptoUtils.encrypt(studentPayload.getStudentBasicInfo().getPrimaryContactNumber()));
						userArgs.add(studentPayload.getStudentId().toString());

						if (jdbcTemplate.update(String.format(UPDATE_USER_NAME_AND_USER_INSTITUTE_ID, queryUser),
								userArgs.toArray()) != 1) {
							logger.error("Unable to update student user name  {}, {}", studentPayload.getStudentId(),
											studentPayload.getStudentBasicInfo().getAdmissionNumber());
							throw new EmbrateRunTimeException("Unable to update student username");
						}
					}
					return studentPayload.getStudentId();
				}
			});
		} catch (final DataAccessException de) {
			logger.error("dataAccessException {}", studentPayload.getStudentId(), academicSessionId, de);
			ExceptionHandling.HandleException(de, STUDENT, "admission number");
		} catch (Exception e) {
			logger.error("Error while updating student {}, {}", studentPayload.getStudentId(), academicSessionId, e);
		} finally {
			invalidateStudentSearchCache(studentPayload.getInstituteId(), academicSessionId);
		}
		return null;
	}

	private StringBuilder getBasicInfoQuery(StudentPayload studentPayload, List<Object> args,
											MetaDataPreferences metaDataPreferences) {
		final StringBuilder basicInfoQuery = new StringBuilder();
		basicInfoQuery.append(String.format(BASIC_INFO_QUERY,
				metaDataPreferences.isRegistrationCounter() ? "" : "registration_number = ?,",
				metaDataPreferences.isAdmissionCounter() ? "" : "admission_number = ?,"));
		if (!metaDataPreferences.isRegistrationCounter()) {
			args.add(studentPayload.getStudentBasicInfo().getRegistrationNumber());
		}
		if (!metaDataPreferences.isAdmissionCounter()) {
			args.add(studentPayload.getStudentBasicInfo().getAdmissionNumber());
		}
		args.add(studentPayload.getStudentBasicInfo().getPenNumber());
		args.add((studentPayload.getStudentBasicInfo().getAdmissionDate() != null)
				&& (studentPayload.getStudentBasicInfo().getAdmissionDate() > 0)
				? new Timestamp(studentPayload.getStudentBasicInfo().getAdmissionDate() * 1000l)
				: null);
		args.add((studentPayload.getStudentBasicInfo().getRegistrationDate() != null)
				&& (studentPayload.getStudentBasicInfo().getRegistrationDate() > 0)
				? new Timestamp(studentPayload.getStudentBasicInfo().getRegistrationDate() * 1000l)
				: null);
		args.add(capitalizeFirstOfAllWords(studentPayload.getStudentBasicInfo().getName()));
		args.add((studentPayload.getStudentBasicInfo().getDateOfBirth() != null)
				&& (studentPayload.getStudentBasicInfo().getDateOfBirth() > 0)
				? new Timestamp(studentPayload.getStudentBasicInfo().getDateOfBirth() * 1000l)
				: null);
		args.add(studentPayload.getStudentBasicInfo().getBirthPlace());
		args.add(studentPayload.getStudentBasicInfo().getGender() == null ? null
				: studentPayload.getStudentBasicInfo().getGender().name());
		args.add(studentPayload.getStudentBasicInfo().getUserCategory() == null ? null
				: studentPayload.getStudentBasicInfo().getUserCategory().name());
		args.add(studentPayload.getStudentBasicInfo().getReligion());
		args.add(studentPayload.getStudentBasicInfo().isRte());
		args.add(studentPayload.getStudentBasicInfo().getAadharNumber());
		args.add(studentPayload.getStudentBasicInfo().getMotherTongue());
		args.add(studentPayload.getStudentBasicInfo().getAreaType() == null ? null
				: studentPayload.getStudentBasicInfo().getAreaType().name());
		args.add(studentPayload.getStudentBasicInfo().isSpeciallyAbled());
		args.add(studentPayload.getStudentBasicInfo().isBpl());
		args.add(studentPayload.getStudentBasicInfo().getPermanentAddress());
		args.add(studentPayload.getStudentBasicInfo().getPermanentCity());
		args.add(studentPayload.getStudentBasicInfo().getPermanentState());
		args.add(studentPayload.getStudentBasicInfo().getPermanentPostOffice());
		args.add(studentPayload.getStudentBasicInfo().getPermanentPoliceStation());
		args.add(studentPayload.getStudentBasicInfo().getPermanentZipcode());
		args.add(studentPayload.getStudentBasicInfo().getPermanentCountry());
		args.add(studentPayload.getStudentBasicInfo().getPresentAddress());
		args.add(studentPayload.getStudentBasicInfo().getPresentCity());
		args.add(studentPayload.getStudentBasicInfo().getPresentState());
		args.add(studentPayload.getStudentBasicInfo().getPresentPostOffice());
		args.add(studentPayload.getStudentBasicInfo().getPresentPoliceStation());
		args.add(studentPayload.getStudentBasicInfo().getPresentZipcode());
		args.add(studentPayload.getStudentBasicInfo().getPresentCountry());
		args.add(studentPayload.getStudentBasicInfo().getNationality());
		args.add(studentPayload.getStudentBasicInfo().getPrimaryContactNumber());
		args.add(studentPayload.getStudentBasicInfo().getPrimaryEmail());
		args.add(studentPayload.getStudentBasicInfo().getCaste());
		args.add(studentPayload.getStudentBasicInfo().getWhatsappNumber());
		args.add(studentPayload.getStudentBasicInfo().isSponsored());
//		args.add(studentPayload.isNewAdmission());
		args.add(studentPayload.getStudentBasicInfo().getInstituteHouseId() == null ? null : studentPayload.getStudentBasicInfo().getInstituteHouseId().toString());
		args.add(studentPayload.getStudentBasicInfo().getAdmissionInClass());
		args.add(studentPayload.getStudentBasicInfo().getSpeciallyAbledType());
		args.add(studentPayload.getStudentBasicInfo().getStudentNameAsPerAadhar());
		args.add(studentPayload.getStudentBasicInfo().getChildCategoryCriteria() == null ? null
						: studentPayload.getStudentBasicInfo().getChildCategoryCriteria().name());
		args.add(studentPayload.getStudentBasicInfo().isHosteller());
		args.add(studentPayload.getStudentBasicInfo().getApaarIdNo());
		return basicInfoQuery;
	}

	private StringBuilder getFamilyInfoQuery(StudentPayload studentPayload, List<Object> args) {
		final StringBuilder familyInfoQuery = new StringBuilder();
		familyInfoQuery.append(FAMILY_INFO_QUERY);
		args.add(capitalizeFirstOfAllWords(studentPayload.getStudentFamilyInfo().getFathersName()));
		args.add(capitalizeFirstOfAllWords(studentPayload.getStudentFamilyInfo().getMothersName()));
		args.add(studentPayload.getStudentFamilyInfo().getFathersQualification());
		args.add(studentPayload.getStudentFamilyInfo().getMothersQualification());
		args.add(studentPayload.getStudentFamilyInfo().getFathersOccupation());
		args.add(studentPayload.getStudentFamilyInfo().getFathersAnnualIncome());
		args.add(studentPayload.getStudentFamilyInfo().getMothersOccupation());
		args.add(studentPayload.getStudentFamilyInfo().getMothersAnnualIncome());
		args.add(studentPayload.getStudentFamilyInfo().getFathersContactNumber());
		args.add(studentPayload.getStudentFamilyInfo().getMothersContactNumber());
		args.add(studentPayload.getStudentFamilyInfo().getFathersAadharNumber());
		args.add(studentPayload.getStudentFamilyInfo().getMothersAadharNumber());
		args.add(studentPayload.getStudentFamilyInfo().getFathersPanCardDetails());
		args.add(studentPayload.getStudentFamilyInfo().getMothersPanCardDetails());
		args.add(studentPayload.getStudentFamilyInfo().getApproxFamilyIncome());
		return familyInfoQuery;
	}

	private StringBuilder getPreviousSchoolInfoQuery(StudentPayload studentPayload, List<Object> args) {
		final StringBuilder previousSchoolInfoQuery = new StringBuilder();
		previousSchoolInfoQuery.append(PREVIOUS_SCHOOL_INFO_QUERY);
		args.add(studentPayload.getStudentPreviousSchoolInfo().getSchoolName());
		args.add(studentPayload.getStudentPreviousSchoolInfo().getClassPassed());
		args.add(studentPayload.getStudentPreviousSchoolInfo().getMedium());
		args.add(studentPayload.getStudentPreviousSchoolInfo().getResult());
		args.add(studentPayload.getStudentPreviousSchoolInfo().getPercentage());
		args.add(studentPayload.getStudentPreviousSchoolInfo().getYearOfPassing());
		args.add(studentPayload.getStudentPreviousSchoolInfo() == null ? false : studentPayload.getStudentPreviousSchoolInfo().isAdmissionTcBased());
		args.add(studentPayload.getStudentPreviousSchoolInfo() == null ? null : studentPayload.getStudentPreviousSchoolInfo().getTcNumber());
		return previousSchoolInfoQuery;
	}

	private StringBuilder getMedicalInfoQuery(StudentPayload studentPayload, List<Object> args) {
		final StringBuilder medicalInfoQuery = new StringBuilder();
		medicalInfoQuery.append(MEDICAL_INFO_QUERY);
		args.add(studentPayload.getStudentMedicalInfo().getBloodGroup() != null
				? studentPayload.getStudentMedicalInfo().getBloodGroup().name()
				: null);
		args.add(studentPayload.getStudentMedicalInfo().getBloodPressure());
		args.add(studentPayload.getStudentMedicalInfo().getPulse());
		args.add(studentPayload.getStudentMedicalInfo().getHeight());
		args.add(studentPayload.getStudentMedicalInfo().getWeight());
		args.add((studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() != null)
				&& (studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() > 0)
				? new Timestamp(studentPayload.getStudentMedicalInfo().getDateOfPhysicalExamination() * 1000l)
				: null);
		return medicalInfoQuery;
	}

	public SearchResultWithPagination<Student> searchStudentsInAcademicSesison(String searchText, int instituteId,
																			   int academicSessionId, List<StudentStatus> studentStatusList, int offset, int limit,
																			   String includeUserStatus, Set<UUID> standardIds) {
		return searchStudentsInAcademicSesison(searchText, instituteId,
		academicSessionId, studentStatusList, offset, limit, includeUserStatus, standardIds, null, null, null, null);
	}

	public SearchResultWithPagination<Student> searchStudentsInAcademicSesison(String searchText, int instituteId,
						int academicSessionId, List<StudentStatus> studentStatusList, int offset, int limit,
						String includeUserStatus, Set<UUID> standardIds, Set<Integer> sectionIdSet, Boolean isHosteller, Set<TaggedActions> taggedActionsSet, Set<UUID> hostelIdSet) {
		try {
			final StringBuilder query = new StringBuilder();
			final StringBuilder statusInQuery = new StringBuilder();
			final StringBuilder standardInQuery = new StringBuilder();
			final StringBuilder sectionInQuery = new StringBuilder();
			final StringBuilder taggedActionInQuery = new StringBuilder();
			final StringBuilder hostelIdInQuery = new StringBuilder();
			final StringBuilder countQuery = new StringBuilder();
			query.append(SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			countQuery.append(COUNT_RESULT_SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE);
				countQuery.append(WHERE);
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append("where " + STUDENT_SEARCH_CONDITION);
						countQuery.append("where " + STUDENT_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(STUDENT_SEARCH_CONDITION);
						countQuery.append(AND).append(STUDENT_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}
				query.append(AND);
				countQuery.append(AND);
			}

			if (includeUserStatus != null) {
				if (includeUserStatus.equals("USER_PAGE")) {
					query.append(NOT_IN_USER_CLAUSE).append(AND);
				}
			}

			query.append(WHERE_CLAUSE_FOR_STUDENT_SEARCH);

			args.add(instituteId);
			args.add(academicSessionId);
			statusInQuery.append("(");

			boolean firstStatus = true;
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
				if (firstStatus) {
					statusInQuery.append("?");
					firstStatus = false;
					continue;
				}
				statusInQuery.append(", ?");
			}
			statusInQuery.append(")");

			if(!CollectionUtils.isEmpty(standardIds)) {
				standardInQuery.append(AND);
				standardInQuery.append(STANDARD_CLAUSE);
				standardInQuery.append(" (");
				boolean firstStandard = true;
				for (final UUID standardId : standardIds) {
					if(standardId == null) {
						continue;
					}
					args.add(standardId.toString());
					if (firstStandard) {
						standardInQuery.append("?");
						firstStandard = false;
						continue;
					}
					standardInQuery.append(", ?");
				}
				standardInQuery.append(") ");
			}

			if(!CollectionUtils.isEmpty(sectionIdSet)) {
				sectionInQuery.append(SECTION_CLAUSE);
				sectionInQuery.append(" (");
				boolean firstSection = true;
				for (final Integer sectionId : sectionIdSet) {
					if(sectionId == null || sectionId <= 0) {
						continue;
					}
					args.add(sectionId);
					if (firstSection) {
						sectionInQuery.append("?");
						firstSection = false;
						continue;
					}
					sectionInQuery.append(", ?");
				}
				sectionInQuery.append(") ");
			}
			if (isHosteller != null && isHosteller) {
				query.append(AND).append(HOSTELLER_CONDITION);
				countQuery.append(HOSTELLER_CONDITION).append(AND);
				args.add(isHosteller);
			}

			if(!CollectionUtils.isEmpty(taggedActionsSet)) {
				boolean firstTag = true;
				for (final TaggedActions taggedActions : taggedActionsSet) {
					args.add(taggedActions.name());
					if(firstTag) {
						taggedActionInQuery.append(" and ( ");
						taggedActionInQuery.append(TAGGED_ACTION_CLAUSE);
						firstTag = false;
						continue;
					}
					taggedActionInQuery.append(" or ");
					taggedActionInQuery.append(TAGGED_ACTION_CLAUSE);
				}
				taggedActionInQuery.append(") ");
			}

			if(!CollectionUtils.isEmpty(hostelIdSet)) {
				hostelIdInQuery.append(HOSTEL_CLAUSE);
				hostelIdInQuery.append(" (");
				boolean first = true;
				for (final UUID hostelId : hostelIdSet) {
					if(hostelId == null) {
						continue;
					}
					args.add(hostelId.toString());
					if (first) {
						hostelIdInQuery.append("?");
						first = false;
						continue;
					}
					hostelIdInQuery.append(", ?");
				}
				hostelIdInQuery.append(") ");
			}

			query.append(ORDER_BY_LIMIT_OFFSET_CLAUSE);
			countQuery.append(COUNT_WHERE_CLAUSE_FOR_STUDENT_SEARCH);
			final int totalResultCount = jdbcTemplate
					.queryForObject(String.format(countQuery.toString(), statusInQuery, standardInQuery,
							sectionInQuery, taggedActionInQuery, hostelIdInQuery), Integer.class, args.toArray());
			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);
			args.add(limit);
			args.add(offset);
			final List<Student> studentResponseList = getStudentWithHouseDetails(instituteId, jdbcTemplate.query(
					String.format(query.toString(), statusInQuery, standardInQuery,
							sectionInQuery, taggedActionInQuery, hostelIdInQuery), args.toArray(), STUDENT_ROW_MAPPER));
			final SearchResultWithPagination<Student> resultWithPagination = new SearchResultWithPagination<>(
					paginationInfo, studentResponseList);

			return resultWithPagination;
		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public List<StudentLiteRowV2> liteV2SearchStudentsInAcademicSession(int instituteId, int academicSessionId, String searchText, List<StudentStatus> studentStatusList) {
		try {
			if (StringUtils.isBlank(searchText)) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Give at least one character to search"));
			}

			final StringBuilder query = new StringBuilder();
			final StringBuilder statusInQuery = new StringBuilder();
			query.append(LITE_SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);


			statusInQuery.append("(");

			String delimiter = "";
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
				statusInQuery.append(delimiter).append(" ?");
				delimiter = ",";
			}
			statusInQuery.append(")");


			String finalSearchText = searchText.toLowerCase().trim();
			args.add("%" + finalSearchText + "%");

			String finalQuery = String.format(query.toString(), statusInQuery);
			return jdbcTemplate.query(finalQuery, args.toArray(), STUDENT_LITE_V2_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public List<StudentSearchEntry> liteV2SearchStudentsInAcademicSession(int instituteId, int academicSessionId, String searchText, Set<StudentStatus> studentStatusSet) {
		try {
			List<StudentSearchEntry> result = new ArrayList<>();
			byte[] serializedData = studentSearchCache.get(new InstituteSessionCacheKey(instituteId, academicSessionId));
			logger.info("serializedData cache size for key {}-{} is {} bytes",instituteId, academicSessionId, serializedData.length);

			List<StudentSearchCacheData> allStudentList = studentSearchCacheGZIPSerializer.deserialize(serializedData);
			final String[] keywords = searchText.trim().toLowerCase().split(" ");
			for (StudentSearchCacheData entry : allStudentList) {
				if (!studentStatusSet.contains(entry.getStatus())){
					continue;
				}
				if(studentEntryPresent(entry.getText().trim().toLowerCase(), keywords)){
					result.add(new StudentSearchEntry(entry.getId(), entry.getText()));
				}
			}
			return result;
		} catch (Exception e) {
			logger.error("Error while getting student entry from cache with deserialize instituteId {}, academicSessionId {}", instituteId, academicSessionId, e);
			return null;
		}
	}

	public static boolean studentEntryPresent(String textToBeSearched, String[] searchTexts) {
		boolean validSearchTexts = false;
		for (String searchText : searchTexts) {
			searchText = searchText.trim();
			if (StringUtils.isBlank(searchText)) {
				continue;
			}
			validSearchTexts = true;
			if (!textToBeSearched.contains(searchText)) {
				return false;
			}
		}
		return validSearchTexts;
	}

	private List<StudentLiteRowV2> fetchAllStudentSearchDataUncached(int instituteId, int academicSessionId) {
		try {
			final StringBuilder query = new StringBuilder();
			query.append(ALL_STUDENT_LITE_SEARCH_DATA_IN_SESSION);
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);

			return jdbcTemplate.query(ALL_STUDENT_LITE_SEARCH_DATA_IN_SESSION, args.toArray(), STUDENT_LITE_V2_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public List<StandardSectionGenderCount> getInstituteStandardDetailsWithStudentAndGenderCount(int instituteId, int academicSessionId, Set<UUID> standardIdsSet, Set<Integer> sectionsSet, Set<StudentStatus> studentStatusList) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(academicSessionId);
			final StringBuilder studentStatusQuery = new StringBuilder();
			String delimiter = "";
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
				studentStatusQuery.append(delimiter).append("?");
				delimiter = " , ";

			}
			args.add(academicSessionId);
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
			}
			args.add(instituteId);

			final StringBuilder standardIdsQuery = new StringBuilder();
			delimiter = "";
			if(!CollectionUtils.isEmpty(standardIdsSet)){
				standardIdsQuery.append(MULTIPLE_STANDARD_ID_CLAUSE);
				for (UUID standard : standardIdsSet) {
					args.add(standard.toString());
					standardIdsQuery.append(delimiter).append("?");
					delimiter = " , ";
				}
				standardIdsQuery.append(")");
			}

			final StringBuilder sectionIdsQuery = new StringBuilder();
			delimiter = "";
			if (!CollectionUtils.isEmpty(sectionsSet)) {
				sectionIdsQuery.append(MULTIPLE_SECTION_ID_CLAUSE);
				for (Integer section : sectionsSet) {
					args.add(section);
					sectionIdsQuery.append(delimiter).append("?");
					delimiter = ", ";
				}
				sectionIdsQuery.append("))");
			}

			return StandardSectionGenderCountRowMapper.getStandardSectionGenderCountList(jdbcTemplate.query(String.format(GET_STANDARD_DETAILS_WITH_STUDENT_COUNT_AND_GENDER_COUNT, studentStatusQuery, studentStatusQuery, standardIdsQuery, sectionIdsQuery),
					args.toArray(), STANDARD_SECTION_GENDER_COUNT_ROW_MAPPER));
		} catch (final DataAccessException dataAccessException) {
			logger.warn("No meta data exists for institute {}, session {}", instituteId, academicSessionId);
		} catch (final Exception e) {
			logger.error("Error occured while getting standard details for institute {}, session {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public SearchResultWithPagination<Student> searchRelievedStudentsInAcademicSession(String searchText, int instituteId,
																					   int academicSessionId, int offset, int limit,
																					   String includeUserStatus, Set<UUID> standardIds) {
		return searchRelievedStudentsInAcademicSession(searchText, instituteId, academicSessionId, offset, limit, includeUserStatus, standardIds, null, null);
	}

	public SearchResultWithPagination<Student> searchRelievedStudentsInAcademicSession(String searchText, int instituteId,
					   int academicSessionId, int offset, int limit,
					   String includeUserStatus, Set<UUID> standardIds, Set<Integer> sectionIdSet, Boolean tcGenerated) {
		try {
			final StringBuilder query = new StringBuilder();
			final StringBuilder standardInQuery = new StringBuilder();
			final StringBuilder sectionInQuery = new StringBuilder();
			final StringBuilder countQuery = new StringBuilder();
			final StringBuilder tcDetailsQuery = new StringBuilder();

			query.append(SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			countQuery.append(COUNT_RESULT_SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();
			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE);
				countQuery.append(WHERE);
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append("where " + STUDENT_SEARCH_CONDITION);
						countQuery.append("where " + STUDENT_SEARCH_CONDITION);
						first = false;
					} else {
						query.append(AND).append(STUDENT_SEARCH_CONDITION);
						countQuery.append(AND).append(STUDENT_SEARCH_CONDITION);
					}
					args.add("%" + keyword + "%");
				}
				query.append(AND);
				countQuery.append(AND);
			}

			if (includeUserStatus != null) {
				if (includeUserStatus.equals("USER_PAGE")) {
					query.append(NOT_IN_USER_CLAUSE).append(AND);
				}
			}

			query.append(WHERE_CLAUSE_FOR_RELIVED_STUDENT_SEARCH);

			args.add(instituteId);
			args.add(academicSessionId);

			if(!CollectionUtils.isEmpty(standardIds)) {
				standardInQuery.append(AND);
				standardInQuery.append(STANDARD_CLAUSE);
				standardInQuery.append(" (");
				boolean firstStandard = true;
				for (final UUID standardId : standardIds) {
					if(standardId == null) {
						continue;
					}
					args.add(standardId.toString());
					if (firstStandard) {
						standardInQuery.append("?");
						firstStandard = false;
						continue;
					}
					standardInQuery.append(", ?");
				}
				standardInQuery.append(") ");
			}

			if(!CollectionUtils.isEmpty(sectionIdSet)) {
				sectionInQuery.append(SECTION_CLAUSE);
				sectionInQuery.append(" (");
				boolean firstSection = true;
				for (final Integer sectionId : sectionIdSet) {
					if(sectionId == null || sectionId <= 0) {
						continue;
					}
					args.add(sectionId);
					if (firstSection) {
						sectionInQuery.append("?");
						firstSection = false;
						continue;
					}
					sectionInQuery.append(", ?");
				}
				sectionInQuery.append(") ");
			}

			if(tcGenerated != null){
				tcDetailsQuery.append(tcGenerated ?" AND tc_details IS NOT NULL": " AND tc_details IS NULL");
			}

			query.append(ORDER_BY_LIMIT_OFFSET_CLAUSE);
			countQuery.append(WHERE_CLAUSE_FOR_RELIVED_STUDENT_SEARCH);

			final int totalResultCount = jdbcTemplate
					.queryForObject(String.format(countQuery.toString(), standardInQuery, sectionInQuery, tcDetailsQuery), Integer.class, args.toArray());

			final PaginationInfo paginationInfo = new PaginationInfo(totalResultCount, limit, offset);
			args.add(limit);
			args.add(offset);

			final List<Student> studentResponseList = getStudentWithHouseDetails(instituteId, jdbcTemplate.query(
					String.format(query.toString(), standardInQuery, sectionInQuery, tcDetailsQuery), args.toArray(), STUDENT_ROW_MAPPER));

			final SearchResultWithPagination<Student> resultWithPagination = new SearchResultWithPagination<>(
					paginationInfo, studentResponseList);

			return resultWithPagination;
		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public List<Student> searchStudentsWithoutAcademicSesison(String searchText, int instituteId,
															  StudentStatus studentStatus) {
		try {
			final List<Object> statusQueryArgs = new ArrayList<>();
			final StringBuilder statusInQuery = new StringBuilder();
			statusInQuery.append("(");
			if (studentStatus == null) {
				statusQueryArgs.add(StudentStatus.ENROLLED.name());
				statusQueryArgs.add(StudentStatus.RELIEVED.name());
				statusInQuery.append("?, ?");
			} else {
				statusQueryArgs.add(studentStatus.name());
				statusInQuery.append("?");
			}
			statusInQuery.append(")");

			final List<Object> args = new ArrayList<>();
			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_STUDENTS_WITHOUT_ACADEMIC_SESSION);

			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE).append(String.format(WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION,
						statusInQuery));
			} else {
				searchText = searchText.trim();
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean whereClauseAdded = false;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if(!whereClauseAdded){
						query.append(WHERE).append(STUDENT_SEARCH_CONDITION_WITHOUT_SESSION);
						whereClauseAdded = true;
					}else{
						query.append(AND).append(STUDENT_SEARCH_CONDITION_WITHOUT_SESSION);
					}
					args.add("%" + keyword + "%");
				}
				if(!whereClauseAdded){
					query.append(WHERE);
				}else{
					query.append(AND);
				}

				query.append(String.format(WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION,
						statusInQuery));
			}
			args.add(instituteId);
			args.addAll(statusQueryArgs);
			return getStudentWithHouseDetails(instituteId, StudentRowMapper.getStudentList(
					jdbcTemplate.query(query.toString(), args.toArray(), STUDENT_WITHOUT_SESSION_ROW_MAPPER)));

		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}", instituteId, e);
		}
		return null;
	}

	public List<Student> searchStudentsWithoutAcademicSesison(String searchText, Set<Integer> instituteIds,
															  Set<StudentStatus> studentStatusSet) {
		try {
			final List<Object> statusQueryArgs = new ArrayList<>();
			final StringBuilder statusInQuery = new StringBuilder();
			statusInQuery.append("(");
			String delimiter = "";
			for (StudentStatus studentStatus : studentStatusSet) {
				statusQueryArgs.add(studentStatus.name());
				statusInQuery.append(delimiter).append(" ?");
				delimiter = ",";
			}

			statusInQuery.append(")");

			final List<Object> institueArgs = new ArrayList<>();
			final StringBuilder institueInQuery = new StringBuilder();
			institueInQuery.append("(");
			String instituteDelimiter = "";
			for (Integer instituteId : instituteIds) {
				institueArgs.add(instituteId);
				institueInQuery.append(instituteDelimiter).append(" ?");
				instituteDelimiter = ",";
			}

			institueInQuery.append(")");

			final List<Object> args = new ArrayList<>();
			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_STUDENTS_WITHOUT_ACADEMIC_SESSION);

			if (StringUtils.isBlank(searchText)) {
				query.append(WHERE)
						.append(String.format(
								WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION_MULTIPLE_INSTITUTES,
								institueInQuery.toString(), statusInQuery.toString()));
			} else {
				final String[] keywords = searchText.toLowerCase().split(" ");
				boolean first = true;
				for (String keyword : keywords) {
					keyword = keyword.trim();
					if (StringUtils.isBlank(keyword)) {
						continue;
					}
					if (first) {
						query.append("where " + STUDENT_SEARCH_CONDITION_WITHOUT_SESSION);
						first = false;
					} else {
						query.append(AND).append(STUDENT_SEARCH_CONDITION_WITHOUT_SESSION);
					}
					args.add("%" + keyword + "%");
				}
				query.append(AND)
						.append(String.format(
								WHERE_CLAUSE_FOR_STUDENT_SEARCH_WITHOUT_ACADEMIC_SESSION_MULTIPLE_INSTITUTES,
								institueInQuery.toString(), statusInQuery.toString()));
			}
			args.addAll(institueArgs);
			args.addAll(statusQueryArgs);
			return StudentRowMapper.getStudentList(
					jdbcTemplate.query(query.toString(), args.toArray(), STUDENT_WITHOUT_SESSION_ROW_MAPPER));

		} catch (final Exception e) {
			logger.error("error while searching students for instituteIds {}", instituteIds, e);
		}
		return null;
	}

	public List<Student> getStudentsWithWalletDetails(int instituteId, int academicSessionId,
													  Set<UUID> standardIdSet, Set<StudentStatus> studentStatusSet) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);

//			String standardClause = "";
			final StringBuilder standardInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(standardIdSet)) {
				standardInQuery.append(WHERE_STUDENT_STANDARD_IN_CLAUSE);
				standardInQuery.append(" (");
				String delimiter = "";
				for (final UUID standardId : standardIdSet) {
					args.add(standardId.toString());
					standardInQuery.append(delimiter).append("?");
					delimiter = ",";
				}
				standardInQuery.append(") ");
			}

//			String statusClause = "";
			final StringBuilder statusInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(studentStatusSet)) {
				statusInQuery.append(WHERE_STUDENT_STATUS_IN_CLAUSE);
				statusInQuery.append(" (");
				String statusDelimiter = "";
				for (final StudentStatus studentStatus : studentStatusSet) {
					args.add(studentStatus.name());
					statusInQuery.append(statusDelimiter).append("?");
					statusDelimiter = ",";
				}
				statusInQuery.append(") ");
			}
			args.add(instituteId);

			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_STUDENTS_WITH_WALLET, standardInQuery.toString(),
					statusInQuery.toString()), args.toArray(), STUDENT_WITH_WALLET_DETAILS_ROW_MAPPER));

		} catch (final Exception e) {
			logger.error("error while searching students for instituteId {}", instituteId, e);
		}
		return null;
	}

	public List<Student> getClassStudents(int instituteId, int academicSessionId, Set<UUID> standardIds) {
		return getClassStudents(instituteId, academicSessionId, standardIds, null, new HashSet<>(Arrays.asList(StudentStatus.ENROLLED)));
	}

	public List<Student> getClassStudents(int instituteId, int academicSessionId, Set<UUID> standardIds, Set<StudentStatus> statusSet) {
		return getClassStudents(instituteId, academicSessionId, standardIds, null, statusSet);
	}
	public List<Student> getClassStudents(int instituteId, int academicSessionId, Set<UUID> standardIds,
										  Set<Integer> sectionIds, Set<StudentStatus> statusSet) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);

			final StringBuilder standardInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(standardIds)) {
				standardInQuery.append(AND);
				standardInQuery.append(STANDARD_CLAUSE);
				standardInQuery.append(" (");
				String standardDelimiter = "";
				for (final UUID standardId : standardIds) {
					args.add(standardId.toString());
					standardInQuery.append(standardDelimiter).append("?");
					standardDelimiter = ",";
				}
				standardInQuery.append(")");
			}

			String sectionClause = "";
			final StringBuilder sectionInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(sectionIds)) {
				sectionClause = SECTION_CLAUSE;
				sectionInQuery.append("(");
				String sectionDelimiter = "";
				for (final int sectionId : sectionIds) {
					args.add(sectionId);
					sectionInQuery.append(sectionDelimiter).append("?");
					sectionDelimiter = ",";
				}
				sectionInQuery.append(")");
			}
			args.add(instituteId);

			final StringBuilder statusInQuery = new StringBuilder();
			if(!CollectionUtils.isEmpty(statusSet)) {
				statusInQuery.append(" and student_academic_session_details.session_status in ( ");
				String delimiterStatus = "";
				for (final StudentStatus status : statusSet) {
					args.add(status.name());
					statusInQuery.append(delimiterStatus).append("?");
					delimiterStatus = ",";
				}
				statusInQuery.append(")");
			}

			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(String.format(GET_CLASS_STUDENTS_IN_ACADEMIC_SESSION, standardInQuery,
					sectionClause, sectionInQuery, statusInQuery), args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("error while fetching results from table for instituteId {}, academicSessionId {}",
					instituteId, academicSessionId, e);
		}
		return null;
	}

	private String getCounterNumber(String counterStaticValue, CounterType counterType, int instituteId,
									boolean isCounterEnable) {
		if (!isCounterEnable) {
			return counterStaticValue;
		}

		final CounterData counterData = instituteDao.getCounter(instituteId, counterType, true);
		if (counterData == null) {
			logger.error("{} counter is not present for institute {} ", counterType.name(), instituteId);
			throw new EmbrateRunTimeException(
					counterType.name() + " counter is not present for institute " + instituteId);
		}
		return counterData.getFullCounterValue();
	}

	public boolean admitStudent(int instituteId, UUID studentId, String admissionNumberInput, Integer admissionDate,
								boolean admissionCounter, UUID houseId, UUID siblingGroupId, List<StudentSessionStatusDetails> studentSessionStatusDetailsList,
								StudentStatus studentStatus) {
		final Boolean success = transactionTemplate.execute(new TransactionCallback<Boolean>() {

			@Override
			public Boolean doInTransaction(TransactionStatus status) {
				return admitStudentNonAtomic(instituteId, studentId, admissionNumberInput, admissionDate,
						admissionCounter, houseId, siblingGroupId, studentSessionStatusDetailsList, studentStatus);
			}
		});
		return success;
	}

	public boolean admitStudentNonAtomic(int instituteId, UUID studentId, String admissionNumberInput, Integer admissionDate,
								boolean admissionCounter, UUID houseId, UUID siblingGroupId, List<StudentSessionStatusDetails> studentSessionStatusDetailsList,
								StudentStatus studentStatus) {
		try {
				final CounterType counterType = CounterType.ADMISSION_NUMBER;
				String admissionNumber = getCounterNumber(admissionNumberInput, counterType, instituteId,
						admissionCounter);
				Timestamp admissionDateInt = (admissionDate != null) && (admissionDate > 0)
						? new Timestamp(admissionDate * 1000l) : null;
				logger.info("admissionNumber {} for admitting studentId {}, instituteId {}, counterEnabled {}",
						admissionNumber, studentId, instituteId, admissionCounter);
				final Object[] args = { studentStatus.name(), admissionNumber, admissionDateInt, houseId == null ? null : houseId.toString(),
						siblingGroupId == null ? null : siblingGroupId.toString(), instituteId, studentId.toString() };
				final int row = jdbcTemplate.update(ADMIT_STUDENT, args);

				if (row != 1) {
					logger.error("Unable to update admissionNumber {} for admitting studentId {}, instituteId {}",
							admissionNumber, studentId, instituteId);
					throw new EmbrateRunTimeException("Unable to admit student");
				}

				if(!enrollStudent(studentId, instituteId, StudentStatus.ENROLLED, studentSessionStatusDetailsList)) {
					logger.error(
							"Unable to update student status for admissionNumber {} for admitting studentId {}, instituteId {}",
							admissionNumber, studentId, instituteId);
					throw new EmbrateRunTimeException(
							"Unable to update student status for institute " + instituteId);
				}

				logger.info("Student admitted with admissionNumber {}, studentId {}, instituteId {}",
						admissionNumber, studentId, instituteId);

				if (admissionCounter && !instituteDao.incrementCounter(instituteId, counterType)) {
					logger.error(
							"Unable to update counter for admissionNumber {} for admitting studentId {}, instituteId {}",
							admissionNumber, studentId, instituteId);
					throw new EmbrateRunTimeException(
							"Unable to increment admission counter for institute " + instituteId);
				}
				return true;
		} catch (final DataAccessException e) {
			logger.error(
					"Execption while admitting for admissionNumber {} , studentId {}, instituteId {}, admissionCounter {}",
					admissionNumberInput, studentId, instituteId, admissionCounter, e);
			ExceptionHandling.HandleException(e, STUDENT, "admission number");

		} catch (final Exception e) {
			logger.error(
					"Exception while admitting for admissionNumber {} , studentId {}, instituteId {}, admissionCounter {}",
					admissionNumberInput, studentId, instituteId, admissionCounter, e);
		} finally {
			for(StudentSessionStatusDetails studentSessionStatusDetails: studentSessionStatusDetailsList){
				invalidateStudentSearchCache(instituteId, studentSessionStatusDetails.getAcademicSessionId());
			}
		}
		return false;
	}

	public List<Student> searchStudentsInAcademicSesisonWithFilter(int instituteId, Integer academicSessionId,
																   Set<StudentStatus> studentStatusSet, FilterationCriteria filterationCriteria) {
		try {

			final StringBuilder query = new StringBuilder();
			query.append(SEARCH_STUDENTS_IN_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();
			query.append(WHERE).append(WHERE_INSTITUTE_CLAUSE);
			args.add(instituteId);


			if(!CollectionUtils.isEmpty(studentStatusSet)) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				for (final StudentStatus studentStatus : studentStatusSet) {
					args.add(studentStatus.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(AND).append(String.format(STUDENT_STATUS_CONDITION, inQuery.toString()));
			}

			if (academicSessionId != null && academicSessionId > 0) {
				query.append(AND).append(WHERE_ACADEMIC_SESSION_CLAUSE);
				args.add(academicSessionId);
			}

//			//Filters the students who were admitted in the given session
//			if (filterationCriteria.getNewAdmissionsOnly() != null && filterationCriteria.getNewAdmissionsOnly().booleanValue()) {
//				args.add(academicSessionId);
//				query.append(ADMISSION_SESSION_CONDITION);
//			}

			//Filters the students on the basic of flag
			if (filterationCriteria.getNewAdmissionsOnly() != null) {
				args.add(filterationCriteria.getNewAdmissionsOnly());
				query.append(NEW_ADMISSION_CONDITION);
			}

			// rte logic
			if (filterationCriteria.getRte() != null) {
				args.add(filterationCriteria.getRte());
				query.append(RTE_CONDITION);
			}

			// category logic
			if (filterationCriteria.getCategory() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<UserCategory> categoryArray = filterationCriteria.getCategory();
				for (final UserCategory category : categoryArray) {
					args.add(category.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(CATEGORY_CONDITION, inQuery.toString()));
			}

			// gender logic
			if (filterationCriteria.getGender() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<Gender> genderArray = filterationCriteria.getGender();
				for (final Gender gender : genderArray) {
					args.add(gender.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(GENDER_CONDITION, inQuery.toString()));
			}

			// religion logic
			if (filterationCriteria.getReligion() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<String> religionArray = filterationCriteria.getReligion();
				for (final String religion : religionArray) {
					args.add(religion);
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(RELIGION_CONDITION, inQuery.toString()));
			}

			// areaType logic
			if (filterationCriteria.getAreaType() != null) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<AreaType> areaTypeArray = filterationCriteria.getAreaType();
				for (final AreaType areaType : areaTypeArray) {
					args.add(areaType.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(AREA_TYPE_CONDITION, inQuery.toString()));
			}

			// speciallyAbled logic
			if (filterationCriteria.getSpeciallyAbled() != null) {
				args.add(filterationCriteria.getSpeciallyAbled());
				query.append(SPECIALLY_ABLED_CONDITION);
			}

			// bpl logic
			if (filterationCriteria.getBpl() != null) {
				args.add(filterationCriteria.getBpl());
				query.append(BPL_CONDITION);
			}

			// state logic
			if (filterationCriteria.getState() != null) {
				final StringBuilder presentInQuery = new StringBuilder();
				presentInQuery.append("(");
				boolean first = true;
				List<String> stateArray = filterationCriteria.getState();
				for (final String state : stateArray) {
					args.add(state);
					if (first) {
						presentInQuery.append("?");
						first = false;
						continue;
					}
					presentInQuery.append(", ?");
				}
				presentInQuery.append(")");

				final StringBuilder permanenetInQuery = new StringBuilder();
				permanenetInQuery.append("(");
				first = true;
				stateArray = filterationCriteria.getState();
				for (final String state : stateArray) {
					args.add(state);
					if (first) {
						permanenetInQuery.append("?");
						first = false;
						continue;
					}
					permanenetInQuery.append(", ?");
				}
				permanenetInQuery.append(")");

				query.append(String.format(STATE_CONDITION, presentInQuery.toString(), permanenetInQuery.toString()));
			}

			// house logic
			if (!CollectionUtils.isEmpty(filterationCriteria.getInstituteHouseId())) {

				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				List<UUID> instituteHouseIdArray = filterationCriteria.getInstituteHouseId();
				for (final UUID houseId : instituteHouseIdArray) {
					if(houseId == null) {
						continue;
					}
					args.add(houseId.toString());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}

				inQuery.append(")");
				query.append(String.format(INSTITUTE_HOUSE_CONDITION, inQuery.toString()));
			}

			// admission date logic
			if (filterationCriteria.getStartDate() != null && filterationCriteria.getStartDate() > 0) {
				Long dateStart = DateUtils.getDayStart(filterationCriteria.getStartDate() * 1000l, DateUtils.DEFAULT_TIMEZONE);
				args.add(new Timestamp(dateStart));
				query.append(START_DATE_CLAUSE);
			}

			if (filterationCriteria.getEndDate() != null && filterationCriteria.getEndDate() > 0) {
				Long dateEnd = DateUtils.getDayEnd(filterationCriteria.getEndDate() * 1000l, DateUtils.DEFAULT_TIMEZONE);
				args.add(new Timestamp(dateEnd));
				query.append(END_DATE_CLAUSE);
			}


			query.append(ORDER_BY_CLAUSE);

			return getStudentWithHouseDetails(instituteId, jdbcTemplate.query(query.toString(), args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("error while fetching students for instituteId {}, academicSessionId {}", instituteId,
					academicSessionId, e);
		}
		return null;
	}

	public List<String> getReligionDetails(int instituteId) {
		try {
			final Object[] args = { instituteId };
			return jdbcTemplate.queryForList(GET_RELIGION_BY_INSTITUTE, args, String.class);
		} catch (final Exception e) {
			logger.error("Exception while getting religions for instituteId {}", instituteId, e);
		}

		return null;
	}

	public StudentCastClassWiseDetails getCastWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { instituteId, academicSessionId };
		try {
			return StudentCastReportRowMapper.getStudentCastReport(
					jdbcTemplate.query(GET_CAST_WISE_REPORT, args, STUDENT_CAST_REPORT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public StudentReligionClassWiseDetails getReligionWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { instituteId, academicSessionId };
		try {
			return StudentReligionReportRowMapper.getStudentReligionReport(
					jdbcTemplate.query(GET_RELIGION_WISE_REPORT, args, STUDENT_RELIGION_REPORT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public StudentHouseSummaryDetails getStudentHouseSummaryReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { academicSessionId,instituteId, academicSessionId };
		try {
			return StudentHouseSummaryReportRowMapper.getStudentHouseSummaryReport(
					jdbcTemplate.query(GET_HOUSE_SUMMARY_REPORT, args, STUDENT_HOUSE_SUMMARY_REPORT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting student house summary report for institute {}", instituteId, e);
		}
		return null;
	}

	public boolean updateStudentAcademicSessionDetails(StudentAcademicSessionPayload studentAcademicSessionPayload) {

		boolean isSectionThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.SECTION);
		boolean isRollNumberThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.ROLL_NUMBER);
		boolean isHeightThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.HEIGHT);
		boolean isWeightThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.WEIGHT);
		boolean isBoardRegistrationNumberThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.BOARD_REGISTRATION_NUMBER);
		boolean isMediumThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.MEDIUM);
		boolean isNewAdmissionThere = studentAcademicSessionPayload.getStudentAcademisSessionParametersList()
				.contains(StudentAcademisSessionParameters.IS_NEW_ADMISSION);

		StringBuilder query = new StringBuilder();
		query.append(UPDATE_STUDENT_SESSION_DETAILS);
		boolean first = true;
		if(isSectionThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_SECTION_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_SECTION_CLAUSE);
			}
			first = false;
		}
		if(isRollNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_ROLL_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_ROLL_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isHeightThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_HEIGHT_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_HEIGHT_CLAUSE);
			}
			first = false;
		}
		if(isWeightThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_WEIGHT_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_WEIGHT_CLAUSE);
			}
			first = false;
		}
		if(isBoardRegistrationNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_BOARD_REGISTRATION_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_BOARD_REGISTRATION_CLAUSE);
			}
			first = false;
		}
		if(isMediumThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_MEDIUM);
			} else {
				query.append(COMMA);
				query.append(UPDATE_MEDIUM);
			}
			first = false;
		}
		if(isNewAdmissionThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_IS_NEW_ADMISSION);
			} else {
				query.append(COMMA);
				query.append(UPDATE_IS_NEW_ADMISSION);
			}
			first = false;
		}
		query.append(UPDATE_STUDENT_SESSION_DETAILS_WHERE_CONDITION);

		if(studentAcademicSessionPayload.getStandardId() != null) {
			query.append(" and standard_id = ? ");
		}

		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(StudentAcademicDetails studentAcademicDetails : studentAcademicSessionPayload.getStudentAcademicDetailsList()) {
			final List<Object> args = new ArrayList<>();

			if(isSectionThere) {
				args.add(studentAcademicDetails.getSectionId() == null ||
						studentAcademicDetails.getSectionId() == 0 ? null : studentAcademicDetails.getSectionId());
			}

			if(isRollNumberThere) {
				args.add(studentAcademicDetails.getRollNumber());
			}

			if(isHeightThere) {
				args.add(studentAcademicDetails.getHeight());
			}

			if(isWeightThere) {
				args.add(studentAcademicDetails.getWeight());
			}

			if(isBoardRegistrationNumberThere) {
				args.add(studentAcademicDetails.getBoardRegistrationNumber());
			}

			if(isMediumThere) {
				args.add(studentAcademicDetails.getMedium() == null ? null : studentAcademicDetails.getMedium().name());
			}

			if(isNewAdmissionThere) {
				args.add(studentAcademicDetails.isNewAdmission());
			}

			args.add(studentAcademicDetails.getStudentId().toString());
			args.add(studentAcademicSessionPayload.getAcademicSessionId());
			if(studentAcademicSessionPayload.getStandardId() != null) {
				args.add(studentAcademicSessionPayload.getStandardId().toString());
			}
			count++;
			batchInsertArgs.add(args.toArray());
		}

		try {
			final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Unable to add notice entity mapping for institute {}", studentAcademicSessionPayload.getInstituteId(), e);
		} finally {
			invalidateStudentSearchCache(studentAcademicSessionPayload.getInstituteId(), studentAcademicSessionPayload.getAcademicSessionId());
		}

		return false;
	}

	public boolean updateStudentAcademicSessionDetails(int instituteId, int academicSessionId, UUID standardId, StudentManagementUpdateFieldPayload payload, boolean standardCheck) {
		StringBuilder query = new StringBuilder();
		query.append(UPDATE_STUDENT_SESSION_DETAILS);
		boolean first = true;

		for(StudentManagementField reqField : payload.getRequiredFields()){
			String delimiter = first ? SET : COMMA;
			query.append(delimiter).append(" " + reqField.getFieldValueManager().getDBColumnName() + " = ? ");
			first = false;
		}

		query.append(UPDATE_STUDENT_SESSION_DETAILS_WHERE_CONDITION);
		if(standardCheck) {
			query.append(" and standard_id = ? ");
		}

		final List<Object[]> batchInsertArgs = new ArrayList<>();
		int count = 0;
		for(StudentManagementUpdateFieldStudentData studentData : payload.getStudentDataList()) {
			final List<Object> args = new ArrayList<>();
			for(StudentManagementUpdateFieldValue fieldValue : studentData.getFieldValueList()){
				args.add(fieldValue.getField().getFieldValueManager().getDBColumnValue(fieldValue.getValue()));
			}
			args.add(studentData.getStudentId().toString());
			args.add(academicSessionId);
			if(standardCheck) {
				args.add(standardId.toString());
			}
			batchInsertArgs.add(args.toArray());
		}
		try {
			final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);
			if (rows.length != batchInsertArgs.size()) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Unable to update student session data for institute {}, session {}, standard {}, payload {}", instituteId, academicSessionId, standardId, payload, e);
		} finally {
			invalidateStudentSearchCache(instituteId, academicSessionId);
		}

		return false;
	}


	public Map<UUID, Student> getStudentsWithAllAcademicSessionDetailWithFilter(int instituteId,
				Boolean rte, Set<UserCategory> categoryList, Set<Gender> genderList, Set<AreaType> areaTypeList,
				Boolean speciallyAbled, Boolean bpl) {
		try {
			final StringBuilder query = new StringBuilder();
			query.append(STUDENTS_IN_ALL_ACADEMIC_SESSION);
			final List<Object> args = new ArrayList<>();

			query.append(AND).append(WHERE_INSTITUTE_CLAUSE);
			args.add(instituteId);

			// rte logic
			if(rte != null) {
				args.add(rte);
				query.append(RTE_CONDITION);
			}

			// category logic
			if(!CollectionUtils.isEmpty(categoryList)) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				for (final UserCategory category : categoryList) {
					args.add(category.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(CATEGORY_CONDITION, inQuery.toString()));
			}

			// gender logic
			if(!CollectionUtils.isEmpty(genderList)) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				for (final Gender gender : genderList) {
					args.add(gender.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(GENDER_CONDITION, inQuery.toString()));
			}

			// areaType logic
			if(!CollectionUtils.isEmpty(areaTypeList)) {
				final StringBuilder inQuery = new StringBuilder();
				inQuery.append("(");
				boolean first = true;
				for (final AreaType areaType : areaTypeList) {
					args.add(areaType.name());
					if (first) {
						inQuery.append("?");
						first = false;
						continue;
					}
					inQuery.append(", ?");
				}
				inQuery.append(")");
				query.append(String.format(AREA_TYPE_CONDITION, inQuery.toString()));
			}

			// speciallyAbled logic
			if (speciallyAbled != null) {
				args.add(speciallyAbled);
				query.append(SPECIALLY_ABLED_CONDITION);
			}

			// bpl logic
			if (bpl != null) {
				args.add(bpl);
				query.append(BPL_CONDITION);
			}

			query.append(ORDER_BY_STUDENT_NAME_CLAUSE);

			return StudentRowMapper.getStudentListWithLatestSession(jdbcTemplate.query(
					query.toString(), args.toArray(), STUDENT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("error while fetching students for instituteId {}", instituteId, e);
		}
		return null;
	}

	public boolean addAcademicSessionStudentDetails(int instituteId, int academicSession, List<StudentAcademicDetails> studentAcademicDetailsList, UUID standardId,
													Integer sectionId, StudentStatus studentStatus, boolean isNewAdmission) {
		try {
			final List<Object[]> batchInsertArgs = new ArrayList<>();
			int count = 0;
			for(StudentAcademicDetails studentAcademicDetails : studentAcademicDetailsList) {

				final List<Object> args = new ArrayList<>();
				args.add(academicSession);
				args.add(studentAcademicDetails.getStudentId().toString());
				args.add(standardId.toString());
				args.add(studentAcademicDetails.getSectionId());
				args.add(studentAcademicDetails.getRollNumber());//rollNumber
				args.add(studentAcademicDetails.getMedium() == null ? null : studentAcademicDetails.getMedium().name());//medium
				args.add(studentStatus.name());
				args.add(isNewAdmission);
				args.add(studentAcademicDetails.getHostelId() == null ? null : studentAcademicDetails.getHostelId().toString());
				count++;
				batchInsertArgs.add(args.toArray());
			}

			final int[] rows = jdbcTemplate.batchUpdate(ADD_ACADEMIC_SESSION_STUDENT_DETAILS, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;
		} catch (final Exception e) {
			logger.error("Error while adding academic session {} for standardId {}, sectionId {}",
					academicSession, standardId, sectionId, e);
		}finally {
			invalidateStudentSearchCache(instituteId, academicSession);
		}
		return false;

	}


	public boolean updateStudentStandardAndSection(int instituteId, int academicSessionId,
												   UUID standardId, Integer sectionId, UUID studentId) {
		try {

			final Object[] args = { standardId.toString(), sectionId == null || sectionId <= 0 ? null : sectionId,
					academicSessionId, studentId.toString() };

			return jdbcTemplate.update(UPDATE_STUDENT_STANDARD, args) >= 1;

		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, STUDENT, "admission number");
		} catch (final Exception e) {
			logger.error("Exception", e);
		} finally {
			invalidateStudentSearchCache(instituteId, academicSessionId);
		}
		return false;
	}

	public boolean editSiblings(int instituteId, StudentSiblingPayload studentSiblingPayload, boolean update,
								boolean isStudentLevelAction) {
		try {
			UUID siblingGroupId = studentSiblingPayload.getSiblingGroupId();
			if(!update) {
				if(!isStudentLevelAction) {
					siblingGroupId = UUID.randomUUID();
				}
				studentSiblingPayload.setSiblingGroupId(siblingGroupId);
				return updateSiblingGroupId(studentSiblingPayload);
			}
			studentSiblingPayload.setSiblingGroupId(siblingGroupId);
			return updateSiblings(studentSiblingPayload, isStudentLevelAction);
		} catch (final Exception e) {
			logger.error("Error while editing sibling details", e);
		}
		return false;
	}

	public boolean updateSiblingGroupId(StudentSiblingPayload studentSiblingPayload) {
		try{
			final List<Object> args = new ArrayList<Object>();
			args.add(studentSiblingPayload.getSiblingGroupId() == null ? null
					: studentSiblingPayload.getSiblingGroupId().toString());
			final StringBuilder inQuery = new StringBuilder();
			inQuery.append("(");
			boolean first = true;
			for (final UUID studentId : studentSiblingPayload.getStudentIdList()) {
				args.add(studentId.toString());
				if (first) {
					inQuery.append("?");
					first = false;
					continue;
				}
				inQuery.append(", ?");
			}
			inQuery.append(")");
			return jdbcTemplate.update(String.format(UPDATE_STUDENT_SIBLING_DETAILS, inQuery), args.toArray()) >= 0;
		} catch (final Exception e) {
			logger.error("Error while adding sibling details", e);
		}
		return false;
	}

	public boolean updateSiblings(StudentSiblingPayload studentSiblingPayload, boolean isStudentLevelAction) {
		try {
			final Boolean status = transactionTemplate.execute(new TransactionCallback<Boolean>() {

				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					/**
					 * deleting whole sibling group only when its not student level action
					 * ow just updating sibling group id on selected students
					 */
					if(!isStudentLevelAction) {
						boolean result = deleteSiblingsGroup(studentSiblingPayload.getInstituteId(),
								studentSiblingPayload.getSiblingGroupId());
						if (!result) {
							logger.error("Error while updating sibling details.");
							throw new RuntimeException("Error while updating sibling details.");
						}
					} else {
						/**
						 * setting sibling group id as null when it's a delete flow
						 */
						studentSiblingPayload.setSiblingGroupId(null);
					}
					boolean result = updateSiblingGroupId(studentSiblingPayload);
					if (!result) {
						logger.error("Error while updating sibling details.");
						throw new RuntimeException("Error while updating sibling details.");
					}
					return true;
				}
			});
			return status;
		} catch (final DatabaseException | ApplicationException ex) {
			throw ex;
		} catch (final Exception e) {
			logger.error("Unable to execute transaction", e);
		}
		return false;
	}

	public boolean updateBulkSiblingAssignment(int instituteId, Map<UUID, UUID> uuidDeviceUserIdMap) {

		try {
			List<Object[]> args = new ArrayList<>();
			for (Map.Entry<UUID, UUID> entry : uuidDeviceUserIdMap.entrySet()) {
				args.add(new Object[]{entry.getValue().toString(), entry.getKey().toString(), instituteId});
			}

			return jdbcTemplate.batchUpdate(UPDATE_SIBLING_GROUP_ID_BY_STUDENT_ID, args).length == args.size();

		} catch (final Exception e) {
			logger.error("Exception while updating device user id for instituteId {}, uuidDeviceUserIdMap {}", instituteId, uuidDeviceUserIdMap, e);
		}
		return false;
	}

	public boolean deleteSiblingsGroup(int instituteId, UUID siblingGroupId) {
		try {
			final List<Object> args = new ArrayList<Object>();
			args.add(null);
			args.add(siblingGroupId.toString());
			return jdbcTemplate.update(DELETE_STUDENT_SIBLING_DETAILS_BY_ID, args.toArray()) >= 0;
		} catch (final Exception e) {
			logger.error("Error while deleting sibling details", e);
		}
		return false;
	}

	public List<StudentSiblingDetails> getStudentSiblingDetailsList(int instituteId) {
		try {
			final List<Object> args = new ArrayList<>();
			args.add(instituteId);
			return StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetailsList(
					jdbcTemplate.query(GET_STUDENT_SIBLING_DETAILS, args.toArray(), STUDENT_SIBLING_WITHOUT_SESSION_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}
		return null;
	}

	public StudentSiblingDetails getStudentSiblingDetails(int instituteId, UUID siblingGroupId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { instituteId, siblingGroupId.toString() };
		try {
			StudentSiblingDetails studentSiblingDetails = StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetails(jdbcTemplate.query(
					GET_STUDENT_SIBLING_DETAILS + SIBLING_GROUP_ID_CLAUSE, args, STUDENT_SIBLING_WITHOUT_SESSION_ROW_MAPPER));
			if(studentSiblingDetails == null) {
				return null;
			}
			List<StudentLite> studentLiteList = getStudentLiteWithHouseDetails(instituteId, studentSiblingDetails.getStudentList());
			studentSiblingDetails.setStudentList(studentLiteList);
			return studentSiblingDetails;
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public StudentSiblingDetails getStudentSiblingDetailsByStudentId(int instituteId, UUID studentId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { instituteId, studentId.toString() };
		try {
			StudentSiblingDetails studentSiblingDetails = StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetails(jdbcTemplate.query(
					GET_STUDENT_SIBLING_DETAILS + STUDENT_ID_SIBLING_GROUP_ID_CLAUSE, args, STUDENT_SIBLING_WITHOUT_SESSION_ROW_MAPPER));
			if(studentSiblingDetails == null) {
				return null;
			}
			List<StudentLite> studentLiteList = getStudentLiteWithHouseDetails(instituteId, studentSiblingDetails.getStudentList());
			studentSiblingDetails.setStudentList(studentLiteList);
			return studentSiblingDetails;
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public List<BirthdayStudentData> getBirthdayStudents(int instituteId, int birthDate) {
		try {
			String query = GET_BIRTHDAY_STUDENT_BASIC_INFO_FOR_INSTITUTE;
			int month = DateUtils.getIntMonthOfYear(birthDate);
			int day = DateUtils.getIntDayOfMonth(birthDate);
			Object[] args = {instituteId, month, day};
			return jdbcTemplate.query(query, args, new RowMapper<BirthdayStudentData>() {
				@Override
				public BirthdayStudentData mapRow(ResultSet rs, int rowNum) throws SQLException {
					UUID studentId = UUID.fromString(rs.getString(com.lernen.cloud.dao.tier.student.mappers.StudentRowMapper.STUDENT_ID));
					StudentBasicInfo studentBasicInfo = STUDENT_BASIC_INFO_ROW_MAPPER.mapRow(rs, rowNum);
					return new BirthdayStudentData(studentId, studentBasicInfo);
				}
			});

		} catch (final Exception e) {
			logger.error("Exception while fetching students on given birthdate {} for institute {}", birthDate, instituteId, e);
		}
		return null;
	}

	public List<User> getBirthdayUsers(Integer instituteId, int birthDate) {
		try {
			String query = GET_BIRTHDAY_STUDENT_USER_INFO_FOR_INSTITUTE;
			int month = DateUtils.getIntMonthOfYear(birthDate);
			int day = DateUtils.getIntDayOfMonth(birthDate);
			List<Object> args = new ArrayList<>();
			args.add(month);
			args.add(day);
			//TODO: improve this query when fetching data for all institutes i.e institute id is null
			if(instituteId != null) {
				query += " and students.institute_id = ? ";
				args.add(instituteId);
			}
			return jdbcTemplate.query(query, args.toArray(), USER_ROW_MAPPER);

		} catch (final Exception e) {
			logger.error("Exception while fetching user on given birthdate {}", birthDate, e);
		}
		return null;
	}

	public List<StudentGenderReport> getGenderWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		final Object[] args = { instituteId, academicSessionId };
		try {
			return StudentGenderReportRowMapper.getStudentGenderReport(
					jdbcTemplate.query(GET_GENDER_WISE_REPORT, args, STUDENT_GENDER_REPORT_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public UserBiometricIdentificationData getBiometricDeviceIdentification(UUID studentId, boolean forUpdate){
		try {
			String forUpdateClause = forUpdate ? " FOR UPDATE " : "";
			return jdbcTemplate.queryForObject(String.format(GET_STUDENT_BIOMETRIC_DATA_BY_ID, forUpdateClause), new Object[]{studentId.toString()}, USER_BIOMETRIC_IDENTIFICATION_ROW_MAPPER);
		}catch (Exception e){
			logger.error("Unable to get student biometric data {}", studentId, e);
		}
		return null;
	}

	public boolean updateBiometricDeviceIdentification(UUID studentId, AttendanceDeviceServiceProviderType serviceProviderType, DeviceUpdateUserData deviceUpdateUserData){
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					UserBiometricIdentificationData userBiometricIdentificationData = getBiometricDeviceIdentification(studentId, true);
					if(userBiometricIdentificationData == null){
						return false;
					}
					List<RFIDCardData> rfidCardDataList = userBiometricIdentificationData.getRfidCardDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getRfidCardDataList();
					List<FingerprintData> fingerprintDataList = userBiometricIdentificationData.getFingerprintDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getFingerprintDataList();
					List<FaceData> faceDataList =userBiometricIdentificationData.getFaceDataList() == null ? new ArrayList<>() : userBiometricIdentificationData.getFaceDataList();

					for(DeviceUpdateUserIdentityData deviceUpdateUserIdentityData : deviceUpdateUserData.getDeviceUpdateUserIdentityDataList()){
						switch (deviceUpdateUserIdentityData.getAttendanceInputCategory()){
							case CARD:
								if(!cardExists(rfidCardDataList, deviceUpdateUserIdentityData)){
									rfidCardDataList.add(new RFIDCardData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserData.getOperationTime()));
								}
								break;
							case FINGER_PRINT:
								if(!fingerprintExists(fingerprintDataList, deviceUpdateUserIdentityData)){
									fingerprintDataList.add(new FingerprintData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserIdentityData.getSize(), deviceUpdateUserIdentityData.getIndex(), deviceUpdateUserData.getOperationTime()));
								}
								break;
							case FACE:
								if(!faceExists(faceDataList, deviceUpdateUserIdentityData)){
									faceDataList.add(new FaceData(serviceProviderType, deviceUpdateUserIdentityData.getData(), deviceUpdateUserIdentityData.getSize(), deviceUpdateUserIdentityData.getIndex(), deviceUpdateUserData.getOperationTime()));
								}
								break;
						}
					}

					return jdbcTemplate.update(UPDATE_STUDENT_BIOMETRIC_DEVICE_IDENTIFICATION, GSON.toJson(rfidCardDataList), GSON.toJson(fingerprintDataList), GSON.toJson(faceDataList), studentId.toString()) == 1;
				}
			});
		} catch (Exception e){
			logger.error("Unable to update biometric device identification for student {}, serviceProviderType {},  deviceUpdateUserData {}", studentId, serviceProviderType, deviceUpdateUserData, e);
			return false;
		}

	}

	public boolean updateStudentTransferCertificateDetails(int instituteId, UUID studentId, boolean isTCNumberCounterEnable,
														   StudentTransferCertificateDetails studentTransferCertificateDetails) {
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					boolean isCounterUpdated = false;
					if(studentTransferCertificateDetails != null && !studentTransferCertificateDetails.isTCNumberFixed()) {
						final CounterType counterType = CounterType.TRANSFER_CERTIFICATE_NUMBER;
						String tcNumber = getCounterNumber(studentTransferCertificateDetails.getTcNumber(),
								counterType, instituteId, isTCNumberCounterEnable);
						studentTransferCertificateDetails.setTcNumber(tcNumber);
						studentTransferCertificateDetails.setTCNumberFixed(true);
						isCounterUpdated = true;
					}
					final List<Object> args = new ArrayList<Object>();
					final String studentTransferCertificateDetailsStr = studentTransferCertificateDetails == null ?
							null : GSON.toJson(studentTransferCertificateDetails, StudentTransferCertificateDetails.class);
					args.add(studentTransferCertificateDetailsStr);
					args.add(studentId.toString());
					args.add(instituteId);
					if(!(jdbcTemplate.update(UPDATE_STUDENT_TC_DETAILS, args.toArray()) >= 0)) {
						logger.error(
								"Unable to update tc details for studentId {}, instituteId {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to update tc details for institute " + instituteId);
					}
					if (isTCNumberCounterEnable && isCounterUpdated &&
							!instituteDao.incrementCounter(instituteId, CounterType.TRANSFER_CERTIFICATE_NUMBER)) {
						logger.error(
								"Unable to update counter for tc for studentId {}, instituteId {}", studentId, instituteId);
						throw new EmbrateRunTimeException("Unable to increment tc number counter for institute " + instituteId);
					}
					return true;
				}
			});
		} catch (final Exception e) {
			logger.error("Error while updating student tc details", e);
			throw e;
		}
	}

	public Student getStudentWithHouseDetails(int instituteId, Student student) {
		if(student == null) {
			return null;
		}
		List<InstituteHouse> instituteHouseList = instituteDao.getInstituteHouseList(instituteId);
		Map<UUID, InstituteHouse> instituteHouseMap = InstituteHouse.getInstituteHouseMap(instituteHouseList);
		if(CollectionUtils.isEmpty(instituteHouseMap)) {
			return student;
		}
		UUID instituteHouseId = student.getStudentBasicInfo().getInstituteHouseId();
		if(instituteHouseId != null) {
			InstituteHouse instituteHouse = instituteHouseMap.get(instituteHouseId);
			student.getStudentBasicInfo().setInstituteHouse(instituteHouse);
		}
		return student;
	}

	public List<Student> getStudentWithHouseDetails(int instituteId, List<Student> studentList) {
		if(CollectionUtils.isEmpty(studentList)) {
			return new ArrayList<>();
		}
		List<InstituteHouse> instituteHouseList = instituteDao.getInstituteHouseList(instituteId);
		Map<UUID, InstituteHouse> instituteHouseMap = InstituteHouse.getInstituteHouseMap(instituteHouseList);
		if(CollectionUtils.isEmpty(instituteHouseMap)) {
			return studentList;
		}
		List<Student> studentWithHouseDetailsList = new ArrayList<>();
		for(Student student : studentList) {
			UUID instituteHouseId = student.getStudentBasicInfo().getInstituteHouseId();
			if(instituteHouseId != null) {
				InstituteHouse instituteHouse = instituteHouseMap.get(instituteHouseId);
				student.getStudentBasicInfo().setInstituteHouse(instituteHouse);
			}
			studentWithHouseDetailsList.add(student);
		}
		return studentWithHouseDetailsList;
	}

	public boolean updateBulkStudentDetails(MetaDataPreferences metaDataPreferences, BulkStudentPayload bulkStudentPayload) {

		boolean isRegistrationNumberThere = !metaDataPreferences.isRegistrationCounter() && bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.REGISTRATION_NUMBER);
		boolean isAdmissionNumberThere = !metaDataPreferences.isAdmissionCounter() && bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.ADMISSION_NUMBER);
		boolean isPrimaryContactNumberThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.PRIMARY_CONTACT_NUMBER);
		boolean isPrimaryEmailThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.PRIMARY_EMAIL);
		boolean isMotherNameThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.MOTHER_NAME);
		boolean isFatherNameThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.FATHER_NAME);
		boolean isFatherContactNumberThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.FATHER_CONTACT_NUMBER);
		boolean isMotherContactNumberThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.MOTHER_CONTACT_NUMBER);
		boolean isCasteThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.CASTE);
		boolean isCategoryThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.CATEGORY);
		boolean isBloodGroupThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.BLOOD_GROUP);
		boolean isPenThere = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.PEN);
		boolean isAadharNumber = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.AADHAR_NUMBER);
		boolean isAdmissionDate = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.ADMISSION_DATE);
		boolean isDateOfBirth = bulkStudentPayload.getStudentParametersSet()
				.contains(StudentParameters.DATE_OF_BIRTH);
		boolean idFatherOccupationThere = bulkStudentPayload.getStudentParametersSet().contains(StudentParameters.FATHER_OCCUPATION);

		StringBuilder query = new StringBuilder();
		query.append(UPDATE_STUDENT_CLAUSE);
		boolean first = true;
		if(isRegistrationNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_REGISTRATION_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_REGISTRATION_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isAdmissionNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_ADMISSION_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_ADMISSION_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isPrimaryContactNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_PRIMARY_CONTACT_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_PRIMARY_CONTACT_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isPrimaryEmailThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_PRIMARY_EMAIL_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_PRIMARY_EMAIL_CLAUSE);
			}
			first = false;
		}
		if(isMotherNameThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_MOTHER_NAME_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_MOTHER_NAME_CLAUSE);
			}
			first = false;
		}
		if(isFatherNameThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_FATHER_NAME_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_FATHER_NAME_CLAUSE);
			}
			first = false;
		}
		if(isFatherContactNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_FATHER_CONTACT_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_FATHER_CONTACT_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isMotherContactNumberThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_MOTHER_CONTACT_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_MOTHER_CONTACT_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isCasteThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_CASTE_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_CASTE_CLAUSE);
			}
			first = false;
		}
		if(isCategoryThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_CATEGORY_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_CATEGORY_CLAUSE);
			}
			first = false;
		}
		if(isBloodGroupThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_BLOOD_GROUP_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_BLOOD_GROUP_CLAUSE);
			}
			first = false;
		}
		if(isPenThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_PEN_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_PEN_CLAUSE);
			}
			first = false;
		}
		if(isAadharNumber) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_AADHAR_NUMBER_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_AADHAR_NUMBER_CLAUSE);
			}
			first = false;
		}
		if(isAdmissionDate) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_ADMISSION_DATE_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_ADMISSION_DATE_CLAUSE);
			}
			first = false;
		}
		if(isDateOfBirth) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_DATE_OF_BIRTH_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_DATE_OF_BIRTH_CLAUSE);
			}
			first = false;
		}
		if(idFatherOccupationThere) {
			if(first) {
				query.append(SET);
				query.append(UPDATE_FATHERS_OCCUPATION_CLAUSE);
			} else {
				query.append(COMMA);
				query.append(UPDATE_FATHERS_OCCUPATION_CLAUSE);
			}
			first = false;
		}
		query.append(UPDATE_STUDENT_ID_WHERE_CONDITION);


		final List<Object[]> batchInsertArgs = new ArrayList<>();
		Set<UUID> studentIds = new HashSet<>();
		final Map<UUID, String> studentAdmissionNumberMap = new HashMap<>();
		final Map<UUID, String> studentEmailMap = new HashMap<>();
		final Map<UUID, String> studentContactNumberMap = new HashMap<>();
		for(BulkStudentInfo bulkStudentInfo : bulkStudentPayload.getBulkStudentInfoList()) {
			final List<Object> args = new ArrayList<>();
			studentIds.add(bulkStudentInfo.getStudentId());
			if(isRegistrationNumberThere) {
				args.add(bulkStudentInfo.getRegistrationNumber());
			}

			if(isAdmissionNumberThere) {
				args.add(bulkStudentInfo.getAdmissionNumber());
				studentAdmissionNumberMap.put(bulkStudentInfo.getStudentId(), bulkStudentInfo.getAdmissionNumber());
			}

			if(isPrimaryContactNumberThere) {
				args.add(bulkStudentInfo.getPrimaryContactNumber());
				studentContactNumberMap.put(bulkStudentInfo.getStudentId(), bulkStudentInfo.getPrimaryContactNumber());
			}

			if(isPrimaryEmailThere) {
				args.add(bulkStudentInfo.getPrimaryEmail());
				studentEmailMap.put(bulkStudentInfo.getStudentId(), bulkStudentInfo.getPrimaryEmail());
			}

			if(isMotherNameThere) {
				args.add(capitalizeFirstOfAllWords(bulkStudentInfo.getMothersName()));
			}

			if(isFatherNameThere) {
				args.add(capitalizeFirstOfAllWords(bulkStudentInfo.getFathersName()));
			}

			if(isFatherContactNumberThere) {
				args.add(bulkStudentInfo.getFathersContactNumber());
			}

			if(isMotherContactNumberThere) {
				args.add(bulkStudentInfo.getMothersContactNumber());
			}

			if(isCasteThere) {
				args.add(bulkStudentInfo.getCaste());
			}

			if(isCategoryThere) {
				args.add(bulkStudentInfo.getCategory() == null ? null : bulkStudentInfo.getCategory().name());
			}

			if(isBloodGroupThere) {
				args.add(bulkStudentInfo.getBloodGroup() == null ? null : bulkStudentInfo.getBloodGroup().name());
			}

			if(isPenThere) {
				args.add(bulkStudentInfo.getPenNumber());
			}

			if(isAadharNumber) {
				args.add(bulkStudentInfo.getAadharNumber());
			}

			if(isAdmissionDate) {
				args.add((bulkStudentInfo.getAdmissionDate() != null) && (bulkStudentInfo.getAdmissionDate() > 0) ? new Timestamp(bulkStudentInfo.getAdmissionDate() * 1000l) : null);
			}

			if(isDateOfBirth) {
				args.add((bulkStudentInfo.getDateOfBirth() != null) && (bulkStudentInfo.getDateOfBirth() > 0) ? new Timestamp(bulkStudentInfo.getDateOfBirth() * 1000l) : null);
			}

			if(idFatherOccupationThere) {
				args.add(StringUtils.isBlank(bulkStudentInfo.getFathersOccupation()) ? null : bulkStudentInfo.getFathersOccupation().trim());
			}

			args.add(bulkStudentInfo.getStudentId().toString());
			batchInsertArgs.add(args.toArray());
		}

		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);
					if (rows.length != batchInsertArgs.size()) {
						logger.error("Unable to update bulk student information");
						throw new EmbrateRunTimeException("Unable to update bulk student information");
					}
					for (final int rowCount : rows) {
						if (rowCount != 1) {
							logger.error("Unable to update bulk student information");
							throw new EmbrateRunTimeException("Unable to update bulk student information");
						}
					}
					if((!isAdmissionNumberThere || !metaDataPreferences.isAdmissionNumberAuthFlow()
							|| metaDataPreferences.isAdmissionCounter()) && !isPrimaryContactNumberThere && !isPrimaryEmailThere){
						logger.info("Skipping user name update. isAdmissionNumberThere = {}, authFlow = {}, admissionCounter = {}",
								isAdmissionNumberThere, metaDataPreferences.isAdmissionNumberAuthFlow(), metaDataPreferences.isAdmissionCounter());
						return true;
					}

					StringBuilder userQuery = new StringBuilder();
					userQuery.append("(");
					String delimiter = "";
					List<Object> userArgs = new ArrayList<>();
					for(UUID studentId : studentIds){
						if(studentId == null) {
							continue;
						}
						userArgs.add(studentId.toString());
						userQuery.append(delimiter).append("? ");
						delimiter = ",";
					}
					userQuery.append(")");
					List<UUID> existingUserIds = jdbcTemplate.query(String.format(SELECT_USER_IDS, userQuery), userArgs.toArray(), new RowMapper<UUID>() {
						@Override
						public UUID mapRow(ResultSet rs, int rowNum) throws SQLException {
							return UUID.fromString(rs.getString("user_id"));
						}
					});
					if(CollectionUtils.isEmpty(existingUserIds)){
						logger.warn("No user found for user name update. Skipping");
						return true;
					}

					Set<UUID> existingUserIdSet = new HashSet<>(existingUserIds);
					List<Object []> userNameArgs = new ArrayList<>();
					for(UUID studentId : studentIds){
						if(existingUserIdSet.contains(studentId)){
							List<Object> userNameArg = new ArrayList<>();
							if(isAdmissionNumberThere) {
								if(metaDataPreferences.isAdmissionNumberAuthFlow() && !metaDataPreferences.isAdmissionCounter()) {
									String userName = StringUtils.isEmpty(studentAdmissionNumberMap.get(studentId)) ? "" : UserNameUtils.getFullUserName(studentAdmissionNumberMap.get(studentId), metaDataPreferences.getInstituteUniqueCode());
									userNameArg.add(userName);
								}
								userNameArg.add(studentAdmissionNumberMap.get(studentId));
							}

							if(isPrimaryContactNumberThere) {
								String contactNumber = CryptoUtils.encrypt(studentContactNumberMap.get(studentId));
								userNameArg.add(contactNumber);
							}

							if(isPrimaryEmailThere) {
								String emailId = CryptoUtils.encrypt(studentEmailMap.get(studentId));
								userNameArg.add(emailId);
							}
							userNameArg.add(studentId.toString());
							userNameArgs.add(userNameArg.toArray());
						}
					}

					StringBuilder userParameterQuery = new StringBuilder();
					userParameterQuery.append(" update users ");
					boolean userParameterFirst = true;
					if(isAdmissionNumberThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							if(metaDataPreferences.isAdmissionNumberAuthFlow() && !metaDataPreferences.isAdmissionCounter()) {
								userParameterQuery.append(" user_name = ?, user_institute_id = ? ");
							} else {
								userParameterQuery.append(" user_institute_id = ? ");
							}
						} else {
							userParameterQuery.append(COMMA);
							if(metaDataPreferences.isAdmissionNumberAuthFlow() && !metaDataPreferences.isAdmissionCounter()) {
								userParameterQuery.append(" user_name = ?, user_institute_id = ? ");
							} else {
								userParameterQuery.append(" user_institute_id = ? ");
							}
						}
						userParameterFirst = false;
					}

					if(isPrimaryContactNumberThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							userParameterQuery.append(" phone_number = ? ");
						} else {
							userParameterQuery.append(COMMA);
							userParameterQuery.append(" phone_number = ? ");
						}
						userParameterFirst = false;
					}

					if(isPrimaryEmailThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							userParameterQuery.append(" email = ? ");
						} else {
							userParameterQuery.append(COMMA);
							userParameterQuery.append(" email = ? ");
						}
						userParameterFirst = false;
					}

					userParameterQuery.append(" where user_id = ? ");

					if (jdbcTemplate.batchUpdate(userParameterQuery.toString(), userNameArgs).length != userNameArgs.size()) {
						logger.error("Unable to update students user details  {}", studentIds);
						throw new EmbrateRunTimeException("Unable to update students user details");
					}

					return true;
				}
			});
		} catch (final Exception e) {
			logger.error("Unable to update student info for institute {}", bulkStudentPayload.getInstituteId(), e);
		} finally {
			invalidateStudentSearchCache(bulkStudentPayload.getInstituteId(), bulkStudentPayload.getAcademicSessionId());
		}
		return false;
	}

	//TODO: academicSessionId to clear cache. Ideally cache must be cleaned up for all sessions
	public boolean updateBulkStudentDetails(int instituteId, int academicSessionId, StudentManagementUpdateFieldPayload payload, MetaDataPreferences metaDataPreferences) {

		// These are special fields as they are also updated in users table.
		boolean isAdmissionNumberThere = !metaDataPreferences.isAdmissionCounter() && payload.getRequiredFields()
				.contains(StudentManagementField.ADMISSION_NUMBER);
		boolean isPrimaryContactNumberThere = payload.getRequiredFields()
				.contains(StudentManagementField.PRIMARY_CONTACT_NUMBER);
		boolean isPrimaryEmailThere = payload.getRequiredFields()
				.contains(StudentManagementField.PRIMARY_EMAIL);

		StringBuilder query = new StringBuilder();
		query.append(UPDATE_STUDENT_CLAUSE);
		boolean first = true;
		for(StudentManagementField reqField : payload.getRequiredFields()){
			if(reqField == StudentManagementField.ADMISSION_NUMBER){
				if(isAdmissionNumberThere) {
					String delimiter = first ? SET : COMMA;
					query.append(delimiter).append(UPDATE_ADMISSION_NUMBER_CLAUSE);
					first = false;
				}
			} else {
				String delimiter = first ? SET : COMMA;
				query.append(delimiter).append(" " + reqField.getFieldValueManager().getDBColumnName() + " = ? ");
				first = false;
			}
		}

		query.append(UPDATE_STUDENT_ID_WHERE_CONDITION);


		final List<Object[]> batchInsertArgs = new ArrayList<>();
		Set<UUID> studentIds = new HashSet<>();
		final Map<UUID, String> studentAdmissionNumberMap = new HashMap<>();
		final Map<UUID, String> studentEmailMap = new HashMap<>();
		final Map<UUID, String> studentContactNumberMap = new HashMap<>();

		for (StudentManagementUpdateFieldStudentData studentData : payload.getStudentDataList()) {
			final List<Object> args = new ArrayList<>();
			UUID studentId = studentData.getStudentId();
			studentIds.add(studentId);
			for (StudentManagementUpdateFieldValue fieldValue : studentData.getFieldValueList()) {
				if (fieldValue.getField() == StudentManagementField.ADMISSION_NUMBER) {
					if (isAdmissionNumberThere) {
						args.add(fieldValue.getField().getFieldValueManager().getDBColumnValue(fieldValue.getValue()));
					}
					studentAdmissionNumberMap.put(studentId, fieldValue.getValue());

				} else {
					args.add(fieldValue.getField().getFieldValueManager().getDBColumnValue(fieldValue.getValue()));
				}

				if (fieldValue.getField() == StudentManagementField.PRIMARY_CONTACT_NUMBER) {
					studentContactNumberMap.put(studentId, fieldValue.getValue());
				} else if (fieldValue.getField() == StudentManagementField.PRIMARY_EMAIL) {
					studentEmailMap.put(studentId, fieldValue.getValue());
				}
			}
			args.add(studentId.toString());
			batchInsertArgs.add(args.toArray());
		}

		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus status) {
					final int[] rows = jdbcTemplate.batchUpdate(query.toString(), batchInsertArgs);
					if (rows.length != batchInsertArgs.size()) {
						logger.error("Unable to update bulk student information");
						throw new EmbrateRunTimeException("Unable to update bulk student information");
					}
					for (final int rowCount : rows) {
						if (rowCount != 1) {
							logger.error("Unable to update bulk student information");
							throw new EmbrateRunTimeException("Unable to update bulk student information");
						}
					}
					if(!isAdmissionNumberThere && !isPrimaryContactNumberThere && !isPrimaryEmailThere){
						logger.info("No user related attribute to be updated. Skipping user table update");
						return true;
					}

					StringBuilder userQuery = new StringBuilder();
					userQuery.append("(");
					String delimiter = "";
					List<Object> userArgs = new ArrayList<>();
					for(UUID studentId : studentIds){
						if(studentId == null) {
							continue;
						}
						userArgs.add(studentId.toString());
						userQuery.append(delimiter).append("? ");
						delimiter = ",";
					}
					userQuery.append(")");
					List<UUID> existingUserIds = jdbcTemplate.query(String.format(SELECT_USER_IDS, userQuery), userArgs.toArray(), new RowMapper<UUID>() {
						@Override
						public UUID mapRow(ResultSet rs, int rowNum) throws SQLException {
							return UUID.fromString(rs.getString("user_id"));
						}
					});
					if(CollectionUtils.isEmpty(existingUserIds)){
						logger.warn("No user found for user name update. Skipping");
						return true;
					}

					Set<UUID> existingUserIdSet = new HashSet<>(existingUserIds);
					List<Object []> userNameArgs = new ArrayList<>();
					for (UUID studentId : studentIds) {
						if (!existingUserIdSet.contains(studentId)) {
							continue;
						}
						List<Object> userNameArg = new ArrayList<>();
						if (isAdmissionNumberThere) {
							if (metaDataPreferences.isAdmissionNumberAuthFlow()) {
								String userName = StringUtils.isEmpty(studentAdmissionNumberMap.get(studentId)) ? "" : UserNameUtils.getFullUserName(studentAdmissionNumberMap.get(studentId), metaDataPreferences.getInstituteUniqueCode());
								userNameArg.add(userName);
							}
							userNameArg.add(studentAdmissionNumberMap.get(studentId));
						}

						if (isPrimaryContactNumberThere) {
							String contactNumber = CryptoUtils.encrypt(studentContactNumberMap.get(studentId));
							userNameArg.add(contactNumber);
						}

						if (isPrimaryEmailThere) {
							String emailId = CryptoUtils.encrypt(studentEmailMap.get(studentId));
							userNameArg.add(emailId);
						}
						userNameArg.add(studentId.toString());
						userNameArgs.add(userNameArg.toArray());
					}

					StringBuilder userParameterQuery = new StringBuilder();
					userParameterQuery.append(" update users ");
					boolean userParameterFirst = true;
					if(isAdmissionNumberThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							if(metaDataPreferences.isAdmissionNumberAuthFlow()) {
								userParameterQuery.append(" user_name = ?, user_institute_id = ? ");
							} else {
								userParameterQuery.append(" user_institute_id = ? ");
							}
						} else {
							userParameterQuery.append(COMMA);
							if(metaDataPreferences.isAdmissionNumberAuthFlow() && !metaDataPreferences.isAdmissionCounter()) {
								userParameterQuery.append(" user_name = ?, user_institute_id = ? ");
							} else {
								userParameterQuery.append(" user_institute_id = ? ");
							}
						}
						userParameterFirst = false;
					}

					if(isPrimaryContactNumberThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							userParameterQuery.append(" phone_number = ? ");
						} else {
							userParameterQuery.append(COMMA);
							userParameterQuery.append(" phone_number = ? ");
						}
						userParameterFirst = false;
					}

					if(isPrimaryEmailThere) {
						if(userParameterFirst) {
							userParameterQuery.append(SET);
							userParameterQuery.append(" email = ? ");
						} else {
							userParameterQuery.append(COMMA);
							userParameterQuery.append(" email = ? ");
						}
						userParameterFirst = false;
					}

					userParameterQuery.append(" where user_id = ? ");

					if (jdbcTemplate.batchUpdate(userParameterQuery.toString(), userNameArgs).length != userNameArgs.size()) {
						logger.error("Unable to update students user details  {}", studentIds);
						throw new EmbrateRunTimeException("Unable to update students user details");
					}

					return true;
				}
			});
		} catch (final Exception e) {
			logger.error("Unable to update student info for institute {}, payload {}", instituteId, payload, e);
		} finally {
			invalidateStudentSearchCache(instituteId, academicSessionId);
		}
		return false;
	}

	public List<Student> getStudentsInAcademicSession(int instituteId, int academicSessionId, List<StudentStatus> studentStatusList) {
		return searchStudentsInAcademicSesison(instituteId, null, academicSessionId, studentStatusList, null, null, null, null)
				.getResult();
	}
	public SearchResultWithPagination<Student> searchStudentsInAcademicSesison(int instituteId, String searchText,
																			   int academicSessionId, List<StudentStatus> studentStatusList, Integer offset, Integer limit,
																			   String includeUserStatus, String requiredStandardsCSV) {
		if (offset == null || offset < 0 || limit == null || limit <= 0) {
			offset = 0;
			limit = Integer.MAX_VALUE;
		}
		if (instituteId <= 0 || academicSessionId <= 0) {
			return null;
		}
		if (CollectionUtils.isEmpty(studentStatusList)) {
			studentStatusList = Arrays.asList(StudentStatus.ENROLLED);
		}

		Set<UUID> standardIds = new HashSet<>();
		if(!StringUtils.isBlank(requiredStandardsCSV)) {
			final String[] requiredStandardsArray = requiredStandardsCSV.split(",");
			for (final String requiredStandard : requiredStandardsArray) {
				UUID standardId = UUID.fromString(requiredStandard.trim());
				if (!standardIds.contains(standardId)) {
					standardIds.add(standardId);
				}
			}
		}

		return searchStudentsInAcademicSesison(searchText,
				instituteId, academicSessionId, studentStatusList, offset, limit, includeUserStatus, standardIds);
	}

	public List<StudentLite> getStudentLiteWithHouseDetails(int instituteId, List<StudentLite> studentList) {
		if(CollectionUtils.isEmpty(studentList)) {
			return new ArrayList<>();
		}
		List<InstituteHouse> instituteHouseList = instituteDao.getInstituteHouseList(instituteId);
		Map<UUID, InstituteHouse> instituteHouseMap = InstituteHouse.getInstituteHouseMap(instituteHouseList);
		if(CollectionUtils.isEmpty(instituteHouseMap)) {
			return studentList;
		}
		List<StudentLite> studentWithHouseDetailsList = new ArrayList<>();
		for(StudentLite student : studentList) {
			UUID instituteHouseId = student.getInstituteHouseId();
			if(instituteHouseId != null) {
				InstituteHouse instituteHouse = instituteHouseMap.get(instituteHouseId);
				student.setInstituteHouse(instituteHouse);
			}
			studentWithHouseDetailsList.add(student);
		}
		return studentWithHouseDetailsList;
	}

	public boolean updateHouseByStudentId(int instituteId, UUID studentId, UUID houseId) {
		try {
			final Object[] args = { houseId == null ? null : houseId.toString(), studentId.toString(), instituteId };
			return jdbcTemplate.update(UPDATE_HOUSE_ID_BY_STUDENT_ID, args) >= 0;
		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating student house {}", studentId);
		} catch (final Exception e) {
			logger.error("Exception while updating student house {}", studentId, e);
		}
		return false;
	}

	public boolean updateHouseByStudentIds(int instituteId, Map<UUID, UUID> studentIdHouseIdMap) {
		try {
			final List<Object[]> batchInsertArgs = new ArrayList<>();
			int count = 0;
			for(Map.Entry<UUID, UUID> studentIdHouseIdEntry : studentIdHouseIdMap.entrySet()) {
				final List<Object> args2 = new ArrayList<>();

				args2.add(studentIdHouseIdEntry.getValue() == null ? null : studentIdHouseIdEntry.getValue().toString());
				args2.add(studentIdHouseIdEntry.getKey().toString());
				args2.add(instituteId);
				count++;
				batchInsertArgs.add(args2.toArray());
			}

			final int[] rows = jdbcTemplate.batchUpdate(UPDATE_HOUSE_ID_BY_STUDENT_ID, batchInsertArgs);
			if (rows.length != count) {
				return false;
			}
			for (final int rowCount : rows) {
				if (rowCount != 1) {
					return false;
				}
			}
			return true;

		} catch (final DataAccessException dataAccessException) {
			ExceptionHandling.HandleException(dataAccessException, null, null);
			logger.error("Error while updating student house for institute {}", instituteId);
		} catch (final Exception e) {
			logger.error("Exception while updating student house for institute {}", instituteId, e);
		}
		return false;
	}

	public boolean updateStudentDeviceUserId(int instituteId, Map<UUID, String> uuidDeviceUserIdMap) {
		try {
			return transactionTemplate.execute(new TransactionCallback<Boolean>() {
				@Override
				public Boolean doInTransaction(TransactionStatus transactionStatus) {
					List<Object[]> args = new ArrayList<>();
					for (Map.Entry<UUID, String> entry : uuidDeviceUserIdMap.entrySet()) {
						args.add(new Object[]{entry.getValue(), instituteId, entry.getKey().toString()});
					}
					return jdbcTemplate.batchUpdate(UPDATE_STUDENT_DEVICE_USER_ID, args).length == args.size();
				}
			});
		} catch (final Exception e) {
			logger.error("Exception while updating device user id for instituteId {}, uuidDeviceUserIdMap {}", instituteId, uuidDeviceUserIdMap, e);
		}
		return false;
	}

	public List<StudentSiblingDetails> getStudentSiblingDetailWithSessionList(int instituteId, int academicSessionId,
				Set<StudentStatus> studentStatusList) {
		try {
			if(CollectionUtils.isEmpty(studentStatusList)) {
				return null;
			}
			String query = GET_STUDENT_SIBLING_WITH_SESSION_DETAILS;
			query += " and student_academic_session_details.session_status in %s ";
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			StringBuilder statusParams = new StringBuilder("(");
			String delimiter = "";
			for (final StudentStatus studentStatus : studentStatusList) {
				args.add(studentStatus.name());
				statusParams.append(delimiter);
				statusParams.append(" ?");
				delimiter = ",";
			}
			statusParams.append(" )");
			return StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetailsList(
					jdbcTemplate.query(String.format(query, statusParams),
							args.toArray(), STUDENT_SIBLING_ROW_MAPPER));
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}
		return null;
	}

	public StudentSiblingDetails getStudentSiblingDetailWithSession(int instituteId, int academicSessionId, UUID siblingGroupId) {
		if (instituteId <= 0) {
			return null;
		}
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(siblingGroupId.toString());
			StudentSiblingDetails studentSiblingDetails = StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetails(jdbcTemplate.query(
					GET_STUDENT_SIBLING_WITH_SESSION_DETAILS + SIBLING_GROUP_ID_CLAUSE + SESSION_STATUS_CLAUSE, args.toArray(),
					STUDENT_SIBLING_ROW_MAPPER));
			if(studentSiblingDetails == null) {
				return null;
			}
			List<StudentLite> studentLiteList = getStudentLiteWithHouseDetails(instituteId, studentSiblingDetails.getStudentList());
			studentSiblingDetails.setStudentList(studentLiteList);
			return studentSiblingDetails;
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	public StudentSiblingDetails getStudentSiblingDetailsWithSessionByStudentId(int instituteId, int academicSessionId, UUID studentId) {
		if (instituteId <= 0) {
			return null;
		}
		try {
			final List<Object> args = new ArrayList<>();
			args.add(academicSessionId);
			args.add(instituteId);
			args.add(academicSessionId);
			args.add(studentId.toString());
			StudentSiblingDetails studentSiblingDetails = StudentSiblingWithoutSessionRowMapper.getStudentSiblingDetails(
					jdbcTemplate.query(GET_STUDENT_SIBLING_WITH_SESSION_DETAILS + STUDENT_ID_SIBLING_GROUP_ID_CLAUSE + SESSION_STATUS_CLAUSE,
							args.toArray(), STUDENT_SIBLING_ROW_MAPPER));
			if(studentSiblingDetails == null) {
				return null;
			}
			List<StudentLite> studentLiteList = getStudentLiteWithHouseDetails(instituteId, studentSiblingDetails.getStudentList());
			studentSiblingDetails.setStudentList(studentLiteList);
			return studentSiblingDetails;
		} catch (final Exception e) {
			logger.error("Error while getting user stats of institute {}", instituteId, e);
		}

		return null;
	}

	/**
	 * Must be used within the transaction
	 */
	public boolean softDeleteStudentNonAtomic(int instituteId, UUID studentId, String updatedAdmissionNumber,
											  String updatedRegistrationNumber) {
		try {
			return jdbcTemplate.update(SOFT_DELETE_STUDENT_INFO, updatedAdmissionNumber, updatedRegistrationNumber, StudentStatus.DELETED.name(), instituteId, studentId.toString()) == 1;
		} catch (final Exception e) {
			logger.error("Error while updating student delete status for instituteId {}, studentId {}", instituteId, studentId, e);
		}
//		finally {
////			TODO: Invalidate student search cache
////			invalidateStudentSearchCache(instituteId);
//		}
		return false;
	}

	/**
	 * Must be used within the transaction
	 */
	public boolean softDeleteStudentSessionDetailsNonAtomic(int instituteId, UUID studentId) {
		try {
			return jdbcTemplate.update(SOFT_DELETE_STUDENT_ALL_SESSION_INFO,  StudentStatus.DELETED.name(), studentId.toString()) > 0;
		} catch (final Exception e) {
			logger.error("Error while updating student delete status for all sessions in instituteId {}, studentId {}", instituteId, studentId, e);
		}
		return false;
	}

	public boolean updateStudentTag(int instituteId,  StudentTaggedPayload studentTaggedPayload) {
		try {
			List<Object[]> args = new ArrayList<>();
			if (studentTaggedPayload != null) {
				List<UUID> studentIds = studentTaggedPayload.getStudentIdList();
				final String  studentsTaggedDetailsStr = studentTaggedPayload.getStudentTaggedDetailsList() == null ?
						null : GSON.toJson(studentTaggedPayload.getStudentTaggedDetailsList());
				if (studentIds != null) {
					for (UUID studentId : studentIds) {
						args.add(new Object[]{studentsTaggedDetailsStr , studentId.toString(), instituteId});
					}
				}
			}

			return jdbcTemplate.batchUpdate(UPDATE_STUDENT_TAG_BY_STUDENT_ID, args).length == args.size();

		} catch (final Exception e) {
			logger.error("Exception while updating student tag in instituteId {}", instituteId, e);
		}
		return false;
	}

}