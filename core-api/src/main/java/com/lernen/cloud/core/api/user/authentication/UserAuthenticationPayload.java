package com.lernen.cloud.core.api.user.authentication;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserAuthenticationPayload {

    private String userName;

    private String password;

    private String appToken;

    private List<UUID> existingUsers;

    private Map<String, Object> deviceData;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAppToken() {
        return appToken;
    }

    public void setAppToken(String appToken) {
        this.appToken = appToken;
    }

    public List<UUID> getExistingUsers() {
        return existingUsers;
    }

    public void setExistingUsers(List<UUID> existingUsers) {
        this.existingUsers = existingUsers;
    }

    public Map<String, Object> getDeviceData() {
        return deviceData;
    }

    public void setDeviceData(Map<String, Object> deviceData) {
        this.deviceData = deviceData;
    }

}
