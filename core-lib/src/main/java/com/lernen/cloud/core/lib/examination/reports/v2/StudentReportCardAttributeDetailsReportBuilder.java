package com.lernen.cloud.core.lib.examination.reports.v2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.report.config.PDFConfigs;
import com.itextpdf.kernel.geom.PageSize;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.embrate.cloud.core.api.report.config.ReportConfigs;
import com.embrate.cloud.core.api.report.layout.ReportBody;
import com.embrate.cloud.core.api.report.layout.ReportHeader;
import com.embrate.cloud.core.api.report.layout.ReportRow;
import com.embrate.cloud.core.api.report.util.ReportMetadata;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.embrate.cloud.core.lib.report.builder.ReportBuilder;
import com.lernen.cloud.core.api.examination.report.ExamReportCardConfiguration;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.StudentReportCardAttributeDetailsPayload;
import com.lernen.cloud.core.api.examination.report.StudentReportCardAttributeDetailsStore;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.Module;
import com.lernen.cloud.core.lib.constants.ReportHeaderAttribute;
import com.lernen.cloud.core.lib.examination.ExamReportCardManager;
import com.lernen.cloud.core.lib.examination.ExamMarksUtils;
import com.lernen.cloud.core.lib.examination.reports.v2.StudentReportSortingService;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.embrate.cloud.core.utils.institute.StandardUtils;

public class StudentReportCardAttributeDetailsReportBuilder extends
        ReportBuilder<StudentReportCardAttributeDetailsPayload, StudentReportCardAttributeDetailsStore> {

    private static final Logger logger = LogManager.getLogger(StudentReportCardAttributeDetailsReportBuilder.class);

    private final ExamReportCardManager examReportCardManager;

    public StudentReportCardAttributeDetailsReportBuilder(UserPermissionManager userPermissionManager,
            InstituteManager instituteManager, ExamReportCardManager examReportCardManager) {
        super(userPermissionManager, instituteManager);
        this.examReportCardManager = examReportCardManager;
    }

    @Override
    public ReportMetadata getReportMetadata() {
        return new ReportMetadata("STUDENT_REPORT_CARD_ATTRIBUTE_DETAILS_REPORT",
                "Student Report Card Attribute Details", Module.EXAMINATION,
                "Report containing student report card attribute details");
    }

    @Override
    public List<ReportHeaderAttribute> getStaticHeaders() {
        return Arrays.asList(
                ReportHeaderAttribute.EXAM_SR_NO,
                ReportHeaderAttribute.ADMISSION_No,
                ReportHeaderAttribute.ROLL_NO,
                ReportHeaderAttribute.NAME.markMandatory(),
                ReportHeaderAttribute.CLASS,
                ReportHeaderAttribute.FATHER_NAME,
                ReportHeaderAttribute.DOB,
                ReportHeaderAttribute.STUDENT_PRIMARY_CONTACT,
                ReportHeaderAttribute.PERCENTAGE,
                ReportHeaderAttribute.GRADE,
                ReportHeaderAttribute.DIVISION,
                ReportHeaderAttribute.RESULT,
                ReportHeaderAttribute.RANK,
                ReportHeaderAttribute.ATTENDANCE,
                ReportHeaderAttribute.STUDENT_SESSION_HEIGHT,
                ReportHeaderAttribute.STUDENT_SESSION_WEIGHT,
                ReportHeaderAttribute.REMARKS,
                ReportHeaderAttribute.TC_REMARKS
        );
    }

    @Override
    public void verifyAuthorisation(StudentReportCardAttributeDetailsPayload payload) {
//        userPermissionManager.verifyAuthorisation(payload.getInstituteId(), null,
//                AuthorisationRequiredAction.GENERATE_EXAM_REPORT_CARD);
    }

    @Override
    public void payloadVerification(StudentReportCardAttributeDetailsPayload payload) {
        if (payload.getInstituteId() <= 0 || payload.getAcademicSessionId() <= 0 ||
            payload.getStandardId() == null || payload.getReportType() == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid Request"));
        }
    }

    @Override
    public StudentReportCardAttributeDetailsStore getStore(StudentReportCardAttributeDetailsPayload payload) {
        int instituteId = payload.getInstituteId();
        int academicSessionId = payload.getAcademicSessionId();
        UUID standardId = payload.getStandardId();
        Set<Integer> sectionIdSet = payload.getSectionIdSet();
        String reportType = payload.getReportType();

        // Get exam report card configuration
        ExamReportCardConfiguration examReportCardConfiguration = examReportCardManager
                .getExamReportCardConfiguration(instituteId, academicSessionId, standardId, reportType);

        if (examReportCardConfiguration == null) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Exam report card configuration not found"));
        }

        // Fetch class exam report data
        List<ExamReportData> classExamReportData = examReportCardManager.getClassExamReportData(instituteId,
                academicSessionId, standardId, sectionIdSet, examReportCardConfiguration.getExamReportStructure(),
                reportType, true);

        if (CollectionUtils.isEmpty(classExamReportData)) {
            throw new ApplicationException(
                    new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No exam report data found"));
        }

        // Get standard and class name
        Standard standard = instituteManager.getStandardByStandardId(instituteId, academicSessionId, standardId);
        String className = StandardUtils.getClassName(sectionIdSet, standard);

        // Get academic session display name
        String academicSessionDisplayName = instituteManager.getAcademicSession(instituteId, academicSessionId).getDisplayName();

        return new StudentReportCardAttributeDetailsStore(classExamReportData, className, academicSessionDisplayName);
    }

    @Override
    public String getDownloadReportName(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        return "Student_Report_Card_Attribute_Details_" + store.getClassName() + "_" +
               store.getAcademicSessionDisplayName();
    }

    @Override
    public ReportConfigs getConfigs(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        return new ReportConfigs(null, null, new PDFConfigs(PageSize.A4.rotate()),
                null); // Use default configs
    }

    @Override
    public ReportHeader getHeader(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        List<String> requiredHeaders = payload.getRequiredHeaders();
        List<ReportRow> headerRows = new ArrayList<>();
        List<ReportCellDetails> headerCells = new ArrayList<>();

        // Create header based on required headers or use all headers if none specified
        if (CollectionUtils.isEmpty(requiredHeaders)) {
            requiredHeaders = getDefaultHeaderKeys();
        }

        for (String headerKey : requiredHeaders) {
            String headerTitle = getHeaderTitle(headerKey);
            headerCells.add(createCell(headerTitle, true));
        }

        headerRows.add(new ReportRow(headerCells));
        return new ReportHeader(headerRows, null);
    }

    @Override
    public ReportBody getBody(StudentReportCardAttributeDetailsPayload payload,
            StudentReportCardAttributeDetailsStore store) {
        List<String> requiredHeaders = payload.getRequiredHeaders();
        if (CollectionUtils.isEmpty(requiredHeaders)) {
            requiredHeaders = getDefaultHeaderKeys();
        }

        List<ReportRow> bodyRows = new ArrayList<>();
        List<ExamReportData> examReportDataList = store.getExamReportDataList();

        int serialNumber = 1;
        for (ExamReportData examReportData : examReportDataList) {
            List<ReportCellDetails> rowCells = new ArrayList<>();

            for (String headerKey : requiredHeaders) {
                String cellValue = getCellValue(headerKey, examReportData, serialNumber);
                rowCells.add(createCell(cellValue, false));
            }

            bodyRows.add(new ReportRow(rowCells));
            serialNumber++;
        }

        return new ReportBody(bodyRows, null);
    }

    private List<String> getDefaultHeaderKeys() {
        return Arrays.asList(
                "sr_no", "admission_no", "roll_no", "name", "class", "father_name", "dob",
                "student_primary_contact_number", "percentage", "grade", "division", "result",
                "rank", "attendance", "student_session_height", "student_session_weight",
                "remarks", "tc_remarks"
        );
    }

    private String getHeaderTitle(String headerKey) {
        switch (headerKey) {
            case "sr_no": return "Sr No.";
            case "admission_no": return "Admission Number";
            case "roll_no": return "Roll No";
            case "name": return "Student Name";
            case "class": return "Class";
            case "father_name": return "Father Name";
            case "dob": return "DOB";
            case "student_primary_contact_number": return "Primary Contact Number";
            case "percentage": return "Percentage";
            case "grade": return "Grade";
            case "division": return "Division";
            case "result": return "Result";
            case "rank": return "Rank";
            case "attendance": return "Attendance";
            case "student_session_height": return "Height";
            case "student_session_weight": return "Weight";
            case "remarks": return "Class Teacher Remarks";
            case "tc_remarks": return "Principal Remarks";
            default: return headerKey;
        }
    }

    private String getCellValue(String headerKey, ExamReportData examReportData, int serialNumber) {
        StudentLite studentLite = examReportData.getStudentLite();

        switch (headerKey) {
            case "sr_no":
                return String.valueOf(serialNumber);
            case "admission_no":
                return studentLite.getAdmissionNumber() != null ? studentLite.getAdmissionNumber() : "";
            case "roll_no":
                return studentLite.getStudentSessionData() != null && studentLite.getStudentSessionData().getRollNumber() != null ?
                       studentLite.getStudentSessionData().getRollNumber() : "";
            case "name":
                return studentLite.getName() != null ? studentLite.getName() : "";
            case "class":
                return studentLite.getStudentSessionData() != null && studentLite.getStudentSessionData().getStandardNameWithSection() != null ?
                       studentLite.getStudentSessionData().getStandardNameWithSection() : "";
            case "father_name":
                return studentLite.getFathersName() != null ? studentLite.getFathersName() : "";
            case "dob":
                return studentLite.getDateOfBirth() != null ? String.valueOf(studentLite.getDateOfBirth()) : "";
            case "student_primary_contact_number":
                return studentLite.getPrimaryContactNumber() != null ? studentLite.getPrimaryContactNumber() : "";
            case "percentage":
                Double percentage = examReportData.getPercentage();
                return percentage != null ? String.format("%.2f", percentage) : "";
            case "grade":
                return examReportData.getTotalGrade() != null ? examReportData.getTotalGrade().getGradeName() : "";
            case "division":
                return ExamMarksUtils.getDivision(examReportData.getPercentage());
            case "result":
                return examReportData.getExamResultStatus() != null ? examReportData.getExamResultStatus().name() : "";
            case "rank":
                return examReportData.getRank() != null ? String.valueOf(examReportData.getRank()) : "";
            case "attendance":
                Integer attendedDays = examReportData.getTotalAttendedDays();
                Integer totalDays = examReportData.getTotalWorkingDays();
                if (attendedDays != null && totalDays != null) {
                    return attendedDays + "/" + totalDays;
                }
                return "";
            case "student_session_height":
                return examReportData.getHeight() != null ? examReportData.getHeight() : "";
            case "student_session_weight":
                return examReportData.getWeight() != null ? examReportData.getWeight() : "";
            case "remarks":
                return examReportData.getRemarks() != null ? examReportData.getRemarks() : "";
            case "tc_remarks":
                return examReportData.getPrincipalRemarks() != null ? examReportData.getPrincipalRemarks() : "";
            default:
                return "";
        }
    }
}
