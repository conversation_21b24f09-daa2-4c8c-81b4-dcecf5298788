package com.embrate.cloud.core.api.hpc.web;

import java.util.List;

/**
 * <AUTHOR>
 */
public class HPCWebContainer {

	private final String id;

	private List<HPCWebContainer> childContainers;

	private HPCWebImageContainer imageContainer;

	private List<HPCWebTextElement> textElements;

	private  HPCWebTable table;

	private HPCFilledStatus hpcFilledStatus;

	public HPCWebContainer(String id) {
		this.id = id;
	}

	public void computeHPCFilledStatus() {

		// Evaluate imageContainer status
		if (imageContainer != null) {
			if (imageContainer.getHpcFilledStatus() != null && imageContainer.getHpcFilledStatus() == HPCFilledStatus.PENDING) {
				hpcFilledStatus = HPCFilledStatus.PENDING;
				return;
			}
		}

		// Evaluate textElements' statuses
		if (textElements != null) {
			for (HPCWebTextElement textElement : textElements) {
				if (textElement.getHpcFilledStatus() != null && textElement.getHpcFilledStatus() == HPCFilledStatus.PENDING) {
					hpcFilledStatus = HPCFilledStatus.PENDING;
					return;
				}
			}
		}

		// Evaluate table status
		if (table != null) {
			if (table.getHpcFilledStatus() != null && table.getHpcFilledStatus() == HPCFilledStatus.PENDING) {
				hpcFilledStatus = HPCFilledStatus.PENDING;
				return;
			}
		}

		// Evaluate childContainers' statuses
		if (childContainers != null) {
			for (HPCWebContainer child : childContainers) {
				if (child.getHpcFilledStatus() != null && child.getHpcFilledStatus() == HPCFilledStatus.PENDING) {
					hpcFilledStatus = HPCFilledStatus.PENDING;
					return;
				}
			}
		}

		hpcFilledStatus = HPCFilledStatus.COMPLETED;
	}

	public String getId() {
		return id;
	}

	public List<HPCWebContainer> getChildContainers() {
		return childContainers;
	}

	public void setChildContainers(List<HPCWebContainer> childContainers) {
		this.childContainers = childContainers;
	}

	public HPCWebImageContainer getImageContainer() {
		return imageContainer;
	}

	public void setImageContainer(HPCWebImageContainer imageContainer) {
		this.imageContainer = imageContainer;
	}

	public List<HPCWebTextElement> getTextElements() {
		return textElements;
	}

	public void setTextElements(List<HPCWebTextElement> textElements) {
		this.textElements = textElements;
	}

	public HPCWebTable getTable() {
		return table;
	}

	public void setTable(HPCWebTable table) {
		this.table = table;
	}

	public HPCFilledStatus getHpcFilledStatus() {
		return hpcFilledStatus;
	}

	public void setHpcFilledStatus(HPCFilledStatus hpcFilledStatus) {
		this.hpcFilledStatus = hpcFilledStatus;
	}

	@Override
	public String toString() {
		return "HPCWebContainer{" +
				"id='" + id + '\'' +
				", childContainers=" + childContainers +
				", imageContainer=" + imageContainer +
				", textElements=" + textElements +
				", table=" + table +
				", hpcFilledStatus=" + hpcFilledStatus +
				'}';
	}
}
