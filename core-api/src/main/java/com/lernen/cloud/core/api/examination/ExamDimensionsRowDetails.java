package com.lernen.cloud.core.api.examination;

import java.util.UUID;

import com.lernen.cloud.core.api.course.CourseType;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExamDimensionsRowDetails {

	private final UUID examId;

	private final CourseType courseType;

	private final ExamDimensionValues examDimensionValues;

	public ExamDimensionsRowDetails(UUID examId, CourseType courseType, ExamDimensionValues examDimensionValues) {
		this.examId = examId;
		this.courseType = courseType;
		this.examDimensionValues = examDimensionValues;
	}

	public UUID getExamId() {
		return examId;
	}

	public CourseType getCourseType() {
		return courseType;
	}

	public ExamDimensionValues getExamDimensionValues() {
		return examDimensionValues;
	}

}
