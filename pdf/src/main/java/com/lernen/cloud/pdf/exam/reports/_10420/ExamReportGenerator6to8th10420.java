package com.lernen.cloud.pdf.exam.reports._10420;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.RomanNumberUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;

public class ExamReportGenerator6to8th10420 extends ExamReportGenerator10420 implements IExamReportCardGenerator{

    public ExamReportGenerator6to8th10420(AssetProvider assetProvider) {
        super(assetProvider);
        //TODO Auto-generated constructor stub
    }
    private static final Logger logger = LogManager.getLogger(ExamReportGenerator6to8th10420.class);

    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}
    private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(),
				"SUBJECTS", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", null,
				"-");
		

        List<String> descriptionTextList = STANDARD_ID_DESCRIPTION_MAP.get(examReportData.getStandardMetaData().getStandardId());
		if (!CollectionUtils.isEmpty(descriptionTextList)) {
			for(String descriptionText : descriptionTextList){
				generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportData, boldFont, regularFont, descriptionText);
			}
		}
		else{
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		}



		if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
				.get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
					"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
		}

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		int totalStudents = getStudentCountInClass(institute.getInstituteId(), examReportData.getStudentLite(), studentManager);
		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, reportType, boldFont, regularFont, totalStudents);
		generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);
		generateSignatureBox(examReportCardLayoutData.getDocument(), institute.getInstituteId(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType, studentManager, examReportData, 6f);
	}
    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
			StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData,
				instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(), examReportData,
				examReportCardLayoutData, studentLite, institute, reportType, 20f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f, boldFont, regularFont, studentManager);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}
    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

		String fatherNameString = StringUtils.isBlank(studentLite.getFathersName()) ? "": "Mr."+studentLite.getFathersName();
		String motherNameString = StringUtils.isBlank(studentLite.getMothersName()) ? "": "Mrs."+studentLite.getMothersName();

		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph fatherName = getKeyValueParagraph("Father Name : ", fatherNameString, boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),  boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", motherNameString, boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph admissionNumber = getKeyValueParagraph("S.R. No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				RomanNumberUtils.standardNameToRoman(studentLite.getStudentSessionData().getStandardNameWithSection()), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), boldFont, boldFont).setMultipliedLeading(1.1f);
		/* Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				boldFont, boldFont); */
        Paragraph house = getKeyValueParagraph("House : ",
				studentLite.getInstituteHouse() == null ? " " : studentLite.getInstituteHouse().getHouseName(),
				boldFont, boldFont).setMultipliedLeading(1.1f);

        Paragraph address = getKeyValueParagraph("Address : ", studentLite.getStudentFullAddress(), boldFont, boldFont).setMultipliedLeading(1.1f);
        


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(house, thirdCellLayoutSetup)));

		document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(address, firstCellLayoutSetup)));       
        
        document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}
    protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont,  StudentManager studentManager) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        
        byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
        if (studentImage == null) {
			studentImage = ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME_WITH_TEXT_2);
        }
		 generateImage(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, LOGO_WIDTH, LOGO_HEIGHT,
                    examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - offsetX - LOGO_WIDTH,
                    offsetY);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);
		Color blueTextColor = WebColors.getRGBColor(EColorUtils.BLUE_COLOR_HEX_CODE_2);
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setFontColor(blueTextColor).setMultipliedLeading(0.8f)),
						cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 18));
		String letterHeadline = institute.getLetterHeadLine1();
		String splitPoint = "Vill.: Soyla";
		int index = letterHeadline.indexOf(splitPoint);
		String letterHeadline1, letterHeadline12;
		if (index != -1) {
			letterHeadline1 = letterHeadline.substring(0, index).trim();
			letterHeadline12 = letterHeadline.substring(index).trim();
		} else {
			letterHeadline1 = letterHeadline;
			letterHeadline12 = "";
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadline1).setMultipliedLeading(0.8f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
		if(!StringUtils.isBlank(letterHeadline12)){
			addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadline12).setMultipliedLeading(0.8f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2()).setMultipliedLeading(0.8f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));

		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) ? "First Term EXAMINATION "
				: "ANNUAL EXAMINATION ";
        String headerText = examReportData.getExamReportStructure().getExamReportStructureMetaData().getReportCardHeaderName();
        if(!StringUtils.isBlank(headerText)){
            headerExamTitle = headerText;
        }
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setMultipliedLeading(0.8f).setUnderline()),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont, int totalStudents) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				boldFont, boldFont);
		Paragraph conduct = getKeyValueParagraph("Conduct : ", "", boldFont, boldFont);
		List<Integer> greenColorRGB = EColorUtils.hex2Rgb(EColorUtils.LIGHT_GREEN_COLOR_HEX_CODE);
		List<Integer> redColorRGB = EColorUtils.hex2Rgb(EColorUtils.LIGHT_RED_COLOR_HEX_CODE);
		List<Integer> blackColorRGB = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);
		List<Integer> resultTextColor = examReportData.getExamResultStatus() == ExamResultStatus.PASS ? greenColorRGB : examReportData.getExamResultStatus() == ExamResultStatus.FAIL ? redColorRGB : null;
		String resultString = StringUtils.isBlank(examReportData.getExamResultStatus().getDisplayName()) ? "-" : examReportData.getExamResultStatus().getDisplayName();
		Paragraph result = getKeyValueParagraph("Result : ", resultString, blackColorRGB, resultTextColor, boldFont, boldFont).setMultipliedLeading(1.1f);

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: Math.round(examReportData.getPercentage() * 100) / 100d + "%", boldFont, boldFont).setMultipliedLeading(1.1f);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), boldFont, boldFont).setMultipliedLeading(1.1f);
		String totalWorkingDaysString =examReportData.getTotalWorkingDays() == null ? "-"
						: String.valueOf(examReportData.getTotalWorkingDays());
		String totalattendedDaysString = examReportData.getTotalAttendedDays() == null ? "-"
						: String.valueOf(examReportData.getTotalAttendedDays());
		Paragraph attendance = getKeyValueParagraph("Attendance: ", totalattendedDaysString+"/"+totalWorkingDaysString, boldFont, boldFont).setMultipliedLeading(1.1f);
		String rankString = examReportData.getRank() == null ? "-" : examReportData.getRank().toString()+"/"+String.valueOf(totalStudents);
		Paragraph rank = getKeyValueParagraph("Rank : ", rankString, boldFont, boldFont).setMultipliedLeading(1.1f);

		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE) || reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}
		else{
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(attendance, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(conduct, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(dateOfResultDeclaration, cellLayoutSetup)));

		document.add(table);

	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}
}
