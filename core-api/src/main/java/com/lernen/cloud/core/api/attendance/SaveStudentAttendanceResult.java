package com.lernen.cloud.core.api.attendance;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class SaveStudentAttendanceResult {

	/**
	 * keeping both notification flag as separate because mobile app notification will be sent for all attendances of present day
	 * while sms will depend on configuration i.e. for which attendance type sms is enabled
	 */
	private final boolean sendMobileAppPushNotification;
	private final boolean sendSMS;
	private final int academicSessionId;
	private final List<StudentAttendancePayload> studentAttendancePayloadList;

	public SaveStudentAttendanceResult(boolean sendMobileAppPushNotification, boolean sendSMS, int academicSessionId, List<StudentAttendancePayload> studentAttendancePayloadList) {
		this.sendMobileAppPushNotification = sendMobileAppPushNotification;
		this.sendSMS = sendSMS;
		this.academicSessionId = academicSessionId;
		this.studentAttendancePayloadList = studentAttendancePayloadList;
	}

	public boolean isSendMobileAppPushNotification() {
		return sendMobileAppPushNotification;
	}

	public boolean isSendSMS() {
		return sendSMS;
	}

	public int getAcademicSessionId() {
		return academicSessionId;
	}

	public List<StudentAttendancePayload> getStudentAttendancePayloadList() {
		return studentAttendancePayloadList;
	}

	@Override
	public String toString() {
		return "SaveStudentAttendanceResult{" +
				"sendMobileAppPushNotification=" + sendMobileAppPushNotification +
				", sendSMS=" + sendSMS +
				", academicSessionId=" + academicSessionId +
				", studentAttendancePayloadList=" + studentAttendancePayloadList +
				'}';
	}
}
