package com.lernen.cloud.core.api.examination.report;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.embrate.cloud.core.api.report.util.IReportPayload;
import com.lernen.cloud.core.api.student.StudentSortingParameters;

public class StudentReportCardAttributeDetailsPayload implements IReportPayload {

    private final int instituteId;
    private final int academicSessionId;
    private final UUID standardId;
    private final Set<Integer> sectionIdSet;
    private final String reportType;
    private final List<String> requiredHeaders;
    private final StudentSortingParameters studentSortingParameters;

    public StudentReportCardAttributeDetailsPayload(int instituteId, int academicSessionId, UUID standardId,
            Set<Integer> sectionIdSet, String reportType, List<String> requiredHeaders,
            StudentSortingParameters studentSortingParameters) {
        this.instituteId = instituteId;
        this.academicSessionId = academicSessionId;
        this.standardId = standardId;
        this.sectionIdSet = sectionIdSet;
        this.reportType = reportType;
        this.requiredHeaders = requiredHeaders;
        this.studentSortingParameters = studentSortingParameters;
    }

    public int getInstituteId() {
        return instituteId;
    }

    public int getAcademicSessionId() {
        return academicSessionId;
    }

    public UUID getStandardId() {
        return standardId;
    }

    public Set<Integer> getSectionIdSet() {
        return sectionIdSet;
    }

    public String getReportType() {
        return reportType;
    }

    public List<String> getRequiredHeaders() {
        return requiredHeaders;
    }

    public StudentSortingParameters getStudentSortingParameters() {
        return studentSortingParameters;
    }

    @Override
    public List<String> getSelectedColumns() {
        return requiredHeaders != null ? requiredHeaders : Collections.emptyList();
    }

    @Override
    public String toString() {
        return "StudentReportCardAttributeDetailsPayload [instituteId=" + instituteId + ", academicSessionId="
                + academicSessionId + ", standardId=" + standardId + ", sectionIdSet=" + sectionIdSet + ", reportType="
                + reportType + ", requiredHeaders=" + requiredHeaders + "]";
    }
}
