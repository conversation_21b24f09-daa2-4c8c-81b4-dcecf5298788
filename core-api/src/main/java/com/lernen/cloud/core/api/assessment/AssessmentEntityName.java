package com.lernen.cloud.core.api.assessment;

import org.apache.commons.lang3.StringUtils;

public enum AssessmentEntityName {
     ASSESSMENT , QUESTION;

    public static AssessmentEntityName getEntityNameType(String assessmentEntityName) {
		if (StringUtils.isBlank(assessmentEntityName)) {
			return null;
		}
		for (AssessmentEntityName assessmentEntityNameEnum : AssessmentEntityName.values()) {
			if (assessmentEntityNameEnum.name().equalsIgnoreCase(assessmentEntityName)) {
				return assessmentEntityNameEnum;
			}
		}
		return null;
	}
}
