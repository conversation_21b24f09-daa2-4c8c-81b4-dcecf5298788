package com.lernen.cloud.core.lib.student;

import com.embrate.cloud.core.api.attendance.AttendanceDeviceServiceProviderType;
import com.embrate.cloud.core.api.attendance.DeviceUpdateUserData;
import com.embrate.cloud.core.api.hostelmanagement.HostelDetails;
import com.embrate.cloud.core.api.student.management.*;
import com.embrate.cloud.core.lib.hostel.management.HostelManagementManager;
import com.embrate.cloud.core.lib.student.management.StudentManagementFieldHandler;
import com.embrate.cloud.core.utils.EMapUtils;
import com.lernen.cloud.core.api.attendance.AttendanceStatus;
import com.lernen.cloud.core.api.common.*;
import com.lernen.cloud.core.api.configurations.MetaDataPreferences;
import com.lernen.cloud.core.api.examination.StudentExamResultDetails;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.fees.payment.FeePaymentDetails;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.ReportOutput;
import com.lernen.cloud.core.api.staff.FullStaffDetails;
import com.lernen.cloud.core.api.staff.Staff;
import com.lernen.cloud.core.api.staff.StaffDocumentType;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.student.lite.StudentSearchEntry;
import com.lernen.cloud.core.api.user.*;
import com.lernen.cloud.core.lib.attendance.AttendanceManager;
import com.lernen.cloud.core.lib.audit.log.StudentAuditLogWriter;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.document.DocumentManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.managers.UserManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.staff.StaffManager;
import com.lernen.cloud.core.utils.*;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.lernen.cloud.core.utils.student.StudentDataUtils;
import com.lernen.cloud.dao.tier.fees.payment.FeePaymentTransactionDao;
import com.lernen.cloud.dao.tier.hostelmanagement.HostelManagementDao;
import com.lernen.cloud.dao.tier.staff.StaffDao;
import com.lernen.cloud.dao.tier.student.StudentDao;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;

public class StudentManager {

	private static final Logger logger = LogManager.getLogger(StudentManager.class);
	private final StudentDao studentDao;
	private final InstituteManager instituteManager;
	private final DocumentManager documentManager;
	private final UserPreferenceSettings userPreferenceSettings;
	private final UserPermissionManager userPermissionManager;
	private final StudentAuditLogWriter studentAuditLogWriter;
	private final AttendanceManager attendanceManager;
	private final FeePaymentTransactionDao feePaymentTransactionDao;
	private final StaffDao staffDao;
	private final HostelManagementDao hostelManagementDao;

	private final StudentManagementFieldHandler studentManagementFieldHandler;

	private static final String STUDENT_COMMON_DOCUMENT_DIRECTORY_NAME = "common";
	private static final String THUMBNAIL_SUFFIX = "thumbnail";
	private static final String HYPHEN = "-";
	private static final String STAFF_PARENT_DIRECTORY_NAME = "staff";
	private static final String S3_FILE_PATH_DELIMITER = "/";
	private static final String STAFF_COMMON_DOCUMENT_DIRECTORY_NAME = "common";

	public StudentManager(StudentDao studentDao, InstituteManager instituteManager, DocumentManager documentManager,
						  UserPreferenceSettings userPreferenceSettings, UserPermissionManager userPermissionManager,
						  StudentAuditLogWriter studentAuditLogWriter, AttendanceManager attendanceManager,
						  FeePaymentTransactionDao feePaymentTransactionDao, StaffDao staffDao, HostelManagementDao hostelManagementDao) {
		this.studentDao = studentDao;
		this.instituteManager = instituteManager;
		this.documentManager = documentManager;
		this.userPreferenceSettings = userPreferenceSettings;
		this.userPermissionManager = userPermissionManager;
		this.studentAuditLogWriter = studentAuditLogWriter;
		this.attendanceManager = attendanceManager;
		this.feePaymentTransactionDao = feePaymentTransactionDao;
		this.staffDao = staffDao;
		this.studentManagementFieldHandler = new StudentManagementFieldHandler();
		this.hostelManagementDao = hostelManagementDao;
	}
	
	public Double getWalletAmount(UUID studentId, DBLockMode dbLockMode) {
		return studentDao.getWalletAmount(studentId, dbLockMode);
	}

	public Student getStudentWithoutSession(UUID studentId) {
		return studentDao.getStudent(studentId);
	}

	public Integer getStudentInstitute(UUID studentId) {
		return studentDao.getStudentInstitute(studentId);
	}

	public List<Student> getStudentsWithoutSession(List<UUID> studentIds) {
		return studentDao.getStudent(studentIds);
	}

	public List<Student> getStudentsWithWalletDetails(int instituteId, int academicSessionId,
													  Set<UUID> standardIdSet, Set<StudentStatus> studentStatusSet) {
		return studentDao.getStudentsWithWalletDetails(instituteId, academicSessionId, standardIdSet, studentStatusSet);
	}

	public UUID addStudent(StudentPayload studentPayload) {
		validateAddStudent(studentPayload);
		final MetaDataPreferences metaDataPreferences = userPreferenceSettings
				.getMetaDataPreferences(studentPayload.getInstituteId());
		return studentDao.addStudent(studentPayload, metaDataPreferences.isRegistrationCounter());
	}

	public boolean addAcademicSessionStudentDetails(int instituteId, int academicSession, UUID studentId, UUID standardId,
			Integer sectionId, String rollNumber, Medium medium, boolean isNewAdmission, UUID hostelId) {
		return studentDao.addAcademicSessionStudentDetails(instituteId, academicSession, studentId, standardId, sectionId,
				rollNumber, medium, StudentStatus.ENROLLED, isNewAdmission, hostelId);
	}

	public Student getStudentByAcademicSessionStudentId(int instituteId, int academicSessionId, UUID studentId, boolean addThumbnail) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session id."));
		}
		Student student = studentDao.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		if(student == null) {
			return null;
		}
		if (addThumbnail) {
			student.setThumbnail(getThumbnail(student));
		}
		return student;
	}

	public Student getStudentByAcademicSessionStudentId(int instituteId, int academicSessionId, UUID studentId) {
		return getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId, false);
	}

	public Student getEnrolledStudentInSession(int instituteId, int academicSessionId, UUID studentId) {
		List<Student> students = getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, Arrays.asList(studentId));
		if(org.apache.commons.collections4.CollectionUtils.isEmpty(students)){
			return null;
		}
		return students.get(0);
	}
	public List<Student> getStudentByAcademicSessionStudentIds(int instituteId, int academicSessionId,
			List<UUID> studentIds) {
		return getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds,
				StudentStatus.ENROLLED);
	}

	public List<Student> getStudentByAcademicSessionStudentIds(int instituteId, int academicSessionId,
			List<UUID> studentIds, StudentStatus studentStatus) {
		if (studentStatus == null) {
			studentStatus = StudentStatus.ENROLLED;
		}
		return studentDao.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds,
				studentStatus);
	}

	public List<Student> getStudentByAcademicSessionStudentIds(int instituteId, int academicSessionId,
															   List<UUID> studentIds, Set<StudentStatus> studentStatusSet) {
		
		if (CollectionUtils.isEmpty(studentStatusSet)) {
			studentStatusSet = new HashSet<>();
			studentStatusSet.add(StudentStatus.ENROLLED);
		}
	
		return studentDao.getStudentByAcademicSessionStudentIds(instituteId, academicSessionId, studentIds,
				studentStatusSet);
	}

	public List<Student> getClassRepeatersReport(int instituteId,int academicSessionId,int previousAcademicSessionId){
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session id."));
		}
		return studentDao.getClassRepeatersReport(instituteId,academicSessionId,previousAcademicSessionId);
	}
	public boolean updateStudentAdmissionNumber(int instituteId, int academicSessionId, UUID userId, StudentUpdateAdmissionNumberPayload studentUpdateAdmissionNumberPayload){
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT);
		fetchStudentDetails(instituteId, academicSessionId,  new HashSet<>(studentUpdateAdmissionNumberPayload.getAdmissionNumbers().values()), "New Admission Number provided is already assigned to student(s).");
		List<Student> studentList = fetchStudentDetails(instituteId, academicSessionId, studentUpdateAdmissionNumberPayload.getAdmissionNumbers().keySet(), "Student Details Not Found");
		StudentManagementUpdateFieldPayload studentManagementUpdateFieldPayload = createStudentManagementUpdateFieldPayload(studentList, studentUpdateAdmissionNumberPayload.getAdmissionNumbers());
		return updateClassStudentsFieldData(instituteId, academicSessionId, null, "", userId, studentManagementUpdateFieldPayload, false);
	}
	public Student getStudentByAcademicSessionAdmissionNumber(int instituteId, int academicSessionId, String admissionNumber) {
		List<Student> studentList = getStudentByAcademicSessionAdmissionNumber(instituteId, academicSessionId, new HashSet<>(Arrays.asList(admissionNumber)));
		if(CollectionUtils.isEmpty(studentList)) {
			return null;
		}
		return studentList.get(0);
	}

	public StudentManagementUpdateFieldPayload createStudentManagementUpdateFieldPayload(List<Student> studentList, Map<String, String> admissionNumberMap){
		
		if (CollectionUtils.isEmpty(admissionNumberMap)) {
			logger.error("Invalid admission number map");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid admission number map"));
		}
		List<StudentManagementField> studentManagementFieldsList = new ArrayList<>();
		studentManagementFieldsList.add(StudentManagementField.ADMISSION_NUMBER);
		List<StudentManagementUpdateFieldStudentData> studentManagementUpdateFieldStudentDataList = new ArrayList<>();
		for(Student student : studentList){
			List<StudentManagementUpdateFieldValue> studentManagementUpdateFieldValuesList = new ArrayList<>();
			StudentManagementUpdateFieldValue studentManagementUpdateFieldValue = new StudentManagementUpdateFieldValue(StudentManagementField.ADMISSION_NUMBER, admissionNumberMap.get(student.getStudentBasicInfo().getAdmissionNumber()));
			studentManagementUpdateFieldValuesList.add(studentManagementUpdateFieldValue);
			StudentManagementUpdateFieldStudentData studentManagementUpdateFieldStudentData = new StudentManagementUpdateFieldStudentData(student.getStudentId(), studentManagementUpdateFieldValuesList);
			studentManagementUpdateFieldStudentDataList.add(studentManagementUpdateFieldStudentData);
		}
		if(CollectionUtils.isEmpty(studentManagementUpdateFieldStudentDataList)){
			logger.error("Student Update Data List Not Found");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Unable to Update Admission Number"));
		}
		
		return new StudentManagementUpdateFieldPayload(StudentManagementDataType.BASIC_DATA, studentManagementFieldsList, studentManagementUpdateFieldStudentDataList); 
	}

	public List<Student> getStudentByAcademicSessionAdmissionNumber(int instituteId, int academicSessionId, Set<String> admissionNumberSet) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session id."));
		}
		if(CollectionUtils.isEmpty(admissionNumberSet)) {
			logger.error("Admission Number set can't be empty!");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Admission Number set can't be empty!"));
		}
		return studentDao.getStudentByAcademicSessionAdmissionNumber(instituteId, academicSessionId, admissionNumberSet);
	}

	public List<Student> fetchStudentDetails(int instituteId, int academicSessionId, Set<String> admissionNumberSet, String errorMessage){
		
		if (CollectionUtils.isEmpty(admissionNumberSet)) {
			logger.error("Empty admission number");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Empty admission number data"));
		}
		List<Student> studentList = getStudentByAcademicSessionAdmissionNumber(instituteId, academicSessionId, admissionNumberSet);
		if(!CollectionUtils.isEmpty(studentList) && errorMessage == "New Admission Number provided is already assigned to student(s)."){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, errorMessage));
		}
		if(CollectionUtils.isEmpty(studentList) && errorMessage == "Student Details Not Found"){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, errorMessage));
		}
		return studentList;
	}

	public List<Student> getStudentByDeviceUserId(String deviceUserId, Set<Integer> institutes) {
		return studentDao.getStudentByDeviceUserId(deviceUserId, institutes);
	}

	public UUID updateStudent(StudentPayload studentPayload, int academicSessionId, int instituteId,
			String documentName, FileData uploadPhoto, UUID userId) {

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT);

		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session id."));
		}

		if (studentPayload.getStudentId() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student id."));
		}

		final Student existingStudent = studentDao.getStudentByAcademicSessionStudentId(instituteId, academicSessionId,
				studentPayload.getStudentId());

		if (existingStudent == null) {
			logger.error("Student does not exist with id {} , institute {}, session {}", studentPayload.getStudentId(),
					instituteId, academicSessionId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student does not exists"));
		}

		if (StudentDataUtils.isDOBChanged(existingStudent, studentPayload)) {
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DOB_CHANGE);
		}

		validateUpdateStudent(instituteId, academicSessionId,
				existingStudent.getStudentAcademicSessionInfoResponse().getStandard().getStandardId(), studentPayload);

		studentPayload
				.setStandardId(existingStudent.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings
				.getMetaDataPreferences(studentPayload.getInstituteId());

		final UUID studentId = studentDao.updateStudent(studentPayload, academicSessionId, metaDataPreferences);

		/**
		 * Student info is updated. Add required audit logs
		 */
		if (studentId != null) {
			studentAuditLogWriter.addDOBUpdateLog(instituteId, userId, existingStudent, studentPayload);
		}

		if (uploadPhoto != null) {
			// upload photo of student
			uploadStudentProfileImage(instituteId, documentName, uploadPhoto, studentId);
		}

//		if(!CollectionUtils.isEmpty(studentPayload.getCollectedStudentDocumentType())) {
			if(!updateCollectedDocuments(instituteId, studentId, studentPayload.getCollectedStudentDocumentType())) {
				logger.error("Error while updating collected document status for student {}", studentId);
			}
//		}

		return studentId;

	}

	/**
	 * if we have an entry in student document what means the document is collected by institute.
	 * DocumentNotUploaded is for checking if document was uploaded in our portal or not.
	 * @param studentId
	 * @param collectedStudentDocumentType
	 * @return
	 */
	public boolean updateCollectedDocuments(int instituteId, UUID studentId,
											 List<StudentDocumentType> collectedStudentDocumentType) {

		final Student student = studentDao.getStudent(studentId);
		List<Document<StudentDocumentType>> studentDocumentTypeList = student.getStudentDocuments();

		Map<StudentDocumentType, Document<StudentDocumentType>> studentDocumentTypeMap = new HashMap<>();
//		List<UUID> deletedDocumentIds = new ArrayList<>();
		if(!CollectionUtils.isEmpty(studentDocumentTypeList)) {
			for(Document<StudentDocumentType> studentDocument : studentDocumentTypeList) {
				if(studentDocument == null) {
					continue;
				}
				if(!CollectionUtils.isEmpty(collectedStudentDocumentType) && !collectedStudentDocumentType.contains(studentDocument.getDocumentType())) {
					/**
					 * here the expectation is that only document whose document id is null
					 * can be marked as not uploaded.
					 */
					if(studentDocument.isDocumentNotUploaded()) {
						continue;
					}
				}
//				if(!collectedStudentDocumentType.contains(studentDocument.getDocumentType())) {
//					if(studentDocument.getDocumentId() != null) {
//						deletedDocumentIds.add(studentDocument.getDocumentId());
//					}
//					continue;
//				}
				studentDocumentTypeMap.put(studentDocument.getDocumentType(), studentDocument);
			}
		}

		if(!CollectionUtils.isEmpty(collectedStudentDocumentType)) {
			for (StudentDocumentType documentType : collectedStudentDocumentType) {
				if (documentType == null || documentType == StudentDocumentType.STUDENT_PROFILE_IMAGE_THUMBNAIL || documentType == StudentDocumentType.OTHER) {
					continue;
				}
				Document<StudentDocumentType> studentDocument = studentDocumentTypeMap.get(documentType);
				if (studentDocument != null) {
					studentDocument.setDocumentNotUploaded(studentDocument.getDocumentId() == null);
				} else {
					studentDocumentTypeMap.put(documentType, new Document<StudentDocumentType>(
							documentType, null, null, null, (int) (System.currentTimeMillis() / 1000l
					)));
				}
			}
		}

//		if(!CollectionUtils.isEmpty(deletedDocumentIds)) {
//			for(UUID documentId : deletedDocumentIds) {
//				deleteDocument(instituteId, student, documentId, false);
//			}
//		}

		return studentDao.updateDocuments(studentId, new ArrayList<>(studentDocumentTypeMap.values()));
	}

	public void uploadStudentProfileImage(int instituteId, String documentName, FileData uploadPhoto, UUID studentId) {
		try {
			if(instituteId <= 0) {
				logger.info("Invalid institute id!");
				return;
			}
			if(uploadPhoto == null) {
				logger.info("No image found to upload!");
				return;
			}
			if(studentId == null) {
				logger.info("Invalid student id!");
				return;
			}
			final Student student = studentDao.getStudent(studentId);
			if(student == null) {
				logger.info("No student found!");
				return;
			}
			final Document<StudentDocumentType> studentExistingImage = student.getStudentImage();
			boolean deletedExistingImage = false;
			//going inside only when doc is uploaded
			if (studentExistingImage != null && studentExistingImage.getDocumentId() != null && !studentExistingImage.isDocumentNotUploaded()) {
				logger.info("Deleting existing image {}, for student {}", studentExistingImage.getDocumentId(),
						studentId);
				deleteDocument(instituteId, studentId, studentExistingImage.getDocumentId());
				deletedExistingImage = true;
			}
			/**
			 * Need to fetch student object again as documents needs to be
			 * refreshed
			 */
			if (deletedExistingImage) {
				uploadDocument(instituteId, student.getStudentId(), StudentDocumentType.STUDENT_PROFILE_IMAGE,
						documentName, uploadPhoto);
			} else {
				uploadDocument(instituteId, student, StudentDocumentType.STUDENT_PROFILE_IMAGE, documentName,
						uploadPhoto);
			}

		} catch (final Exception e) {
			logger.error("Unable to upload student image for {}", studentId, e);
		}
	}

	private void validateUpdateStudent(int instituteId, int academicSessionId, UUID standardId,
			StudentPayload studentPayload) {
		if (studentPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student payload."));
		}

		if (studentPayload.getStudentBasicInfo() != null) {
			if (StringUtils.isEmpty(studentPayload.getStudentBasicInfo().getName())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid first name."));
			}

			if (StringUtils.isEmpty(studentPayload.getStudentBasicInfo().getAdmissionNumber())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid admission number."));
			}

			// if
			// (StringUtils.isEmpty(studentPayload.getStudentBasicInfo().getGender().name()))
			// {
			// throw new ApplicationException(
			// new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid
			// gender."));
			// }
		}

		validateStandardAndSection(standardId, studentPayload.getSectionId(), instituteId, academicSessionId);

	}

	private void validateAddStudent(StudentPayload studentPayload) {
		if (studentPayload == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student payload."));
		}

		if (studentPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute."));
		}

		if (studentPayload.getStudentBasicInfo() == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student basic info."));
		}

		if (StringUtils.isEmpty(studentPayload.getStudentBasicInfo().getName())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid first name."));
		}

		if (StringUtils.isEmpty(studentPayload.getStudentBasicInfo().getRegistrationNumber())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid registration number."));
		}

		if (studentPayload.getAdmissionAcademicSession() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid academic session."));
		}

		if (studentPayload.getStudentBasicInfo().getAdmissionDate() != null
				&& studentPayload.getStudentBasicInfo().getAdmissionDate() > DateUtils.now()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid admission date."));
		}
		//
		// if (studentPayload.getStudentBasicInfo().getGender() == null) {
		// throw new ApplicationException(
		// new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid
		// student gender."));
		// }

		validateStandardAndSection(studentPayload.getStandardId(), studentPayload.getSectionId(),
				studentPayload.getInstituteId(), studentPayload.getAdmissionAcademicSession());

	}

	private void validateStandardAndSection(UUID standardId, Integer sectionId, int instituteId,
			int academicSessionId) {

		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid standard."));
		}

		final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		boolean standardFound = false;
		List<StandardSections> standardSectionList = null;
		for (final Standard standard : standards) {
			if (standardId.equals(standard.getStandardId())) {
				standardFound = true;
				standardSectionList = standard.getStandardSectionList();
				break;
			}
		}

		if (!standardFound) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Given standard does not exists."));
		}

		validateSection(sectionId, standardSectionList);
	}

	private void validateSection(Integer sectionId, List<StandardSections> standardSectionList) {
		if (CollectionUtils.isEmpty(standardSectionList) && sectionId != null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT,
					"Given section does not exists for standard."));
		}

		if (!CollectionUtils.isEmpty(standardSectionList)) {
			if (sectionId == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "No section provided for student."));
			}
			boolean sectionFound = false;
			for (final StandardSections standardSections : standardSectionList) {
				if (standardSections.getSectionId() == sectionId) {
					sectionFound = true;
				}
			}
			if (!sectionFound) {
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT,
						"Given section does not exist for standard."));
			}
		}
	}

	public List<Student> getAllStudentWithoutSession(int instituteId) {
		return studentDao.getAllStudentWithoutSession(instituteId);
	}

	public List<Student> getStudentInAllSessions(int instituteId, UUID studentId) {
		return studentDao.getStudentInAllSessions(instituteId, studentId);
	}

	public List<Student> descendingSortedStudentDetailsInAllSession(int instituteId, UUID studentId) {
		List<Student> studentList = getStudentInAllSessions(instituteId, studentId);
		if(CollectionUtils.isEmpty(studentList)) {
			return null;
		}
		Collections.sort(studentList, new Comparator<Student>() {
			@Override
			public int compare(Student s1, Student s2) {
				int compare = s2.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartYear()
						- s1.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartYear();
				if(compare != 0) {
					return compare;
				}
				return s2.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartMonth().getValue()
						- s1.getStudentAcademicSessionInfoResponse().getAcademicSession().getStartMonth().getValue();
			}
		});
		return studentList;
	}

	public Set<Integer> getAllAcademicSessionIdsOfStudent(int instituteId, UUID studentId){
		List<Student> students = descendingSortedStudentDetailsInAllSession(instituteId, studentId);
		Set<Integer> studentAcademicSessionIds = new HashSet<>();
		for(Student student : students){
			AcademicSession session = student.getStudentAcademicSessionInfoResponse().getAcademicSession();
			studentAcademicSessionIds.add(session.getAcademicSessionId());
		}
		return studentAcademicSessionIds;
	}
	public List<Student> getStudentsInAcademicSession(int instituteId, int academicSessionId, List<StudentStatus> studentStatusList) {
		return searchStudentsInAcademicSesison(instituteId, null, academicSessionId, studentStatusList, null, null, null, null, null, null, null)
				.getResult();
	}
	public List<Student> getStudentsInAcademicSession(int instituteId, int academicSessionId) {
		return searchStudentsInAcademicSesison(instituteId, null, academicSessionId, null, null, null, null, null, null, null, null)
				.getResult();
	}

	public SearchResultWithPagination<Student> searchRelievedStudentsInAcademicSession(int instituteId, String searchText,
											   int academicSessionId, Integer offset, Integer limit,
											   String includeUserStatus, String requiredStandardsCSV, Boolean tcGenerated) {
		Set<UUID> standardIds = new HashSet<>();
		Set<Integer> sectionIdSet = new HashSet<>();
		StandardUtils.getStandardSectionSet(requiredStandardsCSV, standardIds, sectionIdSet);
		return studentDao.searchRelievedStudentsInAcademicSession(searchText,
				instituteId, academicSessionId, offset, limit, includeUserStatus, standardIds, sectionIdSet, tcGenerated);
	}

	public SearchResultWithPagination<Student> searchStudentsInAcademicSesison(int instituteId, String searchText,
			int academicSessionId, List<StudentStatus> studentStatusList, Integer offset, Integer limit,
			String includeUserStatus, String requiredStandardsCSV, Boolean isHosteller, Set<TaggedActions> taggedActionsSet, Set<UUID> hostelIdSet) {
		if (offset == null || offset < 0 || limit == null || limit <= 0) {
			offset = 0;
			limit = Integer.MAX_VALUE;
		}
		if (instituteId <= 0 || academicSessionId <= 0) {
			return null;
		}
		if (CollectionUtils.isEmpty(studentStatusList)) {
			studentStatusList = Arrays.asList(StudentStatus.ENROLLED);
		}

		Set<UUID> standardIds = new HashSet<>();
		Set<Integer> sectionIdSet = new HashSet<>();
		StandardUtils.getStandardSectionSet(requiredStandardsCSV, standardIds, sectionIdSet);

		return studentDao.searchStudentsInAcademicSesison(searchText,
				instituteId, academicSessionId, studentStatusList, offset, limit, includeUserStatus, standardIds, sectionIdSet, isHosteller, taggedActionsSet, hostelIdSet);
	}

	/**
	 * Returns StudentLite object with relevant data only
	 *
	 * @param instituteId
	 * @param searchText
	 * @param academicSessionId
	 * @param studentStatusList
	 * @param offset
	 * @param limit
	 * @return
	 */
	public SearchResultWithPagination<StudentLite> liteSearchStudentsInAcademicSesison(int instituteId,
			String searchText, int academicSessionId, List<StudentStatus> studentStatusList, Integer offset,
			Integer limit, String includeUserStatus, Boolean isHosteller) {
		final SearchResultWithPagination<Student> studentResultWithPagination = searchStudentsInAcademicSesison(
				instituteId, searchText, academicSessionId, studentStatusList, offset, limit, includeUserStatus, null, isHosteller, null, null);

		if (studentResultWithPagination == null) {
			return null;
		}
		if (CollectionUtils.isEmpty(studentResultWithPagination.getResult())) {
			return new SearchResultWithPagination<>(studentResultWithPagination.getPaginationInfo(), new ArrayList<>());
		}
		final List<StudentLite> studentLiteList = new ArrayList<>();
		for (final Student student : studentResultWithPagination.getResult()) {
			studentLiteList.add(Student.getStudentLite(student));
		}

		return new SearchResultWithPagination<>(studentResultWithPagination.getPaginationInfo(), studentLiteList);
	}

	public List<StudentSearchEntry> liteV2SearchStudentsInAcademicSession(int instituteId, int academicSessionId,
																		  String searchText, Set<StudentStatus> studentStatusList) {
		if (instituteId <= 0 || academicSessionId <= 0 || CollectionUtils.isEmpty(studentStatusList)) {
			logger.error("Invalid instituteId {}, academicSessionId {}, studentStatusList {}", instituteId, academicSessionId, studentStatusList);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}

		if (StringUtils.isBlank(searchText)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Give at least one character to search"));
		}

		return studentDao.liteV2SearchStudentsInAcademicSession(instituteId, academicSessionId, searchText, studentStatusList);
	}

	private List<Student> getStudentListAfterClassFiltering(List<Student> studentList, String requiredStandardsCSV) {
		if(studentList == null || StringUtils.isBlank(requiredStandardsCSV)) {
			return studentList;
		}
		final Map<UUID, List<Integer>> requiredStandards = StandardUtils.convertToRequiredStandardsWithSection(requiredStandardsCSV);
		if(CollectionUtils.isEmpty(requiredStandards)) {
			return studentList;
		}
		List<Student> finalStudentList = new ArrayList<>();
		for (final Student student : studentList) {
			final UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
			final Integer sectionId = CollectionUtils.isEmpty(student.getStudentAcademicSessionInfoResponse().getStandard()
					.getStandardSectionList()) ? 0 : student.getStudentAcademicSessionInfoResponse().getStandard()
					.getStandardSectionList().get(0).getSectionId();
			if (!MapUtils.isEmpty(requiredStandards) && !requiredStandards.containsKey(standardId)) {
				continue;
			}
			if (!CollectionUtils.isEmpty(requiredStandards.get(standardId)) &&
					!requiredStandards.get(standardId).contains(sectionId)) {
				continue;
			}
			finalStudentList.add(student);
		}
		return finalStudentList;
	}

	public List<Student> searchStudentsWithoutAcademicSesison(int instituteId, String searchText,
			StudentStatus studentStatus, String requiredStandards) {
		if (instituteId <= 0) {
			return null;
		}
		return getStudentListAfterClassFiltering(
				studentDao.searchStudentsWithoutAcademicSesison(searchText, instituteId, studentStatus), requiredStandards);
	}

	public List<StudentLite> searchStudentsWithoutAcademicSesisonLite(int instituteId, String searchText,
															  StudentStatus studentStatus) {
		List<Student> studentList = searchStudentsWithoutAcademicSesison(instituteId, searchText, studentStatus, null);
		if(CollectionUtils.isEmpty(studentList)) {
			return null;
		}
		List<StudentLite> studentLiteList = new ArrayList<>();
		for(Student student : studentList) {
			if(student == null) {
				continue;
			}
			studentLiteList.add(Student.getStudentLite(student));
		}
		return studentLiteList;
	}

	public List<Student> searchStudentsWithoutAcademicSesison(Set<Integer> instituteIds, String searchText,
			Set<StudentStatus> studentStatusSet) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			return new ArrayList<>();
		}
		return studentDao.searchStudentsWithoutAcademicSesison(searchText, instituteIds, studentStatusSet);
	}

	public List<Student> getClassStudents(int instituteId, int academicSessionId, UUID standardId,
			Set<Integer> sectionIds) {
		if (instituteId <= 0 || academicSessionId <= 0 || standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request information."));
		}
		
		return sortStudentList(studentDao.getClassStudents(instituteId, academicSessionId, 
				new HashSet<>(Arrays.asList(standardId)), sectionIds, new HashSet<>(Arrays.asList(StudentStatus.ENROLLED))));
	}

	public List<Student> getClassStudents(int instituteId, int academicSessionId, UUID standardId) {
		if (instituteId <= 0 || academicSessionId <= 0 || standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request information."));
		}
		return sortStudentList(studentDao.getClassStudents(instituteId, academicSessionId, 
				new HashSet<>(Arrays.asList(standardId))));

	}

	public List<Student> getStudentsByStandardIds(int instituteId, int academicSessionId, Set<UUID> standardIds, Set<StudentStatus> statusSet) {
		if (instituteId <= 0 || academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request information."));
		}
		return sortStudentList(studentDao.getClassStudents(instituteId, academicSessionId, standardIds, statusSet));
	}

	private List<Student> sortStudentList(List<Student> students) {
		if(CollectionUtils.isEmpty(students)) {
			return null;
		}
		/**
		 * Make sure sort header course also
		 */
		Collections.sort(students, new Comparator<Student>() {
			@Override
			public int compare(Student s1, Student s2) {
				int standard1 = s1.getStudentAcademicSessionInfoResponse().getStandard().getLevel();
				int standard2 = s2.getStudentAcademicSessionInfoResponse().getStandard().getLevel();
				int standardCompare = standard1-standard2;
				if(standardCompare != 0){
					return standardCompare;
				}
				final List<StandardSections> section1 = s1.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
				final List<StandardSections> section2 = s2.getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
				if(!CollectionUtils.isEmpty(section1) && !CollectionUtils.isEmpty(section2) && !StringUtils.isBlank(section1.get(0).getSectionName())
						&& !StringUtils.isBlank(section2.get(0).getSectionName())){
					final int sectionCompare = section1.get(0).getSectionName().compareToIgnoreCase(section2.get(0).getSectionName());
					if(sectionCompare != 0){
						return sectionCompare;
					}
				}

				return s1.getStudentBasicInfo().getName().compareTo(s2.getStudentBasicInfo().getName());
//				final List<StandardSections> section1 = s1.getStudentAcademicSessionInfoResponse().getStandard()
//						.getStandardSectionList();
//				final List<StandardSections> section2 = s2.getStudentAcademicSessionInfoResponse().getStandard()
//						.getStandardSectionList();
//
//				if (!CollectionUtils.isEmpty(section1) && !CollectionUtils.isEmpty(section2)
//						&& !StringUtils.isBlank(section1.get(0).getSectionName())
//							&& !StringUtils.isBlank(section2.get(0).getSectionName())) {
//					final int sectionCompare = section1.get(0).getSectionName()
//							.compareToIgnoreCase(section2.get(0).getSectionName());
//					if (sectionCompare != 0) {
//						return sectionCompare;
//					}
//				}
//
//				return s1.getStudentBasicInfo().getName().compareToIgnoreCase(s2.getStudentBasicInfo().getName());
			}

		});
		return students;
	}

	public List<Student> getClassStudents(int instituteId, int academicSessionId, final Set<UUID> standardIds) {
		if (instituteId <= 0 || academicSessionId <= 0 || standardIds == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request information."));
		}
		return studentDao.getClassStudents(instituteId, academicSessionId, standardIds);
	}

	public Student getStudentByLatestAcademicSession(UUID studentId) {
		return studentDao.getStudentByLatestAcademicSession(studentId, Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING,
				StudentStatus.RELIEVED, StudentStatus.NSO));
	}

	public Student getStudentByLatestAcademicSession(UUID studentId, List<StudentStatus> studentStatusList) {
		return studentDao.getStudentByLatestAcademicSession(studentId, studentStatusList);
	}

	public boolean relieveStudent(int instituteId, UUID studentId, StudentStatus studentStatus,
			StudentRelievePayload studentRelievePayload, UUID userId) {
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.RELIEVE_STUDENT);
		if (studentStatus != StudentStatus.RELIEVED && studentStatus != StudentStatus.NSO) {
			logger.error("Invalid status change {} in relieving  student {}, institute {}", studentStatus, studentId,
					instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid status change."));
		}
		if (studentRelievePayload == null || studentRelievePayload.getRelieveDate() == null
				|| studentRelievePayload.getRelieveDate() <= 0) {
			logger.error("Invalid relieve date for status change {}, student {}, institute {} ", studentStatus,
					studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid relieve date."));
		}

		return studentDao.relieveStudent(studentId, instituteId, studentStatus, studentRelievePayload,
				getStudentSessionStatusDetailsList(instituteId, studentId,
						studentStatus, studentRelievePayload));
	}

	public List<StudentSessionStatusDetails> getStudentSessionStatusDetailsList(int instituteId, UUID studentId,
			StudentStatus studentStatus, StudentRelievePayload studentRelievePayload) {
		List<Student> studentList = descendingSortedStudentDetailsInAllSession(instituteId, studentId);
		if(CollectionUtils.isEmpty(studentList)) {
			return new ArrayList<>();
		}
		List<StudentSessionStatusDetails> studentSessionStatusDetailsList = getStudentSessionStatusDetailsList(
				instituteId, studentId, studentStatus, studentRelievePayload, studentList);
		if(CollectionUtils.isEmpty(studentSessionStatusDetailsList)) {
			logger.error("Invalid student status change {}, student {}, institute {} ", studentStatus,
					studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student status change."));
		}
		return studentSessionStatusDetailsList;
	}

	public List<StudentSessionStatusDetails> getStudentSessionStatusDetailsList(int instituteId, UUID studentId,
												  StudentStatus studentStatus, StudentRelievePayload studentRelievePayload,
																				List<Student> studentList) {
		switch (studentStatus) {
			case ENROLLED:
			case NSO:
				return StudentDataUtils.getStudentSessionStatusDetailsList(null,
						studentList, studentRelievePayload, studentStatus);
			case RELIEVED:
				LinkedHashMap<Integer, Student> studentSessionMap = new LinkedHashMap<>();
				LinkedHashMap<Integer, AcademicSession> sessionMap = new LinkedHashMap<>();
				getStudentSessionMap(studentList, studentSessionMap, sessionMap);
				validateRelievingStudentPayload(instituteId, studentId, studentList, studentRelievePayload, studentSessionMap, sessionMap);
				return getRelievedStudentSessionStatusDetailsList(studentStatus, studentRelievePayload, studentList, sessionMap);
			case ENROLMENT_PENDING:
			case DELETED:
				break;
		}
		return new ArrayList<>();
	}

	private void validateRelievingStudentPayload(int instituteId, UUID studentId, List<Student> studentList, StudentRelievePayload studentRelievePayload,
												 LinkedHashMap<Integer, Student> studentSessionMap, LinkedHashMap<Integer, AcademicSession> sessionMap) {

		validateStudentRelievePayload(instituteId, studentId, studentRelievePayload);

		Map<RelievedMetadataVariables, String> relievedMetadataVariablesStringMap = studentRelievePayload.getRelievedMetadata();
		int lastAcademicSessionId = Integer.parseInt(relievedMetadataVariablesStringMap.get(RelievedMetadataVariables.LAST_ACTIVE_SESSION));

		Student student = studentSessionMap.get(lastAcademicSessionId);
		if(student == null) {
			logger.error("Student not present in selected last active session {}.", lastAcademicSessionId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT,
							"Student not present in selected last active session."));
		}

		Map<Integer, List<FeePaymentDetails>> allSessionPaidFeeDetails = feePaymentTransactionDao.getPaidFeeDetails(instituteId, studentId);
		for(Entry<Integer, AcademicSession> academicSessionEntry : sessionMap.entrySet()) {
			int academicSessionId = academicSessionEntry.getKey();
			if(academicSessionId == lastAcademicSessionId) {
				break;
			}
			AcademicSession academicSession = academicSessionEntry.getValue();
			if(!CollectionUtils.isEmpty(allSessionPaidFeeDetails.get(academicSessionId))) {
				logger.error("Future session fees of session {} is collected for student {} .", academicSessionId, studentId);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT,
								academicSession.getShortYearDisplayName()
								+ " session fees is collected for this student. Please select "
								+ academicSession.getShortYearDisplayName() + " as last active session."));
			}
		}

	}

	private void validateStudentRelievePayload(int instituteId, UUID studentId, StudentRelievePayload studentRelievePayload) {
		if(studentRelievePayload == null) {
			logger.error("Invalid student relieve payload for student {}, institute {} ", studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student relieve payload."));
		}

		if(studentRelievePayload.getRelieveDate() == null || studentRelievePayload.getRelieveDate() <= 0) {
			logger.error("Invalid relieve date for student {}, institute {} ", studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid relieve date."));
		}

		//TODO:uncomment once data migration is completed in production
//		if(StringUtils.isBlank(studentRelievePayload.getRelieveReason())) {
//			logger.error("Invalid relieve reason for student {}, institute {} ", studentId, instituteId);
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid relieve reason."));
//		}

		if(CollectionUtils.isEmpty(studentRelievePayload.getRelievedMetadata())) {
			logger.error("Invalid relieve metadata for student {}, institute {} ", studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid relieve metadata."));
		}

		Map<RelievedMetadataVariables, String> relievedMetadataVariablesStringMap = studentRelievePayload.getRelievedMetadata();

		String lastActiveSessionStr = relievedMetadataVariablesStringMap.get(RelievedMetadataVariables.LAST_ACTIVE_SESSION);
		if(StringUtils.isBlank(lastActiveSessionStr)) {
			logger.error("Invalid last active session for student {}, institute {} ", studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid last active session."));
		}
		int lastAcademicSessionId = Integer.parseInt(lastActiveSessionStr);
		if(lastAcademicSessionId <= 0) {
			logger.error("Invalid last active session for student {}, institute {} ", studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid last active session."));
		}
	}

	private void getStudentSessionMap(List<Student> studentList, LinkedHashMap<Integer, Student> studentSessionMap,
																 LinkedHashMap<Integer, AcademicSession> sessionMap) {
		for(Student student : studentList) {
			studentSessionMap.put(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
					student);
			sessionMap.put(student.getStudentAcademicSessionInfoResponse().getAcademicSession().getAcademicSessionId(),
					student.getStudentAcademicSessionInfoResponse().getAcademicSession());
		}
	}

	public List<StudentSessionStatusDetails> getRelievedStudentSessionStatusDetailsList(StudentStatus studentStatus, StudentRelievePayload studentRelievePayload,
																						List<Student> studentList, LinkedHashMap<Integer, AcademicSession> sessionMap) {
		Map<RelievedMetadataVariables, String> relievedMetadataVariablesStringMap = studentRelievePayload.getRelievedMetadata();
		int lastAcademicSessionId = Integer.parseInt(relievedMetadataVariablesStringMap.get(RelievedMetadataVariables.LAST_ACTIVE_SESSION));
		AcademicSession lastAcademicSession = sessionMap.get(lastAcademicSessionId);
		return StudentDataUtils.getStudentSessionStatusDetailsList(lastAcademicSession,
				studentList, studentRelievePayload, studentStatus);
	}

	public boolean enrollStudent(int instituteId, UUID studentId, StudentStatus studentStatus, UUID userId) {
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ENROLL_STUDENT);
		if (studentStatus != StudentStatus.ENROLLED) {
			logger.error("Invalid status change {} in enrolling  student {}, institute {}", studentStatus, studentId,
					instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid status change."));
		}

		List<StudentSessionStatusDetails> studentSessionStatusDetailsList = getStudentSessionStatusDetailsList(instituteId,
				studentId, studentStatus, null);
		if(CollectionUtils.isEmpty(studentSessionStatusDetailsList)) {
			logger.error("Invalid student status change {}, student {}, institute {} ", studentStatus,
					studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student status change."));
		}

		return studentDao.enrollStudent(studentId, instituteId, studentStatus, studentSessionStatusDetailsList);
	}

	public List<Document<StudentDocumentType>> deleteDocument(int instituteId, UUID studentId, UUID documentId) {
		final Student student = studentDao.getStudent(studentId);
		return deleteDocument(instituteId, student, documentId);
	}

	public List<Document<StudentDocumentType>> deleteDocument(int instituteId, Student student, UUID documentId) {

		return deleteDocument(instituteId, student, documentId, true);
	}

	public List<Document<StudentDocumentType>> deleteDocument(int instituteId, Student student, UUID documentId, boolean updateStudentDocumentsInDB) {
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student does not exists"));
		}

		if (documentId == null || instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final UUID studentId = student.getStudentId();
		final List<Document<StudentDocumentType>> studentDocuments = student.getStudentDocuments();
		if (CollectionUtils.isEmpty(studentDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		final Iterator<Document<StudentDocumentType>> iterator = studentDocuments.iterator();
		boolean deleted = false;
		boolean isStudentProfileImage = false;
		while (iterator.hasNext()) {
			final Document<StudentDocumentType> studentDocument = iterator.next();
			if (documentId.equals(studentDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(studentId, studentDocument, studentDocument.getDocumentType().isThumbnail());
				if (documentManager.deleteDocument(instituteId, s3Path)) {
					iterator.remove();
					deleted = true;
					if(studentDocument.getDocumentType() == StudentDocumentType.STUDENT_PROFILE_IMAGE) {
						isStudentProfileImage = true;
					}
				}
				break;
			}
		}
		if (!deleted) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}

		if(isStudentProfileImage) {
			while (iterator.hasNext()) {
				final Document<StudentDocumentType> studentDocument = iterator.next();
				if (studentDocument.getDocumentType() == StudentDocumentType.STUDENT_PROFILE_IMAGE_THUMBNAIL) {
					final String s3Path = buildDocumentPath(studentId, studentDocument, studentDocument.getDocumentType().isThumbnail());
					if (documentManager.deleteDocument(instituteId, s3Path)) {
						iterator.remove();
					}
					break;
				}
			}
		}

		return updateStudentDocumentsInDB ? studentDao.updateDocuments(studentId, studentDocuments) ? studentDocuments : null : studentDocuments;
	}

	public DownloadDocumentWrapper<Document<StudentDocumentType>> downloadDocument(int instituteId, UUID studentId,
			UUID documentId) {
		if (documentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Invalid document Information"));
		}
		final Student student = studentDao.getStudent(studentId);
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student does not exists"));
		}

		final List<Document<StudentDocumentType>> studentDocuments = student.getStudentDocuments();
		if (CollectionUtils.isEmpty(studentDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StudentDocumentType> studentDocument : studentDocuments) {
			if (documentId.equals(studentDocument.getDocumentId())) {
				final String s3Path = buildDocumentPath(studentId, studentDocument, studentDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(studentDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public DownloadDocumentWrapper<Document<StudentDocumentType>> downloadStudentImage(int instituteId,
			UUID studentId) {
		final Student student = studentDao.getStudent(studentId);
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student does not exists"));
		}

		return downloadStudentImage(instituteId, student);
	}

	public DownloadDocumentWrapper<Document<StudentDocumentType>> downloadStudentImage(int instituteId,
			Student student) {
		final List<Document<StudentDocumentType>> studentDocuments = student.getStudentDocuments();
		if (CollectionUtils.isEmpty(studentDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StudentDocumentType> studentDocument : studentDocuments) {
			if (studentDocument.getDocumentType() == StudentDocumentType.STUDENT_PROFILE_IMAGE) {
				final String s3Path = buildDocumentPath(student.getStudentId(), studentDocument, studentDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(studentDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public List<Document<StudentDocumentType>> uploadDocument(int instituteId, UUID studentId,
			StudentDocumentType studentDocumentType, String documentName, FileData document) {
		final Student student = studentDao.getStudent(studentId);
		if (student == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student does not exists"));
		}
		return uploadDocument(instituteId, student, studentDocumentType, documentName, document);
	}

	public List<Document<StudentDocumentType>> uploadDocument(int instituteId, Student student,
			StudentDocumentType studentDocumentType, String documentName, FileData document) {
		final UUID studentId = student.getStudentId();

		List<Document<StudentDocumentType>> studentDocuments = student.getStudentDocuments();
		if (CollectionUtils.isEmpty(studentDocuments)) {
			studentDocuments = new ArrayList<>();
		}

		if (studentDocuments.size() == DocumentUtils.FILE_COUNT_LIMIT) {
			logger.error("Student already have " + DocumentUtils.FILE_COUNT_LIMIT
					+ " documents uploaded. Please delete some to upload more.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_COUNT_OF_DOCUMENT,
					"Student already have " + DocumentUtils.FILE_COUNT_LIMIT
							+ " documents uploaded. Please delete some to upload more."));
		}

		final double length = document.getContent().length / DocumentUtils.ONE_KB;
		if (length > DocumentUtils.FILE_SIZE_LIMIT) {
			logger.error("Size {} Greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb", length);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_SIZE_OF_DOCUMENT,
					"Size Of document cannot be greater than " + DocumentUtils.FILE_SIZE_LIMIT + " kb"));
		}

		if (!DocumentUtils.validStudentDocument(student, studentDocumentType, documentName, document)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Document already exists with give type and name"));
		}

		/**
		 * upload document
		 */
		Document<StudentDocumentType> uploadedDocument = uploadDocument(instituteId, document, studentDocumentType, documentName, studentId, false);

		List<Document<StudentDocumentType>> finalStudentDocuments = getFinalStudentDocuments(studentDocuments, uploadedDocument);

		if(studentDocumentType != StudentDocumentType.STUDENT_PROFILE_IMAGE) {
			return studentDao.updateDocuments(studentId, finalStudentDocuments) ? finalStudentDocuments : null;
		}

		/**
		 * upload student profile image thumbnail
		 */
		FileData thumbnailDocument = ImageUtils.createThumbnailInBytes(document);
		if(thumbnailDocument != null) {
			String thumbnailDocumentName = StudentDocumentType.STUDENT_PROFILE_IMAGE_THUMBNAIL
					.getDisplayName().replace(" ", "_").toLowerCase();
			Document<StudentDocumentType> uploadedThumbnail = uploadDocument(instituteId, thumbnailDocument,
					StudentDocumentType.STUDENT_PROFILE_IMAGE_THUMBNAIL, thumbnailDocumentName, studentId, true);

			studentDocuments = new ArrayList<>(finalStudentDocuments);
			finalStudentDocuments = getFinalStudentDocuments(studentDocuments, uploadedThumbnail);
		}

		return studentDao.updateDocuments(studentId, finalStudentDocuments) ? finalStudentDocuments : null;
	}

	/**
	 * managing student document like this to avoid duplicate entries of same document type.
	 * which occur when we add a document as collected and then upload it later on.
	 * https://embrate.atlassian.net/browse/PD-3310
	 * @param existingStudentDocuments
	 * @param uploadedDocument
	 * @return
	 */
	private List<Document<StudentDocumentType>> getFinalStudentDocuments(List<Document<StudentDocumentType>> existingStudentDocuments,
																		 Document<StudentDocumentType> uploadedDocument) {
		List<Document<StudentDocumentType>> finalStudentDocuments = new ArrayList<>();
		//Not adding the document with new document type, adding rest all.
		for(Document<StudentDocumentType> studentDocument : existingStudentDocuments) {
			if(studentDocument.getDocumentType() != StudentDocumentType.OTHER
					&& studentDocument.getDocumentType() == uploadedDocument.getDocumentType()) {
				continue;
			}
			finalStudentDocuments.add(studentDocument);
		}
		//Adding new uploaded document
		finalStudentDocuments.add(uploadedDocument);
		return finalStudentDocuments;
	}

	private Document<StudentDocumentType> uploadDocument(int instituteId, FileData fileData,
														 StudentDocumentType studentDocumentType, String documentName, UUID studentId,
														 boolean isThumbnail) {
		final UUID documentId = UUID.randomUUID();
		String fileExtension = FilenameUtils.getExtension(fileData.getFileName());
		Document<StudentDocumentType> newDocument = new Document<>(studentDocumentType, documentName, documentId,
				fileExtension, (int) (System.currentTimeMillis() / 1000l));
		String s3Path = buildDocumentPath(studentId, newDocument, isThumbnail);
		boolean documentUploaded = documentManager.uploadDocument(s3Path, instituteId, fileData);

		if (!documentUploaded) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT,
					"Unable to upload document. Please try again"));
		}
		return newDocument;
	}

	private String buildDocumentPath(UUID studentId, Document<StudentDocumentType> studentDocument, boolean isThumbnail) {
		String thumbnailPath = "";
		if(isThumbnail) {
			thumbnailPath = HYPHEN + THUMBNAIL_SUFFIX;
		}
		final StringBuilder s3Path = new StringBuilder();
		s3Path.append(UserType.STUDENT).append(DocumentUtils.S3_FILE_PATH_DELIMITER).append(studentId)
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append(STUDENT_COMMON_DOCUMENT_DIRECTORY_NAME)
				.append(DocumentUtils.S3_FILE_PATH_DELIMITER).append(studentDocument.getDocumentId()).append(thumbnailPath)
				.append(".").append(studentDocument.getFileExtension());
		return s3Path.toString();
	}

	public ReportOutput exportStudentData(int instituteId, int academicSessionId) {
		// TODO Auto-generated method stub
		return null;
	}

	public boolean admitStudentNonAtomic(int instituteId, UUID studentId, String admissionNumber, Integer admissionDate, UUID houseId, UUID siblingGroupId) {
		if (instituteId <= 0 || studentId == null || StringUtils.isBlank(admissionNumber)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid request information."));
		}
		final MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);

		List<StudentSessionStatusDetails> studentSessionStatusDetailsList = getStudentSessionStatusDetailsList(instituteId,
				studentId, StudentStatus.ENROLLED, null);
		if(CollectionUtils.isEmpty(studentSessionStatusDetailsList)) {
			logger.error("Invalid student status change {}, student {}, institute {} ", StudentStatus.ENROLLED,
					studentId, instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student status change."));
		}

		return studentDao.admitStudentNonAtomic(instituteId, studentId, admissionNumber, admissionDate,
				metaDataPreferences.isAdmissionCounter(), houseId, siblingGroupId, studentSessionStatusDetailsList, StudentStatus.ENROLLED);
	}

	/**
	 * Finds the immediate next standard of given current standard
	 *
	 * @param currentStandard
	 * @param standards
	 * @return
	 */
	public Standard getNextPropmotionStandard(Standard currentStandard, List<Standard> standards) {
		Collections.sort(standards);
		for (final Standard standard : standards) {
			if (currentStandard.getLevel() >= standard.getLevel()) {
				continue;
			}
			if (currentStandard.getStandardName().equalsIgnoreCase(standard.getStandardName())) {
				continue;
			}

			if (currentStandard.getStream() == null || currentStandard.getStream() == Stream.NA) {
				return standard;
			}
			if (currentStandard.getStream() == standard.getStream()) {
				return standard;
			}
		}
		return null;
	}

	public Standard getNextPropmotionStandard(int instituteId, int academicSessionId, UUID studentId) {
		final Student student = getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		// final Student student = getStudentByLatestAcademicSession(studentId,
		// Arrays.asList(StudentStatus.ENROLLED));
		if (student == null) {
			logger.error("No student found with id = {}", studentId);
			return null;
		}
		return getNextPropmotionStandard(instituteId, student);
	}

	public Standard getNextPropmotionStandard(int instituteId, Student student) {
		if (student == null) {
			logger.error("Invalid student");
			return null;
		}
		final Standard currentStandard = student.getStudentAcademicSessionInfoResponse().getStandard();
		final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId);
		return getNextPropmotionStandard(currentStandard, standards);
	}

	public List<Student> searchStudentsInAcademicSesisonWithFilter(int instituteId, Integer academicSessionId,
			Set<StudentStatus> studentStatus, FilterationCriteria filterationCriteria) {
		if (instituteId <= 0) {
			return null;
		}
		
		if (academicSessionId == null || academicSessionId <= 0) {
			return null;
		}
		return studentDao.searchStudentsInAcademicSesisonWithFilter(instituteId, academicSessionId, studentStatus,
				filterationCriteria);
	}

	public List<String> getReligionDetails(int instituteId) {
		return studentDao.getReligionDetails(instituteId);
	}

	public List<Student> searchStudentsWithoutAcademicSesisonWithFilter(int instituteId,
			FilterationCriteria filterationCriteria) {
		if (instituteId <= 0) {
			return null;
		}
		return studentDao.searchStudentsInAcademicSesisonWithFilter(instituteId, null, null, filterationCriteria);
	}

	public StudentCastClassWiseDetails getCastWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		if (academicSessionId == null || academicSessionId <= 0) {
			return null;
		}
		return studentDao.getCastWiseStudentReport(instituteId, academicSessionId);
	}

	public StudentReligionClassWiseDetails getReligionWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		if (academicSessionId == null || academicSessionId <= 0) {
			return null;
		}
		return studentDao.getReligionWiseStudentReport(instituteId, academicSessionId);
	}

	public StudentHouseSummaryDetails getStudentHouseSummaryReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		if (academicSessionId == null || academicSessionId <= 0) {
			return null;
		}
		return studentDao.getStudentHouseSummaryReport(instituteId, academicSessionId);
	}

	public boolean updateStudentSectionDetails(int instituteId,int academicSessionId,UUID standardId,UUID userId,int sectionId,List<Student> studentsList){
		Set<StudentAcademisSessionParameters> studentAcademicSessionParameters = new HashSet<>();
		studentAcademicSessionParameters.add(StudentAcademisSessionParameters.SECTION);
		List<StudentAcademicDetails> studentAcademicDetailsList = new ArrayList<>();
		for (Student student : studentsList) {
			StudentAcademicDetails studentAcademicDetails = new StudentAcademicDetails();
			UUID studentId = student.getStudentId();
			studentAcademicDetails.setStudentId(studentId);
			studentAcademicDetails.setSectionId(sectionId);
			studentAcademicDetailsList.add(studentAcademicDetails);
		}

		StudentAcademicSessionPayload studentAcademicSessionPayload = new StudentAcademicSessionPayload(instituteId,academicSessionId,standardId,studentAcademicSessionParameters,studentAcademicDetailsList);
		return updateStudentAcademicSessionDetails(instituteId,academicSessionId,studentAcademicSessionPayload,userId, false);
	}

	public boolean updateStudentAcademicSessionDetails(int instituteId, int academicSessionId,
			StudentAcademicSessionPayload studentAcademicSessionPayload, UUID userId, boolean fromScript) {
		
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT_SESSION_DETAILS);

		if (studentAcademicSessionPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		
		if (studentAcademicSessionPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		/**
		 * if the call is coming from script we don't want to send data for multiple standards separately,
		 * so standard id will be null in that case and data for multiple standards will be sent.
		 */
		if(!fromScript) {
			if (studentAcademicSessionPayload.getStandardId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid standard id."));
			}
		}

		/**
		 * validate section
		 * https://embrate.atlassian.net/browse/PD-2654
		 */
		if(studentAcademicSessionPayload.getStudentAcademisSessionParametersList().contains(StudentAcademisSessionParameters.SECTION)) {
			if (studentAcademicSessionPayload.getStandardId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid standard."));
			}

			final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
			boolean standardFound = false;
			List<StandardSections> standardSectionList = null;
			for (final Standard standard : standards) {
				if (studentAcademicSessionPayload.getStandardId().equals(standard.getStandardId())) {
					standardFound = true;
					standardSectionList = standard.getStandardSectionList();
					break;
				}
			}

			if (!standardFound) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Given standard does not exists."));
			}
			for(StudentAcademicDetails studentAcademicDetails : studentAcademicSessionPayload.getStudentAcademicDetailsList()) {
				validateSection(studentAcademicDetails.getSectionId(), standardSectionList);
			}
		}

		return studentDao.updateStudentAcademicSessionDetails(studentAcademicSessionPayload);
	}

	public List<StudentSiblingCountDetails> generateStudentsSiblingsDetails(int instituteId,int academicSessionId,Set<StudentStatus> studentStatusSet,Map<UUID, List<Integer>> requiredStandards){
		if (instituteId <= 0) {
			return null;
		}

		List<Student> studentsList =  getStudentsInAcademicSession(instituteId, academicSessionId, new ArrayList<>(studentStatusSet));

		Map<UUID, Integer> siblingsCountMap = new HashMap<>();
		// find the count of the each sibling group id
		for(Student student : studentsList) {
			UUID siblingGroupId = student.getStudentBasicInfo().getSiblingGroupId();
			if(!siblingsCountMap.containsKey(siblingGroupId)){
				siblingsCountMap.put(siblingGroupId,0);
				
			}
			siblingsCountMap.put(siblingGroupId, siblingsCountMap.get(siblingGroupId) + 1);
		}

		List<StudentSiblingCountDetails> studentSiblingDetailsReports = new ArrayList<>();
		for (Student student : studentsList) {
			UUID siblingGroupId = student.getStudentBasicInfo().getSiblingGroupId();
			if(siblingGroupId != null && siblingsCountMap.containsKey(siblingGroupId)) {
				studentSiblingDetailsReports.add(new StudentSiblingCountDetails(student, siblingsCountMap.get(siblingGroupId)));
			} else {
				studentSiblingDetailsReports.add(new StudentSiblingCountDetails(student, 0));
			}
		}


		// filter out the standards needed
		if (!MapUtils.isEmpty(requiredStandards)) {
			Set<UUID> standardIds = requiredStandards.keySet();
			Set<UUID> siblingsIdsSet = new HashSet<>();
			for(StudentSiblingCountDetails studentSiblingDetailsReport : studentSiblingDetailsReports){
				final UUID standardId = studentSiblingDetailsReport.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
				final UUID siblingGroupId = studentSiblingDetailsReport.getStudent().getStudentBasicInfo().getSiblingGroupId();
				if(standardIds.contains(standardId)){
					siblingsIdsSet.add(siblingGroupId);
				}
			}
			List<StudentSiblingCountDetails> studentSiblingDetailsReportFilteredStandardList = new ArrayList<>();
			for(StudentSiblingCountDetails studentSiblingDetailsReport : studentSiblingDetailsReports){
				final UUID standardId = studentSiblingDetailsReport.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
				final UUID siblingGroupId = studentSiblingDetailsReport.getStudent().getStudentBasicInfo().getSiblingGroupId();
				final int siblingCount = studentSiblingDetailsReport.getSiblingsCount();
				final Integer sectionId = CollectionUtils.isEmpty(studentSiblingDetailsReport.getStudent().getStudentAcademicSessionInfoResponse().getStandard()
						.getStandardSectionList()) ? 0 : studentSiblingDetailsReport.getStudent().getStudentAcademicSessionInfoResponse().getStandard()
						.getStandardSectionList().get(0).getSectionId();
				if (!CollectionUtils.isEmpty(requiredStandards.get(standardId)) && !requiredStandards.get(standardId).contains(sectionId)) {
					continue;
				}
				if(requiredStandards.containsKey(standardId) ||(!requiredStandards.containsKey(standardId) && siblingCount>0 && siblingsIdsSet.contains(siblingGroupId) && siblingGroupId!=null)){
					studentSiblingDetailsReportFilteredStandardList.add(studentSiblingDetailsReport);
				}
			}
		
			return studentSiblingDetailsReportFilteredStandardList;
		}
		return studentSiblingDetailsReports;
	}

	public List<StandardSectionGenderCount> getStandardSectionGenderCount(int instituteId, int academicSessionId, Map<UUID, List<Integer>> requiredStandardSectionMap, Set<StudentStatus> studentStatusSet) {
		Set<UUID> standadIdSet = new HashSet<>();
		Set<Integer> sectionsIdsSet = new HashSet<>();
		if(!CollectionUtils.isEmpty(requiredStandardSectionMap)){
			standadIdSet = requiredStandardSectionMap.keySet();
			for (List<Integer> sectionIds : requiredStandardSectionMap.values()) {
				sectionsIdsSet.addAll(sectionIds);
			}
		}
		return studentDao.getInstituteStandardDetailsWithStudentAndGenderCount(instituteId, academicSessionId, standadIdSet, sectionsIdsSet, studentStatusSet);
	}

	public List<Student> getStudentsWithAllAcademicSessionDetailWithFilter(int instituteId, int academicSessionId,
			UUID standardId, Set<Integer> sectionIds, Boolean rte, Set<UserCategory> category,
			Set<Gender> gender, Set<AreaType> areaType, Boolean speciallyAbled, Boolean bpl) {
		
		if (instituteId <= 0) {
			return null;
		}
		if (academicSessionId <= 0) {
			return null;
		}
		return getStudentsOfASession(studentDao.getStudentsWithAllAcademicSessionDetailWithFilter(instituteId,
				rte, category, gender, areaType, speciallyAbled, bpl), academicSessionId, standardId, sectionIds);
	}

	public List<Student> getStudentsOfASession(Map<UUID, Student> studentMap, int academicSessionId, UUID standardId,
			Set<Integer> sectionIds) {
		if(CollectionUtils.isEmpty(studentMap)) {
			return null;
		}

		List<Student> finalStudentList = new ArrayList<Student>();
		for(Entry<UUID, Student> studentEntrySet : studentMap.entrySet()) {
			StudentAcademicSessionInfoResponse studentAcademicSessionInfoResponse = 
					studentEntrySet.getValue().getStudentAcademicSessionInfoResponse();
			if(studentAcademicSessionInfoResponse.getAcademicSession().getAcademicSessionId() == academicSessionId
					&& studentAcademicSessionInfoResponse.getStandard().getStandardId().equals(standardId)) {
				if(CollectionUtils.isEmpty(sectionIds)) {
					finalStudentList.add(studentEntrySet.getValue());
				} else {
					if(sectionIds.contains(studentAcademicSessionInfoResponse.getStandard()
							.getStandardSectionList().get(0).getSectionId())) {
						finalStudentList.add(studentEntrySet.getValue());
					}
				}
				
			}
		}
		return sortStudentList(finalStudentList);
	}
	
	public boolean addAcademicSessionStudentDetails(int instituteId, int academicSession, List<StudentAcademicDetails> studentAcademicDetailsList,
													UUID standardId, Integer sectionId, boolean isNewAdmission) {
		return studentDao.addAcademicSessionStudentDetails(instituteId, academicSession, studentAcademicDetailsList, standardId, sectionId,
				StudentStatus.ENROLLED, isNewAdmission);
	}
	
	public boolean updateStudentStandardAndSection(int instituteId, int academicSessionId,
			UUID standardId, Integer sectionId, UUID studentId) {
		return studentDao.updateStudentStandardAndSection(instituteId, academicSessionId, standardId, sectionId, studentId);
	}

	public Map<UUID, Map<AttendanceStatus, Integer>> getStudentAttendanceDetails(int instituteId,
																				 int academicSessionId, Set<UUID> studentIdSet, Integer startDate, Integer endDate) {
		return attendanceManager.getStudentAttendanceDetails(instituteId, academicSessionId, studentIdSet, startDate, endDate);
	}

	public boolean addSiblings(int instituteId, UUID userId, StudentSiblingPayload studentSiblingPayload, boolean isStudentLevelAction, boolean skipAuth) {

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if(!skipAuth){
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_SIBLINGS_DETAILS);
		}
		validateStudentSiblingPayload(studentSiblingPayload, false, isStudentLevelAction);
		return studentDao.editSiblings(instituteId, studentSiblingPayload, false, isStudentLevelAction);
	}

	public boolean bulkSiblingAssignment(int instituteId, UUID userId, StudentSiblingListsPayload studentSiblingListsPayload, int academicSessionId, boolean skipAuth) {

		if (!skipAuth) {
			if (userId == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
			}
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_SIBLINGS_DETAILS);
		}

		validateStudentSiblingsPayload(studentSiblingListsPayload);

		// Collect all admission numbers in a set for uniqueness
		Set<String> admissionNumberSet = new HashSet<>();
		for (List<String> admissionNumbers : studentSiblingListsPayload.getAdmissionNumbers())
			for (String admissionNumber : admissionNumbers) {
				if (admissionNumberSet.contains(admissionNumber)) {
					throw new ApplicationException(
							new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Duplicate Admission Number : "+ admissionNumber));
				}
				admissionNumberSet.add(admissionNumber);
			}

		List<Student> studentList = getStudentByAcademicSessionAdmissionNumber(instituteId,academicSessionId, admissionNumberSet);

		if (admissionNumberSet.size() != studentList.size()){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please check that all lists we receive have the correct admission numbers."));
		}

		// Map admission numbers to student IDs
		HashMap<String, UUID> admissionNumberToStudentIdMap = new HashMap<>();
		for (Student studentSibling : studentList) {
			admissionNumberToStudentIdMap.put(studentSibling.getStudentBasicInfo().getAdmissionNumber(), studentSibling.getStudentId());
		}

		// Prepare the payload for updating sibling group IDs
		Map<UUID, UUID> studentIdPayload = new HashMap<>();
		for (List<String> siblingGroup : studentSiblingListsPayload.getAdmissionNumbers()) {
			UUID siblingGroupId = UUID.randomUUID();
			for (String admissionNumber : siblingGroup) {
				UUID studentId = admissionNumberToStudentIdMap.get(admissionNumber);
				if (studentId != null) {
					studentIdPayload.put(studentId, siblingGroupId);
				}
			}
		}

		if (!CollectionUtils.isEmpty(studentIdPayload)) {
			return studentDao.updateBulkSiblingAssignment(instituteId, studentIdPayload);
		}
		return false;

	}

	public void validateStudentSiblingsPayload(StudentSiblingListsPayload studentSiblingListsPayload) {
		List<List<String>> admissionNumbersList = studentSiblingListsPayload.getAdmissionNumbers();
		for (List<String> admissionNumbers : admissionNumbersList) {
			if (admissionNumbers.size() < 2){
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Every sibling group must have two or more students."));
			}
			for (String admissionNumber : admissionNumbers) {
				if ((admissionNumber == null) || admissionNumber.trim().isEmpty()) {
					throw new ApplicationException(
							new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Admission number is missing or empty"));
				}
			}
		}

		if (CollectionUtils.isEmpty(admissionNumbersList) || admissionNumbersList.size() <= 1) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select at least two students to create siblings."));
		}
	}


	public void validateStudentSiblingPayload(StudentSiblingPayload studentSiblingPayload, boolean update, boolean isStudentLevelAction) {
		if(studentSiblingPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}

		if(update) {
			if (studentSiblingPayload.getSiblingGroupId() == null) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid sibling group id."));
			}
		}

		if(!isStudentLevelAction) {
			if (CollectionUtils.isEmpty(studentSiblingPayload.getStudentIdList()) || studentSiblingPayload.getStudentIdList().size() <= 1) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select atleast two student to create siblings."));
			}
		} else {
			if (CollectionUtils.isEmpty(studentSiblingPayload.getStudentIdList())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Please select atleast one student to perform action."));
			}
        }
	}

	public boolean updateSiblings(int instituteId, UUID userId, StudentSiblingPayload studentSiblingPayload,
								  boolean isStudentLevelAction) {

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_SIBLINGS_DETAILS);
		validateStudentSiblingPayload(studentSiblingPayload, true, isStudentLevelAction);
		//TODO:check if any student belong to other sibling group
		List<Student> studentList = getStudentsWithoutSession(studentSiblingPayload.getStudentIdList());
		if(CollectionUtils.isEmpty(studentList)) {
			throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Error while fetching students, please try again later"));
		}
		List<UUID> studentIdList = new ArrayList<>();
		for(Student student : studentList) {
			UUID siblingGroupId = student.getStudentBasicInfo().getSiblingGroupId();
			if(siblingGroupId != null && !siblingGroupId.equals(studentSiblingPayload.getSiblingGroupId())) {
				logger.info("Student {} already belong to different sibling group {}.", student.getStudentId(),
						siblingGroupId);
//				throw new ApplicationException(
//						new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Student already belong to different sibling group."));
			} else {
				studentIdList.add(student.getStudentId());
			}
		}
		if(CollectionUtils.isEmpty(studentIdList)) {
			return false;
		}
		studentSiblingPayload.setStudentIdList(studentIdList);
		return studentDao.editSiblings(instituteId, studentSiblingPayload, true, isStudentLevelAction);
	}

	public boolean deleteSiblingsGroup(int instituteId, UUID userId, UUID siblingGroupId) {

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_SIBLINGS_DETAILS);
		return studentDao.deleteSiblingsGroup(instituteId, siblingGroupId);
	}

	public List<StudentSiblingDetails> getStudentSiblingDetailsList(int instituteId, String searchText) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		return filterWithSearchText(studentDao.getStudentSiblingDetailsList(instituteId), searchText);
	}

	private List<StudentSiblingDetails> filterWithSearchText(List<StudentSiblingDetails> studentSiblingDetailsList,
															 String searchText) {
		if(CollectionUtils.isEmpty(studentSiblingDetailsList)) {
			return null;
		}
		Map<UUID, StudentSiblingDetails> siblingIdDetailsMap = new HashMap<>();
		if(StringUtils.isBlank(searchText)) {
			for(StudentSiblingDetails studentSiblingDetails : studentSiblingDetailsList) {
				UUID siblingGroupId = studentSiblingDetails.getSiblingGroupId();
				if(siblingGroupId == null) {
					continue;
				}
				/**
				 * checking if a sibling group contain only no student or only one student, continuing in that case.
				 */
				if(CollectionUtils.isEmpty(studentSiblingDetails.getStudentList())
						|| studentSiblingDetails.getStudentList().size() <= 1) {
					continue;
				}
				siblingIdDetailsMap.put(siblingGroupId, studentSiblingDetails);
			}
			return new ArrayList<>(siblingIdDetailsMap.values());
		}

		searchText = searchText.trim().toLowerCase();
		for(StudentSiblingDetails studentSiblingDetails : studentSiblingDetailsList) {
			UUID siblingGroupId = studentSiblingDetails.getSiblingGroupId();
			if(siblingGroupId == null) {
				continue;
			}
			/**
			 * checking if a sibling group contain only no student or only one student, continuing in that case.
			 */
			if(CollectionUtils.isEmpty(studentSiblingDetails.getStudentList())
					|| studentSiblingDetails.getStudentList().size() <= 1) {
				continue;
			}
			for(StudentLite studentLite : studentSiblingDetails.getStudentList()) {
				if(!siblingIdDetailsMap.containsKey(siblingGroupId)) {
					if(studentLite.getAdmissionNumber().toLowerCase().contains(searchText) ||
							(!StringUtils.isBlank(studentLite.getFathersName()) &&
									studentLite.getFathersName().toLowerCase().contains(searchText)) ||
							studentLite.getName().toLowerCase().contains(searchText)) {
						siblingIdDetailsMap.put(siblingGroupId, studentSiblingDetails);
					}
				}
			}
		}
		return new ArrayList<>(siblingIdDetailsMap.values());
	}

	public StudentSiblingDetails getStudentSiblingDetails(int instituteId, UUID siblingGroupId) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		if(siblingGroupId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		return studentDao.getStudentSiblingDetails(instituteId, siblingGroupId);
	}

	/**
	 * converting institute id from int to Integer to handle multiple institute case.
	 * @param instituteId
	 * @param birthday
	 * @return
	 */
	public List<BirthdayStudentData> getBirthdayStudents(int instituteId, int birthday) {
		int dayStart = DateUtils.getDayStart(birthday, DateUtils.DEFAULT_TIMEZONE);
		return studentDao.getBirthdayStudents(instituteId, dayStart);
	}
	public StudentAdmissionStats getStudentAdmissionStats(int instituteId, int academicSessionId) {
		List<Student> students = getStudentsInAcademicSession(instituteId, academicSessionId, Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING, StudentStatus.NSO, StudentStatus.RELIEVED));
		List<Standard> standardList = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);
		return getStudentAdmissionStats(instituteId, academicSessionId, students, standardList);
	}

	public StudentSiblingDetails getStudentSiblingDetailsByStudentId(int instituteId, UUID studentId) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		if(studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		return studentDao.getStudentSiblingDetailsByStudentId(instituteId, studentId);
	}

	public List<StudentGenderReport> getGenderWiseStudentReport(int instituteId, Integer academicSessionId) {
		if (instituteId <= 0) {
			return null;
		}
		if (academicSessionId == null || academicSessionId <= 0) {
			return null;
		}
		return studentDao.getGenderWiseStudentReport(instituteId, academicSessionId);
	}

	public boolean updateBiometricDeviceIdentification(UUID studentId, AttendanceDeviceServiceProviderType serviceProviderType, DeviceUpdateUserData deviceUpdateUserData){
		return studentDao.updateBiometricDeviceIdentification(studentId, serviceProviderType,deviceUpdateUserData);
	}

	public StudentAdmissionStats getStudentAdmissionStats(int instituteId, int academicSessionId, List<Student> students,
														  List<Standard> standardList) {
		//this year data variables
		int totalStudents = 0;
		int oldStudents = 0;
		int newRegistrationStudents = 0;
		int newAdmissionStudents = 0;
		List<Student> newAdmissionStudentList = new ArrayList<>();
		List<Student> rteStudentList = new ArrayList<>();
		LinkedHashMap<String, LinkedHashMap<Gender, Integer>> classWiseOldStudentGenderCountMap = new LinkedHashMap<>();
		LinkedHashMap<String, LinkedHashMap<Gender, Integer>> classWiseNewAdmissionGenderCountMap = new LinkedHashMap<>();
		LinkedHashMap<String, Integer> classWiseNewAdmissionCountMap = new LinkedHashMap<>();
		LinkedHashMap<String, Integer> classWiseRteCountMap = new LinkedHashMap<>();
		LinkedHashMap<String, Integer> classWiseStudentCountMap = new LinkedHashMap<>();
		LinkedHashMap<String, LinkedHashMap<UserCategory, Integer>> classWiseCategoryCountMap = new LinkedHashMap<>();
		LinkedHashMap<Gender, Integer> genderCountMap = new LinkedHashMap<>();
		for(Gender gender : Gender.values()) {
			genderCountMap.put(gender, 0);
		}
		LinkedHashMap<UserCategory, Integer> userCategoryCountMap = new LinkedHashMap<>();
		for(UserCategory userCategory : UserCategory.values()) {
			userCategoryCountMap.put(userCategory, 0);
		}
		//last yr data varibales
		int totalLastYearStudents = 0;
		int oldLastYearStudents = 0;
		int newRegistrationLastYearStudents = 0;
		int newAdmissionLastYearStudents = 0;
		LinkedHashMap<String, Integer> classWiseLastYearNewAdmissionCountMap = new LinkedHashMap<>();

		if(CollectionUtils.isEmpty(standardList)) {
			logger.info("No standard setup for institute {}, academic session {}", instituteId, academicSessionId);
			return new StudentAdmissionStats(totalStudents, oldStudents, newRegistrationStudents, newAdmissionStudents, totalLastYearStudents,
					oldLastYearStudents, newRegistrationLastYearStudents, newAdmissionLastYearStudents, classWiseOldStudentGenderCountMap,
					classWiseNewAdmissionGenderCountMap, classWiseNewAdmissionCountMap,
					classWiseLastYearNewAdmissionCountMap, classWiseRteCountMap, classWiseStudentCountMap,
					classWiseCategoryCountMap, newAdmissionStudentList, rteStudentList);
		}
		for(Standard standard : standardList) {
			String standardName = standard.getDisplayName();
			classWiseOldStudentGenderCountMap.put(standardName, new LinkedHashMap<>(genderCountMap));
			classWiseNewAdmissionGenderCountMap.put(standardName, new LinkedHashMap<>(genderCountMap));
			classWiseCategoryCountMap.put(standardName, new LinkedHashMap<>(userCategoryCountMap));
			classWiseNewAdmissionCountMap.put(standardName, 0);
			classWiseRteCountMap.put(standardName, 0);
			classWiseStudentCountMap.put(standardName, 0);
		}

		
		if(CollectionUtils.isEmpty(students)) {
			logger.info("No students found for institute {}, academic session {}", instituteId, academicSessionId);
			return new StudentAdmissionStats(totalStudents, oldStudents, newRegistrationStudents, newAdmissionStudents, totalLastYearStudents,
					oldLastYearStudents, newRegistrationLastYearStudents, newAdmissionLastYearStudents, classWiseOldStudentGenderCountMap,
					classWiseNewAdmissionGenderCountMap, classWiseNewAdmissionCountMap,
					classWiseLastYearNewAdmissionCountMap, classWiseRteCountMap, classWiseStudentCountMap,
					classWiseCategoryCountMap, newAdmissionStudentList, rteStudentList);
		}

		for(Student student : students) {

			if(student.getStudentStatus() == StudentStatus.ENROLMENT_PENDING) {
				newRegistrationStudents++;
				continue;
			}

			//updating registration count if relieved or nso student is marked as new admission
			if(student.getStudentStatus() == StudentStatus.RELIEVED || student.getStudentStatus() == StudentStatus.NSO) {
				if(student.isNewAdmission()) {
					newRegistrationStudents++;
				}
				continue;
			}

			String standardName = student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName();

			boolean isNewAdmission = student.isNewAdmission();

			Gender gender = student.getStudentBasicInfo().getGender();
			if(gender != null) {
				if(isNewAdmission) {
					LinkedHashMap<Gender, Integer> newAdmissionGenderCountMap = classWiseNewAdmissionGenderCountMap.get(standardName);
					if(!newAdmissionGenderCountMap.containsKey(gender)) {
						newAdmissionGenderCountMap.put(gender, 1);
					} else {
						newAdmissionGenderCountMap.put(gender, newAdmissionGenderCountMap.get(gender) + 1);
					}
				} else {
					LinkedHashMap<Gender, Integer> oldStudentsGenderCountMap = classWiseOldStudentGenderCountMap.get(standardName);
					if(!oldStudentsGenderCountMap.containsKey(gender)) {
						oldStudentsGenderCountMap.put(gender, 1);
					} else {
						oldStudentsGenderCountMap.put(gender, oldStudentsGenderCountMap.get(gender) + 1);
					}
				}
			}

			UserCategory userCategory = student.getStudentBasicInfo().getUserCategory();
			if(userCategory != null) {
				LinkedHashMap<UserCategory, Integer> userCategoryCount = classWiseCategoryCountMap.get(standardName);
				if(!userCategoryCount.containsKey(userCategory)) {
					userCategoryCount.put(userCategory, 1);
				} else {
					userCategoryCount.put(userCategory, userCategoryCount.get(userCategory) + 1);
				}
			}

			boolean isRte = student.getStudentBasicInfo().isRte();
			if(isRte) {
				rteStudentList.add(student);
				if(!classWiseRteCountMap.containsKey(standardName)) {
					classWiseRteCountMap.put(standardName, 1);
				} else {
					classWiseRteCountMap.put(standardName, classWiseRteCountMap.get(standardName) + 1);
				}
			}

			if(!classWiseStudentCountMap.containsKey(standardName)) {
				classWiseStudentCountMap.put(standardName, 1);
			} else {
				classWiseStudentCountMap.put(standardName, classWiseStudentCountMap.get(standardName) + 1);
			}

			if(isNewAdmission) {
				newAdmissionStudentList.add(student);
				newRegistrationStudents++;
				newAdmissionStudents++;
				if(!classWiseNewAdmissionCountMap.containsKey(standardName)) {
					classWiseNewAdmissionCountMap.put(standardName, 1);
				} else {
					classWiseNewAdmissionCountMap.put(standardName, classWiseNewAdmissionCountMap.get(standardName) + 1);
				}
			} else {
				oldStudents++;
			}
			totalStudents++;
		}

		AcademicSession academicSession = instituteManager.getPreviousSessionDetails(instituteId, academicSessionId);
		if(academicSession != null && academicSession.getAcademicSessionId() > 0) {
			int previousAcademicSessionId = academicSession.getAcademicSessionId();
			List<Standard> standardListPreviousSession = instituteManager.getInstituteStandardDetails(instituteId, previousAcademicSessionId);
			for(Standard standard : standardListPreviousSession) {
				String standardName = standard.getDisplayName();
				classWiseLastYearNewAdmissionCountMap.put(standardName, 0);
			}

			List<Student> studentPreviousSession = getStudentsInAcademicSession(instituteId, previousAcademicSessionId,
					Arrays.asList(StudentStatus.ENROLLED, StudentStatus.ENROLMENT_PENDING));
			for(Student student : studentPreviousSession) {
				if(student.getStudentStatus() == StudentStatus.ENROLMENT_PENDING) {
					newRegistrationLastYearStudents++;
					continue;
				}
				String standardName = student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayName();

				boolean isNewAdmission = student.isNewAdmission();
				if(isNewAdmission) {
					newRegistrationLastYearStudents++;
					newAdmissionLastYearStudents++;
					if(!classWiseLastYearNewAdmissionCountMap.containsKey(standardName)) {
						classWiseLastYearNewAdmissionCountMap.put(standardName, 1);
					} else {
						classWiseLastYearNewAdmissionCountMap.put(standardName, classWiseLastYearNewAdmissionCountMap.get(standardName) + 1);
					}
				} else {
					oldLastYearStudents++;
				}
				totalLastYearStudents++;
			}
		}

		return new StudentAdmissionStats(totalStudents, oldStudents, newRegistrationStudents, newAdmissionStudents, totalLastYearStudents,
				oldLastYearStudents, newRegistrationLastYearStudents, newAdmissionLastYearStudents, classWiseOldStudentGenderCountMap,
				classWiseNewAdmissionGenderCountMap, classWiseNewAdmissionCountMap,
				classWiseLastYearNewAdmissionCountMap, classWiseRteCountMap, classWiseStudentCountMap,
				classWiseCategoryCountMap, newAdmissionStudentList, rteStudentList);
	}

	public TreeMap<Integer, List<StudentSessionStatusDetails>> migrateAllInstituteStudentStatusData(boolean update) {
		List<Institute> instituteList = instituteManager.getAllInstitute();
		TreeMap<Integer, List<StudentSessionStatusDetails>> studentSessionStatusDetailsMap = new TreeMap<>();
		for(Institute institute : instituteList) {
			logger.info("Running migration for institute {}", institute.getInstituteId());
			List<StudentSessionStatusDetails> studentSessionStatusDetailsList = migrateStudentStatusData(institute.getInstituteId(),
					null, update);
			studentSessionStatusDetailsMap.put(institute.getInstituteId(), studentSessionStatusDetailsList);
			logger.info("Done migration for institute {}", institute.getInstituteId());
		}
		return studentSessionStatusDetailsMap;
	}
	/**
	 *
	 * @param instituteId
	 * @param update
	 * @return
	 *
	 * To be used for one time migration of data from student level status to session level status
	 *
	 */
	public List<StudentSessionStatusDetails> migrateStudentStatusData(int instituteId, UUID studentIdTesting, boolean update) {
		List<Student> studentList = new ArrayList<>();
		if(studentIdTesting != null) {
			Student student = getStudentWithoutSession(studentIdTesting);
			studentList.add(student);
		} else {
			studentList = getAllStudentWithoutSession(instituteId);
		}
		if(CollectionUtils.isEmpty(studentList)) {
			logger.warn("No students found for institute {}", instituteId);
			return null;
		}
		logger.info("Total students found for institute {}, {}", instituteId, studentList.size());
		List<StudentSessionStatusDetails> studentSessionStatusDetailsList = new ArrayList<>();
		for(Student student : studentList) {
			List<Student> currentStudentList = descendingSortedStudentDetailsInAllSession(instituteId, student.getStudentId());
			StudentRelievePayload studentRelievePayload = getStudentRelievePayload(student, currentStudentList);
			if(CollectionUtils.isEmpty(currentStudentList)) {
				continue;
			}
			List<StudentSessionStatusDetails> studentSessionStatusDetailsList1 = getStudentSessionStatusDetailsList(
					instituteId, student.getStudentId(), student.getFinalStudentStatus(), studentRelievePayload, currentStudentList);

			if(!CollectionUtils.isEmpty(studentSessionStatusDetailsList1)) {
				studentSessionStatusDetailsList.addAll(studentSessionStatusDetailsList1);
			}
		}
		logger.info("Final students status list for institute {}, {}", instituteId, studentSessionStatusDetailsList.size());
		if(update) {
			logger.info("Updating final students status list for institute {}, {}", instituteId, studentSessionStatusDetailsList.size());
			boolean updated = studentDao.updateStudentSessionStatus(studentSessionStatusDetailsList);
			if(!updated) {
				logger.error("Error while updating data for institute {}", instituteId);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Error occurred while updating student session status details."));
			}
			logger.info("Successfully updated data for institute {}", instituteId);
		}
		return studentSessionStatusDetailsList;
	}

	private StudentRelievePayload getStudentRelievePayload(Student student, List<Student> currentStudentList) {
		StudentStatus finalStatus = student.getFinalStudentStatus();
		Integer relieveDate = student.getStudentBasicInfo().getRelieveDate();
		String relieveReason = student.getStudentBasicInfo().getRelieveReason();
		Map<TransferCertificateVariables, String> tcVariables = student.getTcVariables();
		switch (finalStatus) {
			case RELIEVED:
				Map<RelievedMetadataVariables, String> relievedMetadata = getRelievedMetadataMap(student, currentStudentList);
				return new StudentRelievePayload(relieveDate, relieveReason, tcVariables, relievedMetadata);
			case NSO:
				return new StudentRelievePayload(relieveDate, relieveReason, tcVariables, null);
			case ENROLMENT_PENDING:
			case ENROLLED:
			case DELETED:
				return null;
		}
		return null;
	}

	private Map<RelievedMetadataVariables, String> getRelievedMetadataMap(Student student, List<Student> currentStudentList) {
		Integer relieveDate = student.getStudentBasicInfo().getRelieveDate();

		Student latestStudentEntry = currentStudentList.get(0);
		AcademicSession lastActiveAcademicSession = latestStudentEntry.getStudentAcademicSessionInfoResponse().getAcademicSession();
		int lastActiveAcademicSessionId = lastActiveAcademicSession.getAcademicSessionId();

		Boolean lastActiveSessionCompleted = getLastActiveSessionCompleted(lastActiveAcademicSession, relieveDate);

		if(lastActiveSessionCompleted == null) {
			lastActiveSessionCompleted = false;
			int sessionEndTime = lastActiveAcademicSession.getSessionEndTime();
			int sessionStartTime = lastActiveAcademicSession.getSessionStartTime();
			Map<Integer, List<FeePaymentDetails>> allSessionPaidFeeDetails = feePaymentTransactionDao.getPaidFeeDetails(
					latestStudentEntry.getInstituteId(), latestStudentEntry.getStudentId());
			boolean feeCollected = !CollectionUtils.isEmpty(allSessionPaidFeeDetails) && (!CollectionUtils.isEmpty(
					allSessionPaidFeeDetails.get(lastActiveAcademicSessionId)));
			if(!feeCollected && currentStudentList.size() >= 2) {
				lastActiveAcademicSessionId = currentStudentList.get(1).getStudentAcademicSessionInfoResponse()
						.getAcademicSession().getAcademicSessionId();
				lastActiveSessionCompleted = true;
			}
		}

		Map<RelievedMetadataVariables, String> relievedMetadata = new HashMap<>();
		relievedMetadata.put(RelievedMetadataVariables.LAST_ACTIVE_SESSION, String.valueOf(lastActiveAcademicSessionId));
		relievedMetadata.put(RelievedMetadataVariables.LAST_ACTIVE_SESSION_COMPLETED, String.valueOf(lastActiveSessionCompleted));
		return relievedMetadata;
	}

	private Boolean getLastActiveSessionCompleted(AcademicSession academicSession, int relieveDate) {
		int sessionEndTime = academicSession.getSessionEndTime();
		int sessionStartTime = academicSession.getSessionStartTime();

		if (relieveDate <= sessionEndTime && relieveDate >= DateUtils.addDays(sessionEndTime, -30)) {
			return true;
		}

		if (relieveDate > DateUtils.addDays(sessionStartTime, 15) &&
				relieveDate <= DateUtils.addDays(sessionEndTime, -30)) {
			return false;
		}

		if (relieveDate <= DateUtils.addDays(sessionStartTime, 15)) {
			return null;
		}
		return true;
	}

	public boolean updateStudentTransferCertificateDetails(int instituteId, UUID studentId,
														   StudentTransferCertificateDetails studentTransferCertificateDetails) {

		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute id."));
		}

		if(studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student id."));
		}

		MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
		if(!metaDataPreferences.isDetailedTCEnabled()) {
			return true;
		}

		if(studentTransferCertificateDetails == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid transfer certificate details."));
		}

		if(StringUtils.isBlank(studentTransferCertificateDetails.getTcNumber())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Transfer certificate number cannot be empty."));
		}

		return studentDao.updateStudentTransferCertificateDetails(instituteId, studentId,
				metaDataPreferences.isTransferCertificateCounter(), studentTransferCertificateDetails);
	}

	public boolean updateBulkStudentDetails(int instituteId, UUID userId, BulkStudentPayload bulkStudentPayload) {

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_UPDATE_STUDENT);

		if (bulkStudentPayload.getInstituteId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		final MetaDataPreferences metaDataPreferences = userPreferenceSettings
				.getMetaDataPreferences(bulkStudentPayload.getInstituteId());

		return studentDao.updateBulkStudentDetails(metaDataPreferences, bulkStudentPayload);
	}

	public boolean updateHouseByStudentId(int instituteId, UUID studentId, UUID houseId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid student id."));
		}

		return studentDao.updateHouseByStudentId(instituteId, studentId, houseId);
	}

	public boolean updateHouseByStudentIds(int instituteId, Map<UUID, UUID> studentIdHouseIdMap) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (CollectionUtils.isEmpty(studentIdHouseIdMap)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid student id."));
		}

		return studentDao.updateHouseByStudentIds(instituteId, studentIdHouseIdMap);
	}

	public Map<Integer, List<User>> getInstituteWiseEnabledBirthdayUsersList(Integer instituteId, int birthDate) {
		if (birthDate <= 0) {
			return null;
		}
		return getFilteredInstituteWiseUserList(studentDao.getBirthdayUsers(instituteId, birthDate));
	}

	public boolean updateDeviceUserId(int instituteId, Map<String, String> admissionNumberIdMap){
		if(instituteId <= 0 || MapUtils.isEmpty(admissionNumberIdMap)){
			logger.error("Invalid institute id {} or empty payload", instituteId);
			return false;
		}
		List<Student> students =  getAllStudentWithoutSession(instituteId);
		if(CollectionUtils.isEmpty(students)){
			logger.warn("No student found for instituteId {}", instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No student found for instituteId"));
		}
		Map<String, UUID> admissionUUIDMap = new HashMap<>();
		Map<String, String> existingDeviceUserIds = new HashMap<>();
		for(Student student : students){
			admissionUUIDMap.put(student.getStudentBasicInfo().getAdmissionNumber().trim().toLowerCase(), student.getStudentId());
			if(StringUtils.isNotBlank(student.getDeviceUserId())){
				existingDeviceUserIds.put(student.getDeviceUserId().trim().toLowerCase(), student.getStudentBasicInfo().getAdmissionNumber());
			}
		}

		Map<UUID, String> uuidDeviceIdMap = new HashMap<>();
		for(Entry<String, String> mapping : admissionNumberIdMap.entrySet()){
			String admNum = mapping.getKey().trim().toLowerCase();
			if(!admissionUUIDMap.containsKey(admNum)){
				logger.error("Student with {} not found for instituteId {}", admNum, instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Student " + admNum + " not found"));
			}
			if(StringUtils.isNotBlank(mapping.getValue())
					&& existingDeviceUserIds.containsKey(mapping.getValue().trim().toLowerCase())){
				logger.error("Student with {} is already assigned with {} for instituteId {}", existingDeviceUserIds.get(mapping.getValue().trim().toLowerCase()), mapping.getValue(), instituteId);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Student " + existingDeviceUserIds.get(mapping.getValue().trim().toLowerCase()) + " is already assigned with " +mapping.getValue()));
			}
			// device id null is inserted to remove the old value if empty string is provided
			uuidDeviceIdMap.put(admissionUUIDMap.get(admNum), StringUtils.isBlank(mapping.getValue()) ? null : mapping.getValue().trim());
		}

		return studentDao.updateStudentDeviceUserId(instituteId, uuidDeviceIdMap);
	}

	private Map<Integer, List<User>> getFilteredInstituteWiseUserList(List<User> userList) {
		if(org.apache.commons.collections4.CollectionUtils.isEmpty(userList)) {
			return null;
		}
		Map<Integer, List<User>> instituteWiseUserMap = new HashMap<>();
		for(User user : userList) {
			if(user == null || user.getUuid() == null) {
				continue;
			}
			if(user.getUserStatus() != UserStatus.ENABLED) {
				continue;
			}
			if(!instituteWiseUserMap.containsKey(user.getInstituteId())) {
				instituteWiseUserMap.put(user.getInstituteId(), new ArrayList<>());
			}
			instituteWiseUserMap.get(user.getInstituteId()).add(user);
		}
		return instituteWiseUserMap;
	}

	public List<StudentSiblingDetails> getStudentSiblingDetailWithSessionList(int instituteId, String searchText,
				int academicSessionId, Set<StudentStatus> studentStatusList) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academicSession id."));
		}
		return filterWithSearchText(studentDao.getStudentSiblingDetailWithSessionList(instituteId,
				academicSessionId, studentStatusList), searchText);
	}

	public StudentSiblingDetails getStudentSiblingDetailWithSession(int instituteId, UUID siblingGroupId, int academicSessionId) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		if(siblingGroupId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		if(academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academicSession id."));
		}
		return studentDao.getStudentSiblingDetailWithSession(instituteId, academicSessionId, siblingGroupId);
	}

	public StudentSiblingDetails getStudentSiblingDetailsWithSessionByStudentId(int instituteId, int academicSessionId, UUID studentId) {
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		if(studentId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Invalid institute id."));
		}
		return studentDao.getStudentSiblingDetailsWithSessionByStudentId(instituteId, academicSessionId, studentId);
	}

	public boolean softDeleteStudentNonAtomic(int instituteId, UUID studentId, String updatedAdmissionNumber,
											  String updatedRegistrationNumber) {
		return studentDao.softDeleteStudentNonAtomic(instituteId, studentId, updatedAdmissionNumber, updatedRegistrationNumber);
	}

	/**
	 * Must be used within the transaction
	 */
	public boolean softDeleteStudentSessionDetailsNonAtomic(int instituteId, UUID studentId) {
		return studentDao.softDeleteStudentSessionDetailsNonAtomic(instituteId, studentId);
	}

	// Student Information Management APIs

	public StudentManagementInputFieldData getStudentManagementFieldMetadata(int instituteId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id {}", instituteId);
			return null;
		}
		return studentManagementFieldHandler.getStudentManagementFieldMetadata();
	}


	public StudentManagementFieldDetails getClassStudentsFieldData(int instituteId, int academicSessionId, UUID standardId,
																   String sectionIdsCSV, String fieldListCSV,
																   Map<UUID, StudentExamResultDetails> studentExamResultDetailsMap) {
		if (instituteId <= 0 || academicSessionId <= 0 || standardId == null || StringUtils.isBlank(fieldListCSV)) {
			logger.error("Invalid instituteId {}, academicSessionId {}, standardId {}, fieldListStr {}", instituteId, academicSessionId, standardId, fieldListCSV);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute / session / standard."));
		}

		Set<Integer> sectionIds = getSecionIds(sectionIdsCSV);

		Set<StudentManagementField> requiredFields = getStudentManagementFields(fieldListCSV);

		List<Student> studentList =  sortStudentList(studentDao.getClassStudents(instituteId, academicSessionId,
				new HashSet<>(Arrays.asList(standardId)), sectionIds, new HashSet<>(Arrays.asList(StudentStatus.ENROLLED))));

		if(CollectionUtils.isEmpty(studentList)){
			logger.warn("No students found for instituteId {}, academicSessionId {}, standardId {}, sectionIdsCSV {}, fieldListCSV {}", instituteId, academicSessionId, standardId, sectionIdsCSV, fieldListCSV);
			return new StudentManagementFieldDetails(new StudentManagementOutputFieldMetadata(new ArrayList<>()), new ArrayList<>());
		}

		Map<StudentManagementField, ISMFieldPossibleValueGenerator> fieldISMFieldValueGeneratorMap = new HashMap<>();
		fieldISMFieldValueGeneratorMap.put(StudentManagementField.SECTION, new ISMFieldPossibleValueGenerator() {
			@Override
			public LinkedHashMap<String, String> getPossibleValues() {
				List<Standard> standards = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);
				for(Standard standard : standards){
					if(standard.getStandardId().equals(standardId)){
						LinkedHashMap<String, String> map = new LinkedHashMap<>();
						if(!CollectionUtils.isEmpty(standard.getStandardSectionList())){
							for(StandardSections standardSection : standard.getStandardSectionList()){
								map.put(String.valueOf(standardSection.getSectionId()), standardSection.getSectionName());
							}
						}
						return map;
					}
				}
				return new LinkedHashMap<>();
			}
		});

		fieldISMFieldValueGeneratorMap.put(StudentManagementField.HOUSE, new ISMFieldPossibleValueGenerator() {
			@Override
			public LinkedHashMap<String, String> getPossibleValues() {
				List<InstituteHouse> instituteHouseList = instituteManager.getInstituteHouseList(instituteId);
				if(!CollectionUtils.isEmpty(instituteHouseList)) {
					LinkedHashMap<String, String> map = new LinkedHashMap<>();
					for(InstituteHouse instituteHouse : instituteHouseList){
						map.put(String.valueOf(instituteHouse.getHouseId()), instituteHouse.getHouseName());
					}
					return map;
				}
				return new LinkedHashMap<>();
			}
		});

		fieldISMFieldValueGeneratorMap.put(StudentManagementField.HOSTEL, new ISMFieldPossibleValueGenerator() {
			@Override
			public LinkedHashMap<String, String> getPossibleValues() {
				List<HostelDetails> hostelDetailsList = hostelManagementDao.getHostelDetails(instituteId);
				if(!CollectionUtils.isEmpty(hostelDetailsList)) {
					LinkedHashMap<String, String> map = new LinkedHashMap<>();
					for(HostelDetails hostelDetails : hostelDetailsList){
						map.put(String.valueOf(hostelDetails.getHostelId()), hostelDetails.getHostelName());
					}
					return map;
				}
				return new LinkedHashMap<>();
			}
		});

		return studentManagementFieldHandler.getStudentManagementFieldDetails(studentList, studentExamResultDetailsMap, requiredFields, fieldISMFieldValueGeneratorMap);
	}

	public boolean updateClassStudentsFieldData(int instituteId, int academicSessionId, UUID standardId,
																	  String sectionIdsCSV, UUID userId, StudentManagementUpdateFieldPayload payload, boolean standardCheck) {
		if(standardCheck){
			if (instituteId <= 0 || academicSessionId <= 0 || standardId == null || payload == null ||
					CollectionUtils.isEmpty(payload.getStudentDataList()) || payload.getDataType() == null || CollectionUtils.isEmpty(payload.getRequiredFields())) {
				logger.error("Invalid instituteId {}, academicSessionId {}, standardId {}, fieldListStr {}, payload {}", instituteId, academicSessionId, standardId, payload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute / session / standard / required fields / payload."));
			}
		}
		else{
			if (instituteId <= 0 || academicSessionId <= 0 ||  payload == null ||
					CollectionUtils.isEmpty(payload.getStudentDataList()) || payload.getDataType() == null || CollectionUtils.isEmpty(payload.getRequiredFields())) {
				logger.error("Invalid instituteId {}, academicSessionId {}, fieldListStr {}, payload {}", instituteId, academicSessionId, payload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute / session / standard / required fields / payload."));
			}
		}

		Set<Integer> sectionIds = getSecionIds(sectionIdsCSV);

		List<StudentManagementField> requiredFields = payload.getRequiredFields();

		Map<UUID, Student> existingStudentMap = getStudentMap(instituteId, academicSessionId, standardId, sectionIds);

		//TODO : validate the exact values to be updated for each student. Eg: valid section value etc
		validateUpdateStudentDataPayload(instituteId, academicSessionId, standardId, payload, existingStudentMap, requiredFields);

		switch (payload.getDataType()){
			case BASIC_DATA:
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_UPDATE_STUDENT);
				MetaDataPreferences metaDataPreferences = userPreferenceSettings.getMetaDataPreferences(instituteId);
				return studentDao.updateBulkStudentDetails(instituteId, academicSessionId, payload, metaDataPreferences);
			case SESSION_DATA:
				userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT_SESSION_DETAILS);
				return studentDao.updateStudentAcademicSessionDetails(instituteId, academicSessionId, standardId, payload, standardCheck);
			default:
				logger.error("Data type {} not supported for update", payload.getDataType());
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid data type for update."));
		}
	}

	private static void validateUpdateStudentDataPayload(int instituteId, int academicSessionId, UUID standardId, StudentManagementUpdateFieldPayload payload, Map<UUID, Student> existingStudentMap, List<StudentManagementField> requiredFields) {
		for (StudentManagementUpdateFieldStudentData studentData : payload.getStudentDataList()) {
			UUID studentId = studentData.getStudentId();
			if (!existingStudentMap.containsKey(studentId)) {
				logger.error("Invalid student {} for institute {}, academicSessionId {}, standardId {}, payload {}", studentId, instituteId, academicSessionId, standardId, payload);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid student for update"));
			}

			for (StudentManagementUpdateFieldValue fieldValue : studentData.getFieldValueList()) {
				if (fieldValue.getField().getDataType() != payload.getDataType() || !requiredFields.contains(fieldValue.getField())) {
					logger.error("Invalid field {} for update, student {} institute {}, academicSessionId {}, standardId {}, payload {}", fieldValue.getField(), studentId, instituteId, academicSessionId, standardId, payload);
					throw new ApplicationException(
							new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid field for update"));
				}
			}
		}
	}

	private Map<UUID, Student> getStudentMap(int instituteId, int academicSessionId, UUID standardId, Set<Integer> sectionIds) {
		List<Student> studentList =  studentDao.getClassStudents(instituteId, academicSessionId,
		 standardId != null ? new HashSet<>(Arrays.asList(standardId)): new HashSet<>(), sectionIds, new HashSet<>(Arrays.asList(StudentStatus.ENROLLED)));
		return getStudentMap(studentList);
	}


	public Map<UUID, Student>  getStudentMap(List<Student> studentList) {
		Map<UUID, Student> studentMap = EMapUtils.getMap(studentList, new EMapUtils.MapFunction<Student, UUID, Student>() {
			@Override
			public UUID getKey(Student entry) {
				return entry.getStudentId();
			}

			@Override
			public Student getValue(Student entry) {
				return entry;
			}
		});
		return studentMap;
	}
	private static Set<StudentManagementField> getStudentManagementFields(String fieldListCSV) {
		Set<StudentManagementField> requiredFields = new LinkedHashSet<>();
		final String[] fieldTokens = fieldListCSV.split(",");
		for (final String fieldToken : fieldTokens) {
			requiredFields.add(StudentManagementField.valueOf(fieldToken));
		}
		return requiredFields;
	}

	private static Set<Integer> getSecionIds(String sectionIdsCSV) {
		Set<Integer> sectionIds = new HashSet<>();
		if(!StringUtils.isBlank(sectionIdsCSV)) {
			final String[] sectionTokens = sectionIdsCSV.split(",");
			for (final String sectionId : sectionTokens) {
				sectionIds.add(Integer.parseInt(sectionId));
			}
		}
		return sectionIds;
	}

	// End of Student Information Management APIs


	private byte[] getThumbnail(Student student) {
		List<Document<StudentDocumentType>> studentDocuments = student.getStudentDocuments();
		if (CollectionUtils.isEmpty(studentDocuments)) {
			return null;
		}
		UUID thumbnailId = null;
		for (final Document<StudentDocumentType> studentDocument : studentDocuments) {
			if (studentDocument == null || studentDocument.getDocumentId() == null ||
					studentDocument.getDocumentType() != StudentDocumentType.STUDENT_PROFILE_IMAGE_THUMBNAIL) {
				continue;
			}
			thumbnailId = studentDocument.getDocumentId();
			break;
		}
		if (thumbnailId == null) {
			return null;
		}
		DownloadDocumentWrapper<Document<StudentDocumentType>> documentDownloadDocumentWrapper = downloadDocument(
				student.getInstituteId(), student.getStudentId(), thumbnailId);
		if (documentDownloadDocumentWrapper == null || documentDownloadDocumentWrapper.getDocumentContent() == null) {
			return null;
		}
		return documentDownloadDocumentWrapper.getDocumentContent().toByteArray();
	}

	public boolean updateStudentTag(int instituteId, UUID userId, StudentTaggedPayload studentTaggedPayload){
		if(instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute."));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}

		if(studentTaggedPayload == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid student tag payload."));
		}

		if(studentTaggedPayload.getStudentIdList() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid student id list."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT_TAG_DETAILS);

		return studentDao.updateStudentTag(instituteId, studentTaggedPayload);
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStudentSchoolPrincipalSignature(int instituteId, UUID principalId){
		Staff staff = staffDao.getStaff(instituteId, principalId);
		return downloadStaffDocument(instituteId, staff, StaffDocumentType.STAFF_SIGNATURE);
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStudentClassTeacherSignature(int instituteId, int academicSessionId, UUID standardId, Integer sectionId){
		StandardSessionDataPayload standardSessionDataPayload = null;
		List<StandardSessionDataPayload> standardSessionDataPayloadList = instituteManager.getStandardSessionDataPayloadList(instituteId, academicSessionId, standardId, sectionId == null ? null : new HashSet<>(Collections.singletonList(sectionId)), null);
		if (CollectionUtils.isEmpty(standardSessionDataPayloadList)) {
			return null;
		}
		if (sectionId <= 0) {
			standardSessionDataPayload = standardSessionDataPayloadList.get(0);
		}
		for (StandardSessionDataPayload standardSessionData : standardSessionDataPayloadList) {
			if (Objects.equals(standardSessionData.getSectionId(), sectionId)) {
				standardSessionDataPayload =  standardSessionData;
			}
		}
		if(standardSessionDataPayload == null){
			return null;
		}
		return downloadStudentSchoolPrincipalSignature(instituteId, standardSessionDataPayload.getStaffId());
	}

	public DownloadDocumentWrapper<Document<StaffDocumentType>> downloadStaffDocument(int instituteId, Staff staff,
																					  StaffDocumentType staffDocumentType) {
		final List<Document<StaffDocumentType>> staffDocuments = staff.getStaffDocuments();
		if (CollectionUtils.isEmpty(staffDocuments)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DOCUMENT, "Document does not exists"));
		}
		for (final Document<StaffDocumentType> staffDocument : staffDocuments) {
			if (staffDocument.getDocumentType() == staffDocumentType) {
				final String s3Path = buildStaffDocumentPath(staff.getStaffId(), staffDocument, staffDocument.getDocumentType().isThumbnail());
				return new DownloadDocumentWrapper<>(staffDocument,
						documentManager.downloadDocument(instituteId, s3Path));
			}
		}
		return null;
	}

	public String buildStaffDocumentPath(UUID staffId, Document<StaffDocumentType> staffDocument, boolean isThumbnail) {
		String thumbnailPath = "";
		if(isThumbnail) {
			thumbnailPath = HYPHEN + THUMBNAIL_SUFFIX;
		}
		final StringBuilder s3Path = new StringBuilder();
		s3Path.append(STAFF_PARENT_DIRECTORY_NAME).append(S3_FILE_PATH_DELIMITER).append(staffId)
				.append(S3_FILE_PATH_DELIMITER).append(STAFF_COMMON_DOCUMENT_DIRECTORY_NAME)
				.append(S3_FILE_PATH_DELIMITER).append(staffDocument.getDocumentId()).append(thumbnailPath)
				.append(".").append(staffDocument.getFileExtension());
		return s3Path.toString();
	}
}
