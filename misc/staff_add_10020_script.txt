--dev envi
scp -i ~/.ssh/L-qa.pem /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  <EMAIL>:/tmp/
scp -i ~/.ssh/L-qa.pem /Users/<USER>/Lernen/lernen-backend/staff_data/10020_staff_data.csv  <EMAIL>:/tmp/
sudo java -Dlernen_env=dev -cp /tmp/dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.staff.IngestMPPSStaff10020 -f /tmp/10020_staff_data.csv


--preprod
--dev envi
scp -i ~/.ssh/L-preprod.pem /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  <EMAIL>:/tmp/
scp -i ~/.ssh/L-preprod.pem /Users/<USER>/Lernen/lernen-backend/staff_data/10020_staff_data.csv  <EMAIL>:/tmp/
sudo java -Dlernen_env=preprod -cp /tmp/dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.staff.IngestMPPSStaff10020 -f /tmp/10020_staff_data.csv

--prod
--dev envi
scp -i ~/.ssh/lernen-be.pem /Users/<USER>/Lernen/lernen-backend/dev-tools/target/dev-tools-1.0.1-SNAPSHOT.jar  <EMAIL>:/tmp/
scp -i ~/.ssh/lernen-be.pem /Users/<USER>/Lernen/lernen-backend/staff_data/10020_staff_data.csv  <EMAIL>:/tmp/
sudo java -Dlernen_env=prod -cp /tmp/dev-tools-1.0.1-SNAPSHOT.jar com.lernen.cloud.dev.tools.ingest.staff.IngestMPPSStaff10020 -f /tmp/10020_staff_data.csv