package com.lernen.cloud.pdf.exam.reports._10420;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.colors.WebColors;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.ExamGrade;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.ExamReportStructure;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.EColorUtils;
import com.lernen.cloud.core.utils.RomanNumberUtils;
import com.lernen.cloud.core.utils.images.ImageProvider;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;

public class ExamReportGenerator10420 extends ExamReportGenerator implements IExamReportCardGenerator{
    public ExamReportGenerator10420(AssetProvider assetProvider) {
		super(assetProvider);
		//TODO Auto-generated constructor stub
	}

	private static final Logger logger = LogManager.getLogger(ExamReportGenerator10420.class);
	public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
	public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
	public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
	public static final float SQUARE_BORDER_MARGIN = 12f;
    public static final float LOGO_WIDTH = 75f;
    public static final float LOGO_HEIGHT = 75f;
	
	protected static final String TERM_I_REPORT_TYPE = "TERM_I";
	protected static final String TERM_II_REPORT_TYPE = "TERM_II";
	protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
	protected static final String PRINCIPAL_UUID = "37b8809c-458a-493e-9fb3-f05dc4b1d99d";
	
	protected static Map<UUID, List<String>> STANDARD_ID_DESCRIPTION_MAP = new HashMap<>();
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "PT = Periodic Test, NB = Notebook, SE = Subject Enrichment, HY = Half Yearly, YL = Yearly or Annual Exam";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_3 = "Computer Marks: Term 1 is evaluated out of 50 marks.";
	protected static final String SCHOLASTIC_EXAM_DESCRIPTION_2 = "MM = Maximum Marks, MO = Marks Obtained";
	static{
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("670011f3-bc74-41fc-bc6b-837d77f0491a"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("7da0d928-f6a8-46d0-aff7-86d5408e0d83"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("12f11640-9bbf-4680-9384-99267322e2e8"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("11aa96f4-5fea-468a-90af-ccf7b43e7cb7"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("7a597db3-6a7b-4e6d-8cde-6dcd193d298f"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("e2b0389f-dc70-43b0-a135-69f21b5de17d"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("dae9c2ce-acd2-4276-8c93-170d4223f9e1"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("c716c156-353a-433a-a64c-248a5435888c"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("c0339fbb-28ab-4339-9bfd-592340393b5a"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("2f467949-dfa4-4414-88b2-c1fb37ad1567"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
		STANDARD_ID_DESCRIPTION_MAP.put(UUID.fromString("b78042cf-ba1f-4e04-880a-20bac8dbf5e8"), Collections.singletonList(SCHOLASTIC_EXAM_DESCRIPTION_2));
	}
	@Override
	public DocumentOutput generateReport(Institute institute, Student student, String reportType,
			ExamReportData examReportData, String documentName, StudentManager studentManager) {
		try {

			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();

			generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
					1, boldFont, regularFont);

			examReportCardLayoutData.getDocument().close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, student {}, reportType {} ",
					institute.getInstituteId(), student.getStudentId(), reportType, e);
		}
		return null;
	}

	private void generateStudentReportCard(ExamReportCardLayoutData examReportCardLayoutData,
										   Institute institute, ExamReportData examReportData,
										   StudentManager studentManager, String reportType,
										   int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
				examReportData, pageNumber, boldFont, regularFont);


		Set<UUID> nonAdditionalSubjects = getNonAdditionalSubjects(examReportData);
		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forOnlyObtainedTotalRow(),
				"SUBJECTS", getScholasticMarksGridSubjectWidth(reportType),
				nonAdditionalSubjects, "MM", "MO", null,
				"-");
		

        List<String> descriptionTextList = STANDARD_ID_DESCRIPTION_MAP.get(examReportData.getStandardMetaData().getStandardId());
		if (!CollectionUtils.isEmpty(descriptionTextList)) {
			for(String descriptionText : descriptionTextList){
				generateScholasticExamDescription(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportData, boldFont, regularFont, descriptionText);
			}
		}
		else{
			addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		}



		if(!CollectionUtils.isEmpty(examReportData.getExamReportStructure().getExamReportCourseStructure()
				.get(CourseType.SCHOLASTIC).getAdditionalCourses())) {
			generateAdditionalCustomScholasticMarksGrid(examReportCardLayoutData, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
					"Additional Subjects", getScholasticMarksGridSubjectWidth(reportType));
		}

		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
					examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
					examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(),
					"Co-Scholastic Subjects", getCoScholasticMarksGridSubjectWidth(reportType), "-");
		}

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		int totalStudents = getStudentCountInClass(institute.getInstituteId(), examReportData.getStudentLite(), studentManager);
		generateResultSummary(examReportCardLayoutData.getDocument(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportData, reportType, boldFont, regularFont, totalStudents);
			
		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
		generateRemarksSection(examReportCardLayoutData, examReportData, boldFont, regularFont);

		generateSignatureBox(examReportCardLayoutData.getDocument(), institute.getInstituteId(),
				examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
				examReportCardLayoutData.getDefaultBorderWidth(), pageNumber, boldFont, regularFont, reportType, studentManager, examReportData, 10f);
	}

	protected void generateAdditionalCustomScholasticMarksGrid(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, GridConfigs gridConfigs, String subjectColumnTitle, float subjectColumnWidth) throws IOException {
		ExamReportStructure examReportStructure = examReportData.getExamReportStructure();
		if (examReportStructure == null || CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure())
				|| examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC) == null
				|| CollectionUtils.isEmpty(examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC)
				.getAdditionalCourses())) {
			return;
		}

		generateScholasticMarksGrid(examReportCardLayoutData.getDocument(), examReportCardLayoutData.getDocumentLayoutSetup(),
				getRegularFont(), getRegularBoldFont(), examReportCardLayoutData.getContentFontSize() - 1,
				examReportCardLayoutData.getDefaultBorderWidth(), examReportData, gridConfigs,
				subjectColumnTitle, subjectColumnWidth,
				examReportStructure.getExamReportCourseStructure().get(CourseType.SCHOLASTIC).getAdditionalCourses(),
				"MM", "MO", null, "-");
	}

	protected float getScholasticMarksGridSubjectWidth(String reportType) {
		return 0.2f;
	}

	protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
		if (reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
			return 0.5f;
		}
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			return 0.4f;
		}
		return 0.3f;
	}

	protected int getStudentCountInClass(int instituteId, StudentLite studentLite, StudentManager studentManager) {

        int academicSessionId = studentLite.getStudentSessionData().getAcademicSessionId();
        UUID standardId = studentLite.getStudentSessionData().getStandardId();
        Integer sectionId = studentLite.getStudentSessionData().getStandardSection() == null ? null :
                studentLite.getStudentSessionData().getStandardSection().getSectionId();

        List<Student> studentList = studentManager.getClassStudents(instituteId, academicSessionId,
                standardId, sectionId == null ? null : new HashSet<>(Collections.singletonList(sectionId)));

        return CollectionUtils.isEmpty(studentList) ? 0 : studentList.size();

    }

	protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
			throws IOException {

		DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
		float contentFontSize = 11f;
		float defaultBorderWidth = 0.5f;
		Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
		float logoWidth = 75f;
		float logoHeight = 75f;

		int instituteId = institute.getInstituteId();
		return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
				defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
	}

	protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
			StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData,
										  int pageNumber, PdfFont boldFont, PdfFont regularFont) throws IOException {

		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		//Institute watermark
		int instituteId = institute.getInstituteId();
		float watermarkImageHeightWidth = 400f;
		generateWatermark(examReportCardLayoutData,
				instituteId, watermarkImageHeightWidth, watermarkImageHeightWidth, watermarkImageHeightWidth / 5 + 20,
				watermarkImageHeightWidth / 2);
		generateHeader(examReportCardLayoutData.getDocumentLayoutSetup(), examReportData,
				examReportCardLayoutData, studentLite, institute, reportType, 20f,
				examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f, boldFont, regularFont, studentManager);
		generateStudentInformation(examReportCardLayoutData, studentLite, examReportData, boldFont, regularFont);
		generateBorderLayout(examReportCardLayoutData, pageNumber);
	}

	@Override
	public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
		return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
				officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
	}

	protected void generateHeader(DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
			Institute institute, String reportType, float offsetX, float offsetY,
								  PdfFont boldFont, PdfFont regularFont,  StudentManager studentManager) throws IOException {

		generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);
        
        byte[] studentImage = getStudentImage(institute.getInstituteId(), studentLite.getStudentId(), studentManager);
        if (studentImage == null) {
			studentImage = ImageProvider.INSTANCE.getImage(ImageProvider.IMAGE_FRAME_WITH_TEXT_2);
        }
		 generateImage(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), studentImage, LOGO_WIDTH, LOGO_HEIGHT,
                    examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getWidth() - offsetX - LOGO_WIDTH,
                    offsetY);

		int singleContentColumn = 1;
		Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize()).setTextAlignment(TextAlignment.CENTER);
		Color blueTextColor = WebColors.getRGBColor(EColorUtils.BLUE_COLOR_HEX_CODE_2);
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName().toUpperCase()).setFontColor(blueTextColor)),
						cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() + 18));
		String letterHeadline = institute.getLetterHeadLine1();
		String splitPoint = "Vill.: Soyla";
		int index = letterHeadline.indexOf(splitPoint);
		String letterHeadline1, letterHeadline12;
		if (index != -1) {
			letterHeadline1 = letterHeadline.substring(0, index).trim();
			letterHeadline12 = letterHeadline.substring(index).trim();
		} else {
			letterHeadline1 = letterHeadline;
			letterHeadline12 = "";
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadline1)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
		if(!StringUtils.isBlank(letterHeadline12)){
			addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(letterHeadline12)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1));
		}
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize() - 1).setPdfFont(regularFont));

		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 1);
		table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);
		String headerExamTitle = reportType.equalsIgnoreCase(TERM_I_REPORT_TYPE) ? "First Term EXAMINATION "
				: "ANNUAL EXAMINATION ";
        String headerText = examReportData.getExamReportStructure().getExamReportStructureMetaData().getReportCardHeaderName();
        if(!StringUtils.isBlank(headerText)){
            headerExamTitle = headerText;
        }
		headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
		addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline().setMultipliedLeading(0.8f)),
				cellLayoutSetup.copy().setFontSize(examReportCardLayoutData.getContentFontSize()).setPdfFont(boldFont));
		examReportCardLayoutData.getDocument().add(table);
		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
	}

	protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
			StudentLite studentLite, ExamReportData examReportData,
											  PdfFont boldFont, PdfFont regularFont) throws IOException {

		Document document = examReportCardLayoutData.getDocument();
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		float contentFontSize = examReportCardLayoutData.getContentFontSize() - 1;

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

		CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
		CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
		
		String fatherNameString = StringUtils.isBlank(studentLite.getFathersName()) ? "": "Mr."+studentLite.getFathersName();
		String motherNameString = StringUtils.isBlank(studentLite.getMothersName()) ? "": "Mrs."+studentLite.getMothersName();
		Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), boldFont, boldFont);
		Paragraph fatherName = getKeyValueParagraph("Father Name : ", fatherNameString, boldFont, boldFont);
		Paragraph dob = getKeyValueParagraph("DOB : ",
				studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
						: DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE),  boldFont, boldFont);
		Paragraph motherName = getKeyValueParagraph("Mother Name : ", motherNameString, boldFont, boldFont);
		Paragraph admissionNumber = getKeyValueParagraph("S.R. No. : ", studentLite.getAdmissionNumber(), boldFont, boldFont);
		Paragraph classValue = getKeyValueParagraph("Class : ",
				RomanNumberUtils.standardNameToRoman(studentLite.getStudentSessionData().getStandardNameWithSection()), boldFont, boldFont);
		Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), boldFont, boldFont);
		/* Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				boldFont, boldFont); */
        Paragraph house = getKeyValueParagraph("House : ",
				studentLite.getInstituteHouse() == null ? " " : studentLite.getInstituteHouse().getHouseName(),
				boldFont, boldFont);

        Paragraph address = getKeyValueParagraph("Address : ", studentLite.getStudentFullAddress(), boldFont, boldFont);
        


		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(fatherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(motherName, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(classValue, thirdCellLayoutSetup)));
		addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
				new CellData(house, thirdCellLayoutSetup)));

		document.add(table);

        table = getPDFTable(documentLayoutSetup, 1);
        addRow(table, documentLayoutSetup,
				Arrays.asList(new CellData(address, firstCellLayoutSetup)));       
        
        document.add(table);

		addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
	}

	protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
		DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
		PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
		canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
				documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
				documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
		canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
				documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
				documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
		canvas.stroke();
		canvas.setLineWidth(1f);
	}

	protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont, String descriptionText) throws IOException {
		Text descTitle = new Text("Description: " + descriptionText).setFontSize(contentFontSize - 4);
		Paragraph desc = new Paragraph();
		desc.add(descTitle);
		document.add(desc);
	}

	protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, ExamReportData examReportData, String reportType, PdfFont boldFont, PdfFont regularFont, int totalStudents) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize)
				.setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

		Table table = getPDFTable(documentLayoutSetup, new float[] { 0.4f, 0.2f, 0.4f });
		Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date : ",
				examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
				boldFont, boldFont);
		Paragraph conduct = getKeyValueParagraph("Conduct : ", "Good", boldFont, boldFont);
		List<Integer> greenColorRGB = EColorUtils.hex2Rgb(EColorUtils.LIGHT_GREEN_COLOR_HEX_CODE);
		List<Integer> redColorRGB = EColorUtils.hex2Rgb(EColorUtils.LIGHT_RED_COLOR_HEX_CODE);
		List<Integer> blackColorRGB = EColorUtils.hex2Rgb(EColorUtils.BLACK_COLOR_HEX_CODE);
		List<Integer> resultTextColor = examReportData.getExamResultStatus() == ExamResultStatus.PASS ? greenColorRGB : examReportData.getExamResultStatus() == ExamResultStatus.FAIL ? redColorRGB : null;
		String resultString = StringUtils.isBlank(examReportData.getExamResultStatus().getDisplayName()) ? "-" : examReportData.getExamResultStatus().getDisplayName();
		Paragraph result = getKeyValueParagraph("Result : ", resultString, blackColorRGB, resultTextColor, boldFont, boldFont);

		Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
				examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), boldFont, boldFont);
		Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
				examReportData.getTotalObtainedMarks() == null ? "-"
						: examReportData.getTotalObtainedMarks() * 10 % 10 == 0
								? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
								: String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), boldFont, boldFont);
		Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
				: String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), boldFont, boldFont);
		Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
				: Math.round(examReportData.getPercentage() * 100) / 100d + "%", boldFont, boldFont);
		Paragraph grade = getKeyValueParagraph("Grade : ",
				examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), boldFont, boldFont);
		String totalWorkingDaysString =examReportData.getTotalWorkingDays() == null ? "-"
						: String.valueOf(examReportData.getTotalWorkingDays());
		String totalattendedDaysString = examReportData.getTotalAttendedDays() == null ? "-"
						: String.valueOf(examReportData.getTotalAttendedDays());
		Paragraph attendance = getKeyValueParagraph("Attendance: ", totalattendedDaysString+"/"+totalWorkingDaysString, boldFont, boldFont);
		String rankString = examReportData.getRank() == null ? "-" : examReportData.getRank().toString()+"/"+String.valueOf(totalStudents);
		Paragraph rank = getKeyValueParagraph("Rank : ", rankString, boldFont, boldFont);

		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE) || reportType.equalsIgnoreCase(TERM_II_REPORT_TYPE)) {
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
		}
		else{
			addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
					new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(EMPTY_TEXT, cellLayoutSetup)));
		}

		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(attendance, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(rank, cellLayoutSetup)));
		addRow(table, documentLayoutSetup, Arrays.asList(new CellData(conduct, cellLayoutSetup),
				new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(dateOfResultDeclaration, cellLayoutSetup)));

		document.add(table);

	}

	protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
			ExamReportData examReportData, PdfFont boldFont, PdfFont regularFont) throws IOException {

		CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
		cellLayoutSetup.setPdfFont(boldFont).setFontSize(examReportCardLayoutData.getContentFontSize() - 1)
				.setTextAlignment(TextAlignment.LEFT);

		Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(),  1);

		Paragraph remarks = getKeyValueParagraph("Remarks: ",
				StringUtils.isBlank(examReportData.getRemarks()) ? "" :
						 examReportData.getRemarks());
		
		addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(new CellData(remarks, cellLayoutSetup)));

		examReportCardLayoutData.getDocument().add(remarksTable);
	}

	protected void generateSignatureBox(Document document, int instituteId, DocumentLayoutSetup documentLayoutSetup,
			float contentFontSize, float defaultBorderWidth, int pageNumber, PdfFont boldFont, PdfFont regularFont,
										String reportType, StudentManager studentManager, ExamReportData examReportData, float yAxisExtraSpace) throws IOException {
		PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
		List<ExamGrade> coScholasticGrades = new ArrayList<>();
		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);
		}

		boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
		boolean hasCoScholastic = !CollectionUtils.isEmpty(coScholasticGrades);
		float yAxis = 10f;
		if (hasScholastic || hasCoScholastic) {
			yAxis = hasScholastic && hasCoScholastic ? 150f : 94f;
			canvas.moveTo(12, yAxis + yAxisExtraSpace);
			canvas.lineTo(583, yAxis + yAxisExtraSpace);
			canvas.setLineWidth(.5f);
			canvas.closePathStroke();
		}
		
		int singleContentColumn = 3;
		Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);
		if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph(EMPTY_TEXT),
				getParagraph("Principal")), signatureCellLayoutSetup);
		}else{
			addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Parent"),
				getParagraph("Principal")), signatureCellLayoutSetup);
		}
		
		table.setFixedPosition(30f, yAxis + yAxisExtraSpace, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(table);

		byte[] principleSignatureImage = getStaffSignature(instituteId, studentManager);

		if (principleSignatureImage != null) {
			generateImage(document, documentLayoutSetup, principleSignatureImage,
					50f, 50f, documentLayoutSetup.getPageSize().getWidth() - 130f, yAxis + 30f);
		}

		generateGradeBox(document, documentLayoutSetup, examReportData, contentFontSize, defaultBorderWidth, boldFont, regularFont);
	}

	protected void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, ExamReportData examReportData, float contentFontSize,
                               float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont) throws IOException {

		Map<CourseType, List<ExamGrade>> gradesList = examReportData.getCourseTypeExamGrades();
		List<ExamGrade> scholasticGrades = gradesList.get(CourseType.SCHOLASTIC);
		List<ExamGrade> coScholasticGrades = new ArrayList<>();
		if (examReportData.getCourseTypeExamReportMarksGrid().containsKey(CourseType.COSCHOLASTIC)) {
			coScholasticGrades = gradesList.get(CourseType.COSCHOLASTIC);
		}

		boolean hasScholastic = !CollectionUtils.isEmpty(scholasticGrades);
		boolean hasCoScholastic = !CollectionUtils.isEmpty(coScholasticGrades);

		if (!hasScholastic && !hasCoScholastic) {
			return;
		}

		float yAxisForMarksRange = hasScholastic && hasCoScholastic ? 130f : 74f;
		float yOffsetScholastic = hasCoScholastic ? 109f : 53f;

		// Add "PERCENTAGE RANGE" heading table
		Table titleTable = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
		signatureCellLayoutSetup.setPdfFont(boldFont)
				.setFontSize(contentFontSize - 1)
				.setTextAlignment(TextAlignment.CENTER);
		addRow(titleTable, documentLayoutSetup, Arrays.asList(getParagraph("PERCENTAGE RANGE")), signatureCellLayoutSetup);
		titleTable.setFixedPosition(10f, yAxisForMarksRange, documentLayoutSetup.getPageSize().getWidth());
		document.add(titleTable);

		// Common cell layout for grades
		CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
		marksCellLayoutSetup.setPdfFont(regularFont)
				.setFontSize(contentFontSize - 2)
				.setBorder(new SolidBorder(defaultBorderWidth))
				.setTextAlignment(TextAlignment.CENTER);

		// Generate Scholastic table if available
		if (hasScholastic) {
			generateGradeTable(document, documentLayoutSetup, "Scholastic Grading", scholasticGrades, marksCellLayoutSetup,
					contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffsetScholastic, CourseType.SCHOLASTIC);
		}

		// Generate Co-Scholastic table if available
		if (hasCoScholastic) {
			float yOffsetCoScholastic = hasScholastic ? 57f : 53f;
			generateGradeTable(document, documentLayoutSetup, "Co-Scholastic Grading", coScholasticGrades, marksCellLayoutSetup,
					contentFontSize, defaultBorderWidth, boldFont, regularFont, yOffsetCoScholastic, CourseType.COSCHOLASTIC);
		}
	}

	private void generateGradeTable(Document document, DocumentLayoutSetup documentLayoutSetup, String title, List<ExamGrade> grades, CellLayoutSetup marksCellLayoutSetup,
									float contentFontSize, float defaultBorderWidth, PdfFont boldFont, PdfFont regularFont, float yPosition, CourseType courseType) throws IOException {

		// Section heading
		Table titleTable = getPDFTable(documentLayoutSetup, 1);
		CellLayoutSetup titleHeadLayout = new CellLayoutSetup();
		titleHeadLayout.setPdfFont(boldFont)
				.setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER);

		if (courseType == CourseType.SCHOLASTIC) {
			titleHeadLayout.setBorderTop(new SolidBorder(defaultBorderWidth))
					.setBorderRight(new SolidBorder(defaultBorderWidth))
					.setBorderLeft(new SolidBorder(defaultBorderWidth));
		} else if (courseType == CourseType.COSCHOLASTIC) {
			titleHeadLayout.setBorderRight(new SolidBorder(defaultBorderWidth))
					.setBorderLeft(new SolidBorder(defaultBorderWidth));
		}

		addRow(titleTable, documentLayoutSetup, Arrays.asList(getParagraph(title)), titleHeadLayout);
		titleTable.setFixedPosition(25f, yPosition, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(titleTable);

		// Calculate dynamic column widths
		int columnCount = grades.size() + 1;
		float firstColumnWidth = 0.18f;
		float remainingWidth = 1f - firstColumnWidth;
		float columnWidth = remainingWidth / (columnCount - 1);
		float[] columnWidths = new float[columnCount];
		columnWidths[0] = firstColumnWidth;
		Arrays.fill(columnWidths, 1, columnWidths.length, columnWidth);

		Table gradeTable = getPDFTable(documentLayoutSetup, columnWidths);
		List<CellData> headerRow = new ArrayList<>();
		List<CellData> gradeRow = new ArrayList<>();

		CellLayoutSetup headerCellLayout = new CellLayoutSetup();
		headerCellLayout.setPdfFont(regularFont)
				.setFontSize(contentFontSize - 2)
				.setTextAlignment(TextAlignment.CENTER)
				.setBorder(new SolidBorder(defaultBorderWidth));

		// Header row
		headerRow.add(new CellData("PERCENTAGE RANGE", headerCellLayout));
		gradeRow.add(new CellData("GRADE", marksCellLayoutSetup));

		for (ExamGrade grade : grades) {
			headerRow.add(new CellData(grade.getRangeDisplayName(), headerCellLayout));
			gradeRow.add(new CellData(grade.getGradeName(), marksCellLayoutSetup));
		}

		addRow(gradeTable, documentLayoutSetup, headerRow);
		addRow(gradeTable, documentLayoutSetup, gradeRow);

		gradeTable.setFixedPosition(25f, yPosition - 37f, documentLayoutSetup.getPageSize().getWidth() - 100f);
		document.add(gradeTable);
	}


	protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
		Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
		for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
				.get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
			if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
				nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
			}
		}
		return nonAdditionalSubjects;
	}

	protected byte[] getStaffSignature(int instituteId, StudentManager studentManager) throws IOException {
		if(org.apache.commons.lang.StringUtils.isEmpty(PRINCIPAL_UUID)){
			return null;
		}
		return getStudentSchoolPrincipalSignature(instituteId, UUID.fromString(PRINCIPAL_UUID), studentManager);
	}

	private void generateBackgroundImage(Document document, DocumentLayoutSetup documentLayoutSetup, byte[] image, float width,
										 float height, float offsetX, float offsetY) {
		try {
			generateImage(document, documentLayoutSetup, image, width, height, offsetX, offsetY);
		} catch (Exception e) {
			logger.error("Exception while adding background image", e);
		}

	}

	protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
		Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

			@Override
			public int compare(ExamReportData e1, ExamReportData e2) {
				StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

				StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

				if (section1 != null && section2 != null) {
					int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
					if (sectionCompare != 0) {
						return sectionCompare;
					}
				}

				return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
			}
		});
	}

	@Override
	public DocumentOutput generateClassReport(Institute institute, String reportType,
													List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

			sortClassExamReports(examReportDataList);

			int pageNumber = 1;
			PdfFont regularFont = getCambriaFont();
			PdfFont boldFont = getCambriaBoldFont();
			for (ExamReportData examReportData : examReportDataList) {
				generateStudentReportCard(examReportCardLayoutData, institute, examReportData, studentManager, reportType,
						pageNumber, boldFont, regularFont);

				if (pageNumber != examReportDataList.size()) {
					examReportCardLayoutData.getDocument().add(new AreaBreak(AreaBreakType.NEXT_PAGE));
				}
				pageNumber++;
			}

			examReportCardLayoutData.getDocument().close();
			return documentOutput;

		} catch (IOException e) {
			e.printStackTrace();
		}

		return null;
	}

	@Override
	public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
													String documentName, StudentManager studentManager, int studentPerPage) {
		try {
			DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
			DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
			Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

			PdfFont regularFont = getRegularFont();
			PdfFont boldFont = getRegularBoldFont();

			CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
			marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
					.setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

			CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
					.setTextAlignment(TextAlignment.LEFT);

			String instituteName = institute.getInstituteName().toUpperCase();
			String sessionName = "";
			if(!CollectionUtils.isEmpty(examReportDataList)) {
				sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
			}
			generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
					sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

			sortClassExamReports(examReportDataList);
			int pageNumber = 1;
			for (ExamReportData examReportData : examReportDataList) {
				generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
				generateScholasticMarksGrid(document,
						documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH, examReportData, GridConfigs.forOnlyObtainedTotalRow(),
						"Scholastic Subjects", getScholasticMarksGridSubjectWidth(reportType),
						null, null);
				generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
				if (pageNumber % studentPerPage == 0) {
					document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
					generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
							marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
				} else {
					addBlankLine(document, false, 1);
				}
				pageNumber++;
			}
			document.close();
			return documentOutput;
		} catch (Exception e) {
			logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
					reportType, e);
		}
		return null;
	}
}
