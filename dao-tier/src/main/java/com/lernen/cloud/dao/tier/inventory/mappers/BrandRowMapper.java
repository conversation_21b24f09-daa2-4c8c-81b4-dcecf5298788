package com.lernen.cloud.dao.tier.inventory.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import com.lernen.cloud.core.api.inventory.BrandInfo;
import com.lernen.cloud.core.api.inventory.BrandRowData;

public class BrandRowMapper implements RowMapper<BrandRowData> {

	private static final String INSTITUTE_ID = "institute_id";
	private static final String BRAND_ID = "brand_id";
	private static final String BRAND_NAME = "brand_name";
	private static final String CATEGORY_ID = "category_id";
	private static final String CREATED_AT = "created";
	private static final String UPDATED_AT = "updated";

	public BrandRowData mapRow(ResultSet rs, int rowNum) throws SQLException {
		return new BrandRowData(rs.getInt(INSTITUTE_ID), UUID.fromString(rs.getString(BRAND_ID)),
				rs.getString(BRAND_NAME), rs.getInt(CATEGORY_ID), (int) (rs.getTimestamp(CREATED_AT).getTime() / 1000l),
				(int) (rs.getTimestamp(UPDATED_AT).getTime() / 1000l));
	}

	/**
	 * This function assumes that all rows corresponds to same institute
	 * 
	 * @param brandRows
	 * @return
	 */
	public static List<BrandInfo> getBrandDetails(List<BrandRowData> brandRows) {
		if (CollectionUtils.isEmpty(brandRows)) {
			return new ArrayList<BrandInfo>();
		}
		Map<UUID, BrandInfo> brands = new HashMap<>();
		for (BrandRowData brandRow : brandRows) {
			UUID brandId = brandRow.getBrandId();
			if (brands.containsKey(brandId)) {
				brands.get(brandId).getCategoryIds().add(brandRow.getCategoryId());
			} else {
				Set<Integer> categoryIds = new HashSet<>();
				categoryIds.add(brandRow.getCategoryId());
				brands.put(brandId, new BrandInfo(brandRow.getInstituteId(), brandId, brandRow.getBrandName(),
						categoryIds, brandRow.getCreatedAt(), brandRow.getUpdatedAt()));
			}
		}
		List<BrandInfo> result = new ArrayList<>(brands.values());
		Collections.sort(result);
		return result;
	}

}
